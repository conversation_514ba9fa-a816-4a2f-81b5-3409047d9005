name: production-manual-workflow

on:
  workflow_dispatch:

permissions:
  id-token: write
  contents: read

jobs:
  build_and_deploy:
    uses: Mstack-Chemicals/shared-workflows/.github/workflows/build_and_deploy_azure.yaml@main
    with:
      branch: ${{ github.event.inputs.branch }}
      service_name: agent-hub
      dockerfile_path: .
      values_file_path: chemstack/agent-hub/production.values.yaml
    secrets: inherit