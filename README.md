# Agent Hub

## Overview
Agent Hub is a powerful and flexible platform for orchestrating AI agents using AutoGen. It provides a comprehensive solution for agent management, tool integration, and workflow automation, enabling seamless coordination between multiple AI agents and tools.

## Features

- **Agent Registry**: Centralized management of available AI agents
- **Tool Registry**: Dynamic tool integration with MCP server support
- **Model Registry**: Flexible LLM model management
- **Agent Orchestration**: Intelligent workflow management with root agent coordination
- **Session Management**: Robust user session handling
- **RESTful API**: FastAPI-based interface for workflow and agent management

## Project Structure

```
agent-hub/
├── app/
│   ├── agents/             # Agent registry and agent implementations
│   ├── api/                # FastAPI routes and endpoints
│   ├── config/             # Configuration management
│   ├── models/             # Data models and schemas
│   ├── orchestration/      # Workflow and agent orchestration logic
│   ├── sessions/           # Session management implementation
│   ├── tools/              # Tool registry and tool implementations
│   └── utils/              # Utility functions and helpers
├── tests/                  # Test suite
├── docs/                   # Documentation
└── scripts/               # Utility scripts
```

## Prerequisites

- Python 3.11+
- FastAPI
- AutoGen
- PostgreSQL 14+ (for session management)
- Git

## Development Setup

```sh
# Clone the repository
git clone https://github.com/yourusername/agent-hub.git
cd agent-hub

# Create and activate virtual environment
python3 -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate

# Install dependencies
pip install -r requirements.txt

# For development dependencies
pip install -r requirements-dev.txt

# Run the application locally with hot reload
uvicorn app.main:app --reload
```

## Environment Configuration

Create a `.env` file in the root directory with the following configuration:

```env
ENV=local
DATABASE_URL="postgresql+asyncpg://<username>:<password>@<host>:<port>/<database>"

# MCP Server Configuration
MCP_SERVER_URL=<MCP_SERVER_URL>
MCP_API_KEY=<MCP_API_KEY>

# LLM Configuration
OPENAI_API_KEY=<OPENAI_API_KEY>
DEFAULT_MODEL=gpt-4

# Session Configuration
SESSION_TIMEOUT=3600
MAX_SESSIONS_PER_USER=5
```

## API Documentation

### Swagger UI
```
http://localhost:8000/docs
```

### ReDoc
```
http://localhost:8000/redoc
```

## Monitoring

### Health Checks
```
/health
/health/live
/health/ready
```

### Metrics
```
/metrics
```

## Testing
```bash
# Run all tests
pytest

# Run tests with coverage
pytest --cov=app tests/

# Run specific test file
pytest tests/agents/test_agent_registry.py

# Generate HTML coverage report
pytest --cov=app --cov-report=html tests/
```

## Git Workflow

This repository follows a **trunk-based development** approach. All changes are branched from and merged back into `main`.

### 1. Feature Development

```bash
git checkout main
git pull
git checkout -b feature/feature-name
# Make changes
git add .
git commit -m "feat: add feature description"
git push origin feature/feature-name
```

- Open a Pull Request (PR) from `feature/feature-name` into `main`
- After review and approval, merge the PR
- Optionally, clean up the branch locally:

```bash
git checkout main
git pull
git branch -d feature/feature-name
```

### 2. Hotfix

```bash
git checkout main
git pull
git checkout -b hotfix/fix-description
# Make fixes
git add .
git commit -m "fix: short description of fix"
git push origin hotfix/fix-description
```

## Deployment
```bash
# Build the Docker image
docker build -t agent-hub:latest .

# Run the container
docker run -d \
    --name agent-hub \
    -p 8000:8000 \
    -e ENV=production \
    -e DATABASE_URL=postgresql+asyncpg://user:password@db:5432/agent_hub \
    agent-hub:latest
```

## TODO
1. Implement caching for agent responses
2. Add comprehensive logging
3. Implement rate limiting
4. Add agent performance metrics
5. Implement agent versioning
6. Add agent backup and restore functionality

## Contributing
1. Fork the repository
2. Create your feature branch from `main`
3. Commit your changes with descriptive commit messages
4. Push to your branch
5. Create a Pull Request

## License
This project is licensed under the MIT License - see the LICENSE file for details.
