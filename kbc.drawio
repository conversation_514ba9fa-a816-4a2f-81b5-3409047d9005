<mxfile host="app.diagrams.net" agent="Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36" version="27.1.6">
  <diagram id="healthcare-system" name="Healthcare Processing System">
    <mxGraphModel dx="3725" dy="1304" grid="0" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="1654" pageHeight="1169" math="0" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />
        <mxCell id="ui" value="User Interface" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#5DADE2;strokeColor=#2E86AB;fontColor=#FFFFFF;fontSize=12;fontStyle=1" parent="1" vertex="1">
          <mxGeometry x="-330" y="280" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="bPQhtgidLjxVrQNLIv1w-29" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" target="redis">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="400" y="320" as="sourcePoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="bPQhtgidLjxVrQNLIv1w-30" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" target="patient_queue">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="400" y="320" as="sourcePoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="redis" value="Redis Queue&lt;br&gt;(Input)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#8D6E63;strokeColor=#5D4037;fontColor=#FFFFFF;fontSize=12;fontStyle=1" parent="1" vertex="1">
          <mxGeometry x="480" y="190" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="patient_queue" value="Patient Queue&lt;br&gt;(input)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#8D6E63;strokeColor=#5D4037;fontColor=#FFFFFF;fontSize=12;fontStyle=1" parent="1" vertex="1">
          <mxGeometry x="480" y="370" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="status_queue" value="Status Queue" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#8D6E63;strokeColor=#5D4037;fontColor=#FFFFFF;fontSize=12;fontStyle=1" parent="1" vertex="1">
          <mxGeometry x="70" y="1090" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="celery" value="Celery Worker" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#689F38;strokeColor=#33691E;fontColor=#FFFFFF;fontSize=12;fontStyle=1" parent="1" vertex="1">
          <mxGeometry x="270" y="1090" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="azure" value="Azure AI" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#2196F3;strokeColor=#0D47A1;fontColor=#FFFFFF;fontSize=12;fontStyle=1" parent="1" vertex="1">
          <mxGeometry x="70" y="1210" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="retrieve_data" value="Retrieve Data&#xa;from MongoDB" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#424242;strokeColor=#212121;fontColor=#FFFFFF;fontSize=10" parent="1" vertex="1">
          <mxGeometry x="470" y="1090" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="update_status" value="Update Status&#xa;and Data" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#4CAF50;strokeColor=#2E7D32;fontColor=#FFFFFF;fontSize=10" parent="1" vertex="1">
          <mxGeometry x="470" y="1210" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="retro_module" value="RETRO RUNNER MODULE" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#6A1B9A;strokeColor=#4A148C;fontColor=#FFFFFF;fontSize=10;fontStyle=1" parent="1" vertex="1">
          <mxGeometry x="730" y="140" width="600" height="30" as="geometry" />
        </mxCell>
        <mxCell id="run_retro" value="Run Retro&#xa;Synthesis" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#424242;strokeColor=#212121;fontColor=#FFFFFF;fontSize=10" parent="1" vertex="1">
          <mxGeometry x="890" y="190" width="100" height="60" as="geometry" />
        </mxCell>
        <mxCell id="bPQhtgidLjxVrQNLIv1w-5" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" source="store_route" target="bPQhtgidLjxVrQNLIv1w-4">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="1080" y="290" />
              <mxPoint x="1570" y="290" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="store_route" value="Store Route&#xa;Data in&#xa;MongoDB" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#424242;strokeColor=#212121;fontColor=#FFFFFF;fontSize=10" parent="1" vertex="1">
          <mxGeometry x="1030" y="190" width="100" height="60" as="geometry" />
        </mxCell>
        <mxCell id="bPQhtgidLjxVrQNLIv1w-3" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" source="send_success" target="bPQhtgidLjxVrQNLIv1w-2">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="bPQhtgidLjxVrQNLIv1w-27" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="1" target="bPQhtgidLjxVrQNLIv1w-25">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="1400" y="190" as="sourcePoint" />
            <mxPoint x="1790" y="660" as="targetPoint" />
            <Array as="points">
              <mxPoint x="1400" y="180" />
              <mxPoint x="1670" y="180" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="send_success" value="Send Success&#xa;Response to&#xa;Status Queue" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#424242;strokeColor=#212121;fontColor=#FFFFFF;fontSize=10" parent="1" vertex="1">
          <mxGeometry x="1170" y="190" width="100" height="60" as="geometry" />
        </mxCell>
        <mxCell id="patient_module" value="PATIENT PROCESSING MODULE" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#6A1B9A;strokeColor=#4A148C;fontColor=#FFFFFF;fontSize=10;fontStyle=1" parent="1" vertex="1">
          <mxGeometry x="730" y="320" width="600" height="30" as="geometry" />
        </mxCell>
        <mxCell id="process_patient" value="Process Patient&#xa;Data" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#424242;strokeColor=#212121;fontColor=#FFFFFF;fontSize=10" parent="1" vertex="1">
          <mxGeometry x="890" y="370" width="100" height="60" as="geometry" />
        </mxCell>
        <mxCell id="bPQhtgidLjxVrQNLIv1w-15" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="1" source="store_patient" target="bPQhtgidLjxVrQNLIv1w-14">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="store_patient" value="Store Patient&#xa;Data in Azure AI" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#2196F3;strokeColor=#0D47A1;fontColor=#FFFFFF;fontSize=10" parent="1" vertex="1">
          <mxGeometry x="1030" y="370" width="100" height="60" as="geometry" />
        </mxCell>
        <mxCell id="bPQhtgidLjxVrQNLIv1w-28" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="1" source="send_patient_success" target="bPQhtgidLjxVrQNLIv1w-26">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="send_patient_success" value="Send Patient&#xa;Success&#xa;Response" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#424242;strokeColor=#212121;fontColor=#FFFFFF;fontSize=10" parent="1" vertex="1">
          <mxGeometry x="1170" y="370" width="100" height="60" as="geometry" />
        </mxCell>
        <mxCell id="patient_complete" value="Patient&#xa;Processing&#xa;Complete" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#4CAF50;strokeColor=#2E7D32;fontColor=#FFFFFF;fontSize=10" parent="1" vertex="1">
          <mxGeometry x="1350" y="370" width="100" height="60" as="geometry" />
        </mxCell>
        <mxCell id="conn6" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" parent="1" target="run_retro" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="850" y="220" as="sourcePoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="conn7" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" parent="1" source="run_retro" target="store_route" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="conn8" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" parent="1" source="store_route" target="send_success" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="conn10" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" parent="1" target="process_patient" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="850" y="400" as="sourcePoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="conn11" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" parent="1" source="process_patient" target="store_patient" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="conn12" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" parent="1" source="store_patient" target="send_patient_success" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="conn13" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" parent="1" source="send_patient_success" target="patient_complete" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="conn15" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="1" source="retrieve_data" target="update_status" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="conn16" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=1;entryY=0.5;entryDx=0;entryDy=0;" parent="1" source="celery" target="status_queue" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="conn17" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="1" source="status_queue" target="azure" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="bPQhtgidLjxVrQNLIv1w-2" value="Patient&#xa;Processing&#xa;Complete" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#4CAF50;strokeColor=#2E7D32;fontColor=#FFFFFF;fontSize=10" vertex="1" parent="1">
          <mxGeometry x="1350" y="190" width="100" height="60" as="geometry" />
        </mxCell>
        <mxCell id="bPQhtgidLjxVrQNLIv1w-4" value="CosmosDB" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#4CAF50;strokeColor=#2E7D32;fontColor=#FFFFFF;fontSize=12;fontStyle=1" vertex="1" parent="1">
          <mxGeometry x="1510" y="530" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="bPQhtgidLjxVrQNLIv1w-9" value="Celery Consumer" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#689F38;strokeColor=#33691E;fontColor=#FFFFFF;fontSize=12;fontStyle=1" vertex="1" parent="1">
          <mxGeometry x="1400" y="710" width="120" height="70" as="geometry" />
        </mxCell>
        <mxCell id="bPQhtgidLjxVrQNLIv1w-13" value="MongoDB" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#4CAF50;strokeColor=#2E7D32;fontColor=#FFFFFF;fontSize=12;fontStyle=1" vertex="1" parent="1">
          <mxGeometry x="1020" y="1230" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="bPQhtgidLjxVrQNLIv1w-14" value="Azure AI" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#2196F3;strokeColor=#0D47A1;fontColor=#FFFFFF;fontSize=12;fontStyle=1" vertex="1" parent="1">
          <mxGeometry x="1020" y="530" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="bPQhtgidLjxVrQNLIv1w-18" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=1;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" source="bPQhtgidLjxVrQNLIv1w-16" target="redis">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="bPQhtgidLjxVrQNLIv1w-19" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" source="bPQhtgidLjxVrQNLIv1w-16" target="run_retro">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="bPQhtgidLjxVrQNLIv1w-16" value="Celery Worker&lt;br&gt;(Retro)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#689F38;strokeColor=#33691E;fontColor=#FFFFFF;fontSize=12;fontStyle=1" vertex="1" parent="1">
          <mxGeometry x="680" y="190" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="bPQhtgidLjxVrQNLIv1w-17" value="Fetch Request&#xa;from Redis" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#424242;strokeColor=#212121;fontColor=#FFFFFF;fontSize=10" vertex="1" parent="1">
          <mxGeometry x="1290" y="1210" width="100" height="60" as="geometry" />
        </mxCell>
        <mxCell id="bPQhtgidLjxVrQNLIv1w-23" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=1;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" source="bPQhtgidLjxVrQNLIv1w-20" target="patient_queue">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="bPQhtgidLjxVrQNLIv1w-24" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" source="bPQhtgidLjxVrQNLIv1w-20" target="process_patient">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="bPQhtgidLjxVrQNLIv1w-20" value="Celery Worker&lt;br&gt;(Patent)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#689F38;strokeColor=#33691E;fontColor=#FFFFFF;fontSize=12;fontStyle=1" vertex="1" parent="1">
          <mxGeometry x="690" y="370" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="bPQhtgidLjxVrQNLIv1w-21" value="Fetch Patient&#xa;Request from&#xa;Patient Queue" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#424242;strokeColor=#212121;fontColor=#FFFFFF;fontSize=10" vertex="1" parent="1">
          <mxGeometry x="1540" y="1120" width="100" height="60" as="geometry" />
        </mxCell>
        <mxCell id="bPQhtgidLjxVrQNLIv1w-22" value="Fetch Patient&#xa;Request from&#xa;Patient Queue" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#424242;strokeColor=#212121;fontColor=#FFFFFF;fontSize=10" vertex="1" parent="1">
          <mxGeometry x="780" y="1230" width="100" height="60" as="geometry" />
        </mxCell>
        <mxCell id="bPQhtgidLjxVrQNLIv1w-32" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=1;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" source="bPQhtgidLjxVrQNLIv1w-25" target="bPQhtgidLjxVrQNLIv1w-9">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="bPQhtgidLjxVrQNLIv1w-25" value="Redis Queue&lt;br&gt;(Output)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#8D6E63;strokeColor=#5D4037;fontColor=#FFFFFF;fontSize=12;fontStyle=1" vertex="1" parent="1">
          <mxGeometry x="1773" y="656" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="bPQhtgidLjxVrQNLIv1w-31" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" source="bPQhtgidLjxVrQNLIv1w-26" target="bPQhtgidLjxVrQNLIv1w-9">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="bPQhtgidLjxVrQNLIv1w-26" value="Patient Queue&lt;br&gt;(Output)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#8D6E63;strokeColor=#5D4037;fontColor=#FFFFFF;fontSize=12;fontStyle=1" vertex="1" parent="1">
          <mxGeometry x="1160" y="630" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="bPQhtgidLjxVrQNLIv1w-39" value="" style="rounded=0;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="40" y="20" width="360" height="540" as="geometry" />
        </mxCell>
        <mxCell id="bPQhtgidLjxVrQNLIv1w-34" value="Project Module" style="rounded=1;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="90" y="90" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="bPQhtgidLjxVrQNLIv1w-35" value="Patent Module" style="rounded=1;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="90" y="190" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="bPQhtgidLjxVrQNLIv1w-36" value="AI Retro Module" style="rounded=1;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="90" y="280" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="bPQhtgidLjxVrQNLIv1w-37" value="Workspace Module" style="rounded=1;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="90" y="380" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="bPQhtgidLjxVrQNLIv1w-40" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.477;entryY=1.006;entryDx=0;entryDy=0;entryPerimeter=0;" edge="1" parent="1" source="bPQhtgidLjxVrQNLIv1w-9" target="bPQhtgidLjxVrQNLIv1w-39">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="1460" y="820" />
              <mxPoint x="212" y="820" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="bPQhtgidLjxVrQNLIv1w-41" value="&lt;b&gt;&lt;font style=&quot;font-size: 16px;&quot;&gt;ChemStack API&lt;/font&gt;&lt;/b&gt;" style="text;html=1;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;" vertex="1" parent="1">
          <mxGeometry x="140" y="30" width="140" height="50" as="geometry" />
        </mxCell>
        <mxCell id="bPQhtgidLjxVrQNLIv1w-43" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.006;entryY=0.538;entryDx=0;entryDy=0;entryPerimeter=0;" edge="1" parent="1" source="ui" target="bPQhtgidLjxVrQNLIv1w-39">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="bPQhtgidLjxVrQNLIv1w-44" value="Celery Producer" style="rounded=1;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="250" y="280" width="120" height="60" as="geometry" />
        </mxCell>
      </root>
    </mxGraphModel>
  </diagram>
</mxfile>
