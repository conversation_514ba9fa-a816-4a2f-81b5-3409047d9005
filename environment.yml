name: agent-hub-env
channels:
  - conda-forge
  - pytorch # For PyTorch from Conda if preferred
  - defaults
dependencies:
  # --- Core Python ---
  - python=3.10.*

  # --- Conda-managed packages (preferred for these) ---
  - rdkit # RDKit from conda-forge
  # PyTorch from Conda (CPU version by default, adjust channel/package for GPU)
  # Example: For PyTorch 2.1.x CPU
  - pytorch::pytorch=2.1.* 
  - pytorch::torchvision # Usually needed with torch
  - pytorch::torchaudio  # Usually needed with torch
  # If you need a specific CUDA version via conda, e.g., for CUDA 11.8:
  # - pytorch::pytorch-cuda=11.8 
  # - pytorch::pytorch=2.1.* # Ensure version matches
  # - pytorch::torchvision
  # - pytorch::torchaudio

  # --- Common data science & utility packages via Conda ---
  - pandas>=2.1.0
  - pyarrow>=14.0.0
  - requests>=2.31.0
  - pip # To install pip-only packages

  # --- Pip-managed packages (installed after Conda ones) ---
  - pip:
    - fastapi>=0.110.0
    - uvicorn[standard]>=0.29.0
    - pydantic>=2.5.0
    - pydantic-settings>=2.1.0
    - python-dotenv>=1.0.0
    - openai>=1.10.0
    - pyautogen[retrievechat]>=0.2.20
    # Transformers and related - versions pinned for rxnmapper compatibility
    - transformers==4.40.0
    - tokenizers==0.19.1 # Compatible with transformers 4.40.0
    - sentencepiece>=0.2.0
    - rxnmapper==0.4.2  # <--- CHANGED THIS LINE (or try ^0.4.0 or >=0.4.0)
    - pubchempy>=1.0.4
    # Development tools (optional for production environment.yml)
    - pytest>=7.4.0
    - pytest-asyncio>=0.21.0
    - httpx>=0.25.0
    - black>=23.10.0
    - ruff>=0.1.0
    - mypy>=1.6.0
    - pre-commit>=3.5.0