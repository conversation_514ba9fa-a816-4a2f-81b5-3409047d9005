# from app.utils.es_helper import get_search_client
# import json

# from app.config.settings import get_settings

# app_config = get_settings()
# es_client = get_search_client(
#     {
#                 "provider": app_config.SEARCH_PROVIDER,
#                 "endpoint": f"https://{app_config.AZURE_SEARCH_SERVICE_NAME}.search.windows.net",
#                 "index_name": app_config.AZURE_PATENT_SUMMARY_INDEX_NAME,
#                 "api_key": app_config.AZURE_SEARCH_API_KEY
#             }
# )

# rr = {
#             "id": "68320d44r-5322-4e50-8826-a838f7uc92e9",
#             "patent_id": "US20020049155B2",
#             "request_id": "8b048ebd-b091-4a6c-b43c-b23f74c397c3",
#             "chemical_name": "caffine",
#             "created_at": "2025-06-24T15:30:28.596603",
#             "summaryOfClaims": "The patent claims novel cobalamin (Vitamin B12) derivatives covalently linked, directly or via a linker, to one or more cardiovascular agents and/or imaging agents. These compounds are intended for the treatment, prophylaxis, or diagnosis of cardiovascular diseases by targeting transcobalamin or intrinsic factor receptors, which are found in or near cardiovascular vessels, especially those containing plaque. The claims also cover pharmaceutical compositions containing these conjugates and methods of using them for medical therapy, including parenteral administration and controlled release formulations.",
#             "reactionsInClaims": "Key Reactions:\n*   Covalent linking of a transcobalamin- or intrinsic factor-binding agent (cobalamin derivative) to a cardiovascular agent or an imaging agent (implied amide, ester, ether, or other linkages through various functional groups).\n*   Chelation of metallic radionuclides or paramagnetic metal atoms to form imaging agents.",
#             "tags": [
#                 "Cobalamin",
#                 "Conjugation",
#                 "Cardiovascular",
#                 "Therapeutics",
#                 "Diagnosis",
#                 "Pharmaceutical",
#                 "Drug-Delivery",
#                 "Medical"
#             ],
#             "claim_tags": [
#                 "Product",
#                 "Process",
#                 "Composition"
#             ],
#             "relevancyScore": 0.0,
#             "abstract": "The invention provides cobalamin derivatives linked to a cardiovascular agent, as well as pharmaceutical compositions comprising the compounds and methods for using the compounds in treatment or diagnosis of a cardiovascular disease.",
#             "title": "Cobalamin compounds useful as cardiovascular agents and as imaging agents",
#             "publication_date": "2002-04-25",
#             "filing_date": "2001-05-31",
#             "pdf_url": "https://patentimages.storage.googleapis.com/3c/46/2c/82c6321d517b8d/US20020049155A1.pdf",
#             "assignees": [
#                 "Individual"
#             ],
#             "inventors": [
#                 "Henricus Hogenkamp"
#             ],
#             "claims": [
#                 {
#                     "id": "Claim 1",
#                     "claim": "1. A compound of the formula I: \n      \n        \n          \n          \n             \n             \n          \n        \n      \n      or its pharmaceutically acceptable salt, wherein: \na) the wavy line in the chemical structure indicates either a dative or covalent bond such that there are three dative Co\u2014N bonds and one covalent Co\u2014N bond, wherein, in the case of the dative bond, the valence of nitrogen is completed either with a double bond with an adjacent ring carbon or with a hydrogen; \nb) the dotted line in the chemical structure indicates either a double or single bond such that the double bond does not over-extend the valence of the element (i.e. to give pentavalent carbons) and, in the case of a single bond, the valence is completed with hydrogen; \nc) X is hydrogen, cyano, halogen (Cl, F, Br or I), haloalkyl, CF3, CF2CF3, CH2CF3, CF2Cl, NO, NO2, NO3, phosphonate, alkyl-P(P)2OR15), PR15R16R17, N2, NR15R16, OH, OR15, SR15, SCN, N3, OC(O)R15, C(O)2R15, C(O)R15, OC(O)NR15R16, C(o)2NR15R16, C(o)N15R16, P(O)2OR15, S(O)2 OR15, a purine or pyrimidine nucleoside or nucleoside analog, adenosyl, 5-FU, alkyl, alkenyl, alkynyl, aryl, aralkyl, alkaryl, amino acid, peptide, protein, carbohydrate, heteroalkyl, heterocycle, heteroaryl, alkylheteroaryl or L-T; \nd) M is a monovalent heterocycle or heteroaromatic, which is capable of binding to the adjacent sugar ring; \ne) K is O, S, NJ1, C(OH)H, CR100R101 or C(R100)V8Z8; \nf) E is O or S; \ng) G1 is hydrogen, alkyl, acyl, silyl, mono-, di- or tri-phosphate or L-T; \nh) Y1, Y2, Y3, Y4, Y5, Y6 and Y7 independently are O, S or NJ2; direct bond; \nj) Z1, Z2, Z3, Z4, Z5, Z7 and Z8 independently are R104 or L-T; \nk) each L is independently a direct bond or a linker to one or more T moieties and that does not significantly impair the ability of the TC- or IF-binding agent to bind to a transcobalamin receptor; \nl) each T independently comprises a cardiovascular agent, or pharmaceutically acceptable residue thereof, optionally bound though a chelating moiety; \nm) wherein at least one of Z1, Z2, Z3, Z4, Z5, Z7, Z8 and G1 is L-T; \nn) J1, J2 and J3 independently are hydrogen, alkyl, alkenyl, alkynyl, alkaryl, cycloalkyl, aryl, cycloaryl, heteroalkyl, heterocycle, heteroaryl, hydroxyl, alkoxy or amine; \no) R1, R, R3, R4, R5, R6, R7, R8, R9, R10, R11, R12, R13 and R14 independently are hydrogen, lower alkyl, lower alkenyl, lower alkynyl, lower cycloalkyl, heteroalkyl, heterocyclic, lower alkoxy, azido, amino, lower alkylamino, halogen, thiol, SO2, SO3, carboxylic acid, C1-6 carboxyl, hydroxyl, nitro, cyano, oxime or hydrazine; \np) R13 and R14 optionally can form a double bond; \nq) R15, R16 and R17 are independently hydrogen, alkyl, alkenyl, alkynyl, aryl, alkaryl or aralkyl group, heteroalkyl, heterocycle or heteroaromatic; and \nr) R100, R101, R 102, R103 and R104 are independently hydrogen, alkyl, alkenyl, alkynyl, aryl, acyl, heteroaromatic, heteroaryl, heteroalkyl, hydroxyl, alkoxy, cyano, azido, halogen, nitro, SO2, SO3, thioalkyl or amino.",
#                     "tag": "Product",
#                     "dependent": False,
#                     "depends_on": ""
#                 },
#                 {
#                     "id": "Claim 2",
#                     "claim": "2. The compound of claim 1 wherein \na) X is CN, OH, CH3, adenosyl or L-T; \nb) M is 5,6-dimethylbenzimidazole; \nc) K is C(OH)H; \nd) E is O; \ne) G1 is hydrogen, alkyl, acyl, silyl, mono-, di- or tri-phosphate or L-T \nf) Y1, Y2, Y3, Y4, Y5, Y6 and Y7 are O \ng) V1, V2, V3, V4, V5, V6, V7 and V8 are independently NJ3; \nh) R1, R2, R4, R5, R8, R9, R11, R12 and R15 are independently methyl; \ni) R3, R6, R7, R10, R13 and R14 are independently hydrogen; and \nj) Z1, Z2, Z3, Z4, Z5, Z7 and Z8 are independently hydrogen or L-T.",
#                     "tag": "Product",
#                     "dependent": True,
#                     "depends_on": "Claim 1"
#                 },
#                 {
#                     "id": "Claim 3",
#                     "claim": "3. The compound of claim 1 wherein, M is a purine or pyrimidine.",
#                     "tag": "Product",
#                     "dependent": True,
#                     "depends_on": "Claim 1"
#                 }
#             ]
#         }
# es_client.insert_document(
#     document=rr
# )

# print(rr)


# import fitz  # PyMuPDF
# import re
# import os
# import pandas as pd

# def extract_amount_and_currency(text):
#     # Pattern: currency symbol + number or 3-letter currency + number
#     patterns = [
#         r'(\$|€|£|₹)\s?[\d,]+\.\d{2}',              # e.g. $1,000.00
#         r'(USD|EUR|INR|GBP)\s?[\d,]+\.\d{2}',       # e.g. USD 1000.00
#     ]
#     for pattern in patterns:
#         match = re.search(pattern, text)
#         if match:
#             return match.group()
#     return None

# def process_pdfs(filepath):
#     # results = []
#     # for filename in os.listdir(folder_path):
#     #     if filename.endswith(".pdf"):
#     #         filepath = os.path.join(folder_path, filename)
#     results = []
#     filename = os.path.basename(filepath)
#     doc = fitz.open(filepath)
#     text = ""
#     for page in doc:
#         text += page.get_text()
#     value = extract_amount_and_currency(text)
#     results.append({"file": filename, "total_value": value})
#     return pd.DataFrame(results)

# # Change to your actual path
# df = process_pdfs("/Users/<USER>/Downloads/PO-PS-009 kacid.PDF")
# df.to_csv("po_totals.csv", index=False)


import os
import pdfplumber
import pytesseract
from pdf2image import convert_from_path
import re
import pandas as pd
from PIL import Image

# Optional: If Tesseract is not in PATH
# pytesseract.pytesseract.tesseract_cmd = r'/usr/bin/tesseract'  # Adjust for your system

def is_digital_pdf(file_path):
    with pdfplumber.open(file_path) as pdf:
        for page in pdf.pages:
            text = page.extract_text()
            if text and len(text.strip()) > 10:
                return True
    return False

def extract_text_digital(file_path):
    with pdfplumber.open(file_path) as pdf:
        return "\n".join(page.extract_text() or "" for page in pdf.pages)

def extract_text_scanned(file_path):
    images = convert_from_path(file_path)
    text = ""
    for img in images:
        text += pytesseract.image_to_string(img)
    return text

import re

import re
def extract_all_text(file_path):
    if is_digital_pdf(file_path):
        print("→ Digital PDF detected.")
        full_text = ""
        with pdfplumber.open(file_path) as pdf:
            for page in pdf.pages:
                page_text = page.extract_text()
                full_text += page_text + "\n" if page_text else ""
        print(full_text)  # Print the extracted text for debugging
        return full_text
    else:
        print("→ Scanned PDF detected, using OCR...")
        pages = convert_from_path(file_path)
        full_text = ""
        for i, image in enumerate(pages):
            text = pytesseract.image_to_string(image)
            full_text += text + "\n"
        print(text)  # Print the OCR text for debugging
        return full_text
    
def extract_amount_and_currency(text):
    # Normalize text
    text = text.replace('\xa0', ' ')  # non-breaking spaces
    lines = text.splitlines()

    # Ignore irrelevant subtotal lines
    ignore_keywords = ['subtotal', 'tax', 'discount', 'shipping']

    # Regex to match amount with optional currency
    amount_pattern = re.compile(
        r'(?i)(total( amount)?( due)?|amount payable|grand total)[^\dA-Z\$€£₹]{0,10}([€$£₹]?\s?(?:USD|EUR|INR|GBP)?\s?\d{1,3}(?:,\d{3})*(?:\.\d{2})?)'
    )

    # Candidate list of matches
    candidates = []

    for line in lines:
        if any(kw in line.lower() for kw in ignore_keywords):
            continue
        match = amount_pattern.search(line)
        print(match)
        if match:
            raw_value = match.group(4).replace(',', '').strip()
            # Currency + Value parsing
            currency_match = re.match(r'([€$£₹]|USD|EUR|INR|GBP)?\s*(\d+(?:\.\d{2})?)', raw_value)
            if currency_match:
                currency = currency_match.group(1) or 'UNKNOWN'
                value = float(currency_match.group(2))
                candidates.append({
                    'currency': currency,
                    'value': value,
                    'text': line.strip()
                })

    # Strategy: Choose highest value assuming it's total
    if candidates:
        best = sorted(candidates, key=lambda x: -x['value'])[0]
        return {'currency': best['currency'], 'value': best['value']}
    
    return None



def process_pdfs(folder_path):
    results = []
    for filename in os.listdir(folder_path):
        if not filename.lower().endswith('.pdf'):
            continue
        file_path = os.path.join(folder_path, filename)
        try:
            extract_all_text(file_path)  # Ensure we can read the file
            if is_digital_pdf(file_path):
                text = extract_text_digital(file_path)
            else:
                text = extract_text_scanned(file_path)
            amount = extract_amount_and_currency(text)
            results.append({"filename": filename, "amount": amount})
        except Exception as e:
            results.append({"filename": filename, "amount": None, "error": str(e)})
    return results

# === Run it ===
folder = "pdfs"  # <- Change this
output_csv = "purchase_order_totals.csv"

data = process_pdfs(folder)
df = pd.DataFrame(data)
df.to_csv(output_csv, index=False)

print(f"Done. Results saved to {output_csv}")


curl -X GET "https://chemstack-ai-search.search.windows.net/indexes/patent_summary_data/docs?api-version=2023-07-01-Preview&search=*&$count=true&$top=0" \
  -H "api-key: 1q4Ebm2wdheUL80aOSr9KwKZpURtgTrP6PHZ5XIX1qAzSeCa08to" \
  -H "Content-Type: application/json"



curl -G "https://chemstack-ai-search.search.windows.net/indexes/patent_summary_data/docs" \
  -H "api-key: 1q4Ebm2wdheUL80aOSr9KwKZpURtgTrP6PHZ5XIX1qAzSeCa08to" \
  -H "Content-Type: application/json" \
  --data-urlencode "api-version=2023-07-01-Preview" \
  --data-urlencode "search=*" \
  --data-urlencode "$count=true" \
  --data-urlencode "$top=0"


curl -X GET "https://<your-search-service>.search.windows.net/indexes/<your-index-name>/docs?api-version=2023-07-01-Preview&search=*&$count=true" \
  -H "api-key: <your-admin-or-query-key>" \
  -H "Content-Type: application/json"




curl -X GET "https://chemstack-ai-search.search.windows.net/indexes/patent_data/docs?api-version=2023-07-01-Preview&%24count=true&%24filter=request_id%20eq%20'a518fe80-974d-44c7-9f93-6475518e2b7e'" \
  -H "api-key: 1q4Ebm2wdheUL80aOSr9KwKZpURtgTrP6PHZ5XIX1qAzSeCa08to" \
  -H "Content-Type: application/json"
