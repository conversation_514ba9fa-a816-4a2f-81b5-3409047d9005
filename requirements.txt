
ag2==0.9.2
annotated-types==0.7.0
anyio==4.9.0
asyncer==0.0.8
attrs==25.3.0
autogen==0.9.2
certifi==2025.4.26
charset-normalizer==3.4.2
diskcache==5.6.3
distro==1.9.0
dnspython==2.7.0
h11==0.16.0
hf-xet==1.1.3
httpcore==1.0.9
httpx==0.28.1
idna==3.10
Jinja2==3.1.6
jiter==0.10.0
MarkupSafe==3.0.2
mpmath==1.3.0
networkx==3.5
openai==1.85.0
packaging==25.0
pillow==11.2.1
pydantic==2.11.5
pydantic-settings==2.9.1
pydantic_core==2.33.2
python-dateutil==2.9.0.post0
python-dotenv==1.1.0
pytz==2025.2
PyYAML==6.0.2
regex==2024.11.6
requests==2.32.4
rxn-chem-utils==1.6.0
rxn-utils==2.0.0
rxnmapper==0.4.2
safetensors==0.5.3
scipy==1.15.3
setuptools==80.9.0
six==1.17.0
sniffio==1.3.1
starlette==0.46.2
sympy==1.14.0
termcolor==3.1.0
tiktoken==0.9.0
tokenizers==0.21.1
torch==2.7.1
tqdm==4.67.1
transformers==4.52.4
typing-inspection==0.4.1
typing_extensions==4.14.0
tzdata==2025.2
urllib3==2.4.0

fastapi>=0.104.0
uvicorn>=0.24.0
pydantic>=2.5.0
pydantic-settings>=2.0.0
click>=8.1.0
requests>=2.31.0
tqdm>=4.66.0

# AI/ML dependencies
google-genai>=0.3.0
openai>=1.3.0

# Chemical processing
rdkit>=2023.9.1

# Web scraping and APIs
serpapi>=0.1.5
google-search-results>=2.4.2
beautifulsoup4>=4.12.0

# Data processing
pandas>=2.1.0
numpy>=1.24.0

# Testing
pytest>=7.4.0
pytest-asyncio>=0.21.0
pytest-cov>=4.1.0

# Development
black>=23.0.0
flake8>=6.0.0
mypy>=1.7.0

# Logging and monitoring
structlog>=23.2.0

# Configuration
python-dotenv>=1.0.0

redis
celery
gunicorn
aiohttp
elasticsearch==8.18.1
pymongo
PubChemPy==1.0.4
azure-search-documents
azure-storage-blob
