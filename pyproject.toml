[tool.poetry]
name = "agent-hub"
version = "0.1.0"
description = "AGENT-HUB: A multi-agent system for chemical analysis and operations, featuring ChemCopilot capabilities."
authors = ["Your Name <<EMAIL>>"] # Replace with your details
readme = "README.md"
license = "MIT" # Or your preferred license

[tool.poetry.dependencies]
python = "^3.10"

# Core FastAPI and Web Server
fastapi = "^0.110.0"
uvicorn = {extras = ["standard"], version = "^0.29.0"}

# Configuration and Settings
pydantic = "^2.5.0" # Pydantic v2
pydantic-settings = "^2.1.0"
python-dotenv = "^1.0.0"

# Chemical Informatics & Data Handling
# For RDKit, if you primarily use Conda, this line in pyproject.toml is more of a placeholder
# or for users who might install it via pip (rdkit-pypi).
# Conda environment will handle RDKit installation separately.
# rdkit = "*" # If you want to declare it for pip-based environments.
#             # For now, let's assume Conda will provide it via environment.yml
pandas = "^2.1.0"
pyarrow = "^14.0.0" # For Parquet file support with pandas

# LLM and Agent Frameworks
openai = "^1.10.0"
pyautogen = {extras = ["retrievechat"], version = "^0.2.20"}

# Reaction Mapping & Deep Learning Dependencies
# These are tricky with Poetry due to PyTorch's complex installation.
# We'll specify versions known to work well together for rxnmapper.
# If `poetry install` fails on these, install them via pip *after* `poetry install --no-dev`.
transformers = "4.40.0" # Pinning to a specific version for rxnmapper
tokenizers = "0.19.1"   # Pinning to a specific version compatible with transformers 4.40.0
sentencepiece = "^0.2.0" # Or a more specific version if known
rxnmapper = "^2.2.0"     # Or the latest version you intend to use
torch = "^2.1.0" # Specify a base torch version; users might need to adjust for CPU/GPU/OS

# HTTP Requests
requests = "^2.31.0"

# PubChem API Client
pubchempy = "^1.0.4"


[tool.poetry.group.dev.dependencies]
pytest = "^7.4.0"
pytest-asyncio = "^0.21.0"
httpx = "^0.25.0"
black = "^23.10.0"
ruff = "^0.1.0"
mypy = "^1.6.0"
pre-commit = "^3.5.0"
# For Conda environment creation from lock file
poetry-plugin-export = "^1.6.0" # To export requirements.txt more easily

[build-system]
requires = ["poetry-core>=1.0.0"]
build-backend = "poetry.core.masonry.api"

[tool.ruff]
line-length = 120
select = ["E", "W", "F", "I", "UP", "C90", "N", "D", "S", "B", "A", "COM", "LOG", "T20", "PT", "Q", "RUF"]
ignore = ["E501"]

[tool.black]
line-length = 120

[tool.mypy]
python_version = "3.10"
warn_return_any = true
warn_unused_configs = true
ignore_missing_imports = true
# files = ["app"] # Uncomment and adjust if your source is in a subdirectory

[tool.pytest.ini_options]
pythonpath = ["."]
asyncio_mode = "auto"