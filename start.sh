#!/bin/bash

# Start FastAPI app in the background
celery -A app.worker worker --concurrency=1 --loglevel=info 2>&1 | sed 's/^/[celery] /' &

celery -A app.enrich_worker worker --loglevel=INFO --concurrency=5 --pool=prefork --without-gossip --without-mingle --without-heartbeat 2>&1 | sed 's/^/[enrich_worker] /' &

gunicorn app.main:app -k uvicorn.workers.UvicornWorker --timeout 480 -b 0.0.0.0:8000 2>&1 | sed 's/^/[api] /'

wait -n
