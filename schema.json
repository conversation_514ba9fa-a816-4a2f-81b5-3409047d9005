{"name": "patents-index", "fields": [{"name": "id", "type": "Edm.String", "key": true, "searchable": false}, {"name": "title", "type": "Edm.String", "searchable": true}, {"name": "abstract", "type": "Edm.String", "searchable": true}, {"name": "publication_number", "type": "Edm.String", "filterable": true, "facetable": true}, {"name": "application_number", "type": "Edm.String", "filterable": true}, {"name": "country", "type": "Edm.String", "filterable": true, "facetable": true}, {"name": "priority_date", "type": "Edm.DateTimeOffset", "filterable": true}, {"name": "filing_date", "type": "Edm.DateTimeOffset", "filterable": true}, {"name": "publication_date", "type": "Edm.DateTimeOffset", "filterable": true}, {"name": "inventors", "type": "Collection(Edm.String)", "searchable": true}, {"name": "assignees", "type": "Collection(Edm.String)", "searchable": true}, {"name": "pdf", "type": "Edm.String", "retrievable": true}, {"name": "claims", "type": "Collection(Edm.String)", "searchable": true}, {"name": "prior_art_keywords", "type": "Collection(Edm.String)", "searchable": true}, {"name": "classifications", "type": "Collection(Edm.ComplexType)", "fields": [{"name": "code", "type": "Edm.String"}, {"name": "description", "type": "Edm.String"}]}, {"name": "concepts", "type": "Edm.String", "searchable": true}, {"name": "description_link", "type": "Edm.String"}, {"name": "external_links", "type": "Collection(Edm.ComplexType)", "fields": [{"name": "text", "type": "Edm.String"}, {"name": "link", "type": "Edm.String"}]}, {"name": "events", "type": "Collection(Edm.ComplexType)", "fields": [{"name": "date", "type": "Edm.DateTimeOffset"}, {"name": "title", "type": "Edm.String"}, {"name": "type", "type": "Edm.String"}]}]}