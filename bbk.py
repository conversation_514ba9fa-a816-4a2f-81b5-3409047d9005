# import os
# import json
# import requests
# from azure.storage.blob import BlobServiceClient, BlobClient
# from tqdm import tqdm
# import threading
# from concurrent.futures import ThreadPoolExecutor, as_completed
# import time
# import logging

# # CONFIGURATION
# SEARCH_SERVICE = "mstack-reactions-search-service"  # no https://, just the name
# INDEX_NAME = "molecule_patents"
# API_VERSION = "2024-07-01"
# SEARCH_API_KEY = "t6p5Q0MRPOiWKVOvAc68Yexd0IuT9HKhlbzse3Mxe6AzSeDygi5E"

# STORAGE_CONNECTION_STRING = "DefaultEndpointsProtocol=https;AccountName=chemstackprodstorage;AccountKey=****************************************************************************************;EndpointSuffix=core.windows.net"
# CONTAINER_NAME = "patent-data"

# # Configuration
# DOCS_PER_FILE = 50000
# OUTPUT_DIR = "dumped_docs"
# NUM_THREADS = 10
# BATCH_SIZE = 2000  # Larger batches for better throughput
# UPLOAD_IMMEDIATELY = True
# DOCS_PER_PAGE = 1000

# # Thread-safe logging
# logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(threadName)s - %(message)s')
# logger = logging.getLogger(__name__)

# # Global variables
# blob_service_client = None
# container_client = None
# total_fetched = 0
# total_uploaded = 0
# fetch_lock = threading.Lock()
# upload_lock = threading.Lock()

# os.makedirs(OUTPUT_DIR, exist_ok=True)

# def get_total_document_count():
#     headers = {
#         "api-key": SEARCH_API_KEY,
#         "Content-Type": "application/json"
#     }
#     url = f"https://{SEARCH_SERVICE}.search.windows.net/indexes/{INDEX_NAME}/docs/$count?api-version={API_VERSION}"
#     response = requests.get(url, headers=headers)
#     response.raise_for_status()
#     return int(response.text)

# def fetch_batch(last_id: str, file_index: int):
#     headers = {
#         "api-key": SEARCH_API_KEY,
#         "Content-Type": "application/json"
#     }
#     url = f"https://{SEARCH_SERVICE}.search.windows.net/indexes/{INDEX_NAME}/docs/search?api-version={API_VERSION}"

#     # Construct filter if not the first page
#     filter_query = f"id gt '{last_id}'" if last_id else None

#     payload = {
#         "search": "*",
#         "top": DOCS_PER_PAGE,
#         "orderby": "id asc"
#     }
#     if filter_query:
#         payload["filter"] = filter_query

#     response = requests.post(url, headers=headers, json=payload)
#     data = response.json()

#     if "value" not in data:
#         logger.error(f"Error on batch {file_index}: {data}")
#         return [], last_id

#     batch = data["value"]
#     if not batch:
#         return [], last_id

#     filename = f"molecules-{file_index:04}.json"
#     file_path = os.path.join(OUTPUT_DIR, filename)
#     with open(file_path, "w", encoding="utf-8") as f:
#         for doc in batch:
#             f.write(json.dumps(doc) + "\n")

#     # Upload immediately if required
#     if UPLOAD_IMMEDIATELY:
#         blob_client = container_client.get_blob_client(filename)
#         with open(file_path, "rb") as data_file:
#             blob_client.upload_blob(data_file, overwrite=True)
        
#         os.remove(file_path)  # Remove file after upload
#     new_last_id = batch[-1]["id"]
#     return batch, new_last_id

# def fetch_documents_parallel():
#     logger.info("Initializing Azure Blob Storage...")
#     global blob_service_client, container_client
#     blob_service_client = BlobServiceClient.from_connection_string(STORAGE_CONNECTION_STRING)
#     container_client = blob_service_client.get_container_client(CONTAINER_NAME)
#     try:
#         container_client.create_container()
#     except:
#         pass  # Container may already exist

#     total_docs = get_total_document_count()
#     logger.info(f"Total documents: {total_docs}")

#     file_index = 1
#     last_id = ""
#     total_fetched = 0

#     with tqdm(total=total_docs, desc="Fetching") as pbar:
#         while True:
#             batch, last_id = fetch_batch(last_id, file_index)
#             count = len(batch)
#             if count == 0:
#                 break
#             total_fetched += count
#             file_index += 1
#             pbar.update(count)

#     logger.info(f"Fetched {total_fetched} documents.")
#     return total_fetched


# def upload_to_blob_parallel():
#     print("Uploading to Azure Blob Storage...")

#     blob_service_client = BlobServiceClient.from_connection_string(STORAGE_CONNECTION_STRING)
#     container_client = blob_service_client.get_container_client(CONTAINER_NAME)

#     try:
#         container_client.create_container()
#     except:
#         pass  # Already exists

#     files = sorted(os.listdir(OUTPUT_DIR))

#     def upload_file(file_name):
#         blob_client = container_client.get_blob_client(file_name)
#         file_path = os.path.join(OUTPUT_DIR, file_name)
#         with open(file_path, "rb") as data:
#             blob_client.upload_blob(data, overwrite=True)

#     with ThreadPoolExecutor(max_workers=8) as executor:
#         list(tqdm(executor.map(upload_file, files), total=len(files), desc="Uploading"))

#     print("Upload complete.")

# # Run the script
# if __name__ == "__main__":
#     total = fetch_documents_parallel()
#     upload_to_blob_parallel()

import requests
from bs4 import BeautifulSoup
from typing import List

def get_claim_divs_html(url: str) -> List[str]:
    """
    Fetches the given URL, parses the HTML, and returns a list of
    HTML strings corresponding to all <div class="claim"> elements.
    """
    response = requests.get(url)
    response.raise_for_status()  # Raises an error for bad status codes
    
    soup = BeautifulSoup(response.content, 'html.parser')
    
    # Find all divs with class 'claim'
    claim_divs = soup.find_all('div', class_='claim')
    
    # Convert each to HTML string
    html_list = [str(div) for div in claim_divs]
    return html_list

get_claim_divs_html("https://serpapi.com/searches/54c6c22b8fd5aedb/6863c95ebf04d51c89c78ab8.html")
