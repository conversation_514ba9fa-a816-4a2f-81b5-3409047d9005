# import pymongo, traceback
# import certifi, json


# client = pymongo.MongoClient("mongodb+srv://chemstack:<EMAIL>/agent_hub_db?tls=true&authMechanism=SCRAM-SHA-256&retrywrites=false&maxIdleTimeMS=120000", tlsCAFile=certifi.where())

# db = client["retro_synthesis"]
# collection = db["retro_data"]


# coll = collection.find({'route_status': 'COMPLETED'})

# for i in list(coll):
#     _id = i.get('_id')
#     modi = False
#     for dt in i.get('data',[]):
#         try:
#             other_info = dt.get('other_information', {})
#             if isinstance(other_info, str):
#                 dt['other_information'] = {}
#                 modi = True
#                 continue
#             other_info = other_info.get('reagents_and_solvents', [])
#             if other_info:
#                 for kk in other_info:
#                     if kk.get('smiles') == '[Pd].[C]' or 'Palladium' in kk.get('name',''):
#                         modi = True
#                         other_info.remove(kk)
#                         # print(dt.get('other_information').get('reagents_and_solvents'))
#         except Exception as e:
#             print(dt)
#             print(e, _id, traceback.format_exc())
#             break
#     if modi:
#         collection.update_one({"_id" : _id}, {"$set" : {"data" : i.get('data')}})



