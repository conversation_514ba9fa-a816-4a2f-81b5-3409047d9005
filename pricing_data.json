{"Ammonium Bicarbonate": ["C(=O)(O)[O-].[NH4+]", 42.0], "Ammonium Carbonate": ["C(=O)([O-])[O-].[NH4+].[NH4+]", 73.5], "Ammonium Nitrate": ["[NH4+].[NO3-]", 62.5], "Borax": ["[B4O5(OH)4-2].[Na+].[Na+].8H2O", 60.0], "Bromine": ["BrBr", 425.0], "Calcium Carbonate": ["C(=O)([O-])[O-].[Ca+2]", 29.75], "Carbon Disulphide": ["C(=S)=S", 126.5], "Caustic Potash": ["[K+].[OH-]", 99.5], "Caustic Soda": ["[Na+].[OH-]", 60.62], "Hydro": ["S(S(=O)(=O)[O-])(=O)=O.[Na+].[Na+]", 87.0], "Hydrogen Peroxide": ["OO", 37.0], "Hyflosupercel": ["[Si](=O)=O", 66.0], "Lithopone": ["[O-]S(=O)(=O)[O-].O=[Zn].S=[Zn].[Ba+2]", 154.5], "Magnesium Carbonate": ["C(=O)([O-])[O-].[Mg+2]", 90.5], "Mercury": ["[Hg]", 2450.0], "Nitric Acid": ["[N+](=O)(O)[O-]", 50.0], "Phosphoric Acid": ["OP(=O)(O)O", 84.5], "Potassium Carbonate": ["C(=O)([O-])[O-].[K+].[K+]", 80.0], "Potassium Permanganate": ["[O-][Mn](=O)(=O)=O.[K+]", 173.0], "Soda Ash": ["C(=O)([O-])[O-].[Na+].[Na+]", 31.5], "Sodium Nitrite": ["[N+](=O)[O-].[Na+]", 52.0], "Sodium Nitrate": ["[N+](=O)([O-])[O-].[Na+]", 74.0], "Sulphuric Acid": ["OS(=O)(=O)O", 13.0], "Titanium Dioxide": ["O=[Ti]=O", 231.89], "Zinc Oxide": ["O=[Zn]", 184.33], "Acetic Acid": ["CC(=O)O", 42.12], "Acetone": ["CC(=O)C", 13264.2], "Acrylamide": ["C=CC(=O)N", 230.0], "Acrylic Acid": ["C=CC(=O)O", 28973.67], "Acrylonitrile": ["C=CC#N", 30282.0], "Adipic Acid": ["C(CCC(=O)O)CC(=O)O", 92.0], "Aniline": ["C1=CC=C(C=C1)N", 124.0], "Benzene": ["C1=CC=CC=C1", 89.0], "Benzoic Acid": ["C1=CC=C(C=C1)C(=O)O", 86.0], "Benzoyl Chloride": ["C1=CC=C(C=C1)C(=O)Cl", 125.0], "Benzyl Alcohol": ["C1=CC=C(C=C1)CO", 152.5], "Benzyl Chloride": ["C1=CC=C(C=C1)CCl", 165.0], "Bisphenol A": ["CC(C)(c1ccc(O)cc1)c2ccc(O)cc1)O", 282.5], "n Butanol": ["CCCCO", 88.33], "Butyl Acetate": ["CCCCOC(=O)C", 79.5], "Butyl Acrylate": ["CCCCOC(=O)C=C", 115.0], "Butyl Carbitol": ["CCCCOCCOCCO", 130.0], "Butyl Cellosolve": ["CCCCOCCO", 115.0], "Butyl Stearate": ["CCCCCCCCCCCCCCCCCC(=O)OCCCC", 105.0], "C9 Solvent": ["SMILES_NOT_FOUND", 77.5], "C10 Solvent": ["SMILES_NOT_FOUND", 60.0], "Cellosolve": ["CCOCCO", 122.0], "Chloroform": ["C(Cl)(Cl)Cl", 21.0], "Citric Acid": ["C(C(=O)O)C(CC(=O)O)(C(=O)O)O", 123.67], "m Cresol": ["Cc1cccc(c1)O", 270.0], "o Cresol": ["CC1=CC=CC=C1O", 300.0], "p Cresol": ["CC1=CC=C(C=C1)O", 325.0], "mixed Cresol": ["SMILES_NOT_FOUND", 85.0], "Cyclohexane": ["C1CCCCC1", 88.75], "Cyclohexanone": ["C1CCC(=O)CC1", 119.75], "Diacetone": ["CC(C)(O)CC(=O)C", 99.5], "Dibutyl Maleate": ["CCCCOC(=O)C=CC(=O)OCCCC", 108.0], "Dibutyl Phthalate": ["CCCCOC(=O)C1=CC=CC=C1C(=O)OCCCC", 112.0], "Dicyandiamide": ["N#CN=C(N)N", 475.0], "Diethanolamine": ["O(CCN)CCO", 110.0], "Diethylene Glycol": ["C(COCCO)O", 74.75], "Diethyl Phthalate": ["CCOC(=O)c1ccccc1C(=O)OCC", 85.0], "Diisobutyl phthalate": ["CC(C)COC(=O)c1ccccc1C(=O)OCC(C)C", 111.0], "Dimethyl formamide": ["CN(C)C=O", 60.0], "Dioctyl Adipate": ["CCCCCCCCOC(=O)CCCCC(=O)OCCCCCCCC", 130.5], "Dioctyl Maleate": ["CCCCCCCCOC(=O)C=CC(=O)OCCCCCCCC", 118.5], "Dioctyl Phthalate": ["CCCCCCCCOC(=O)C1=CC=CC=C1C(=O)OCCCCCCCC", 122.75], "2 Ethylhexyl Acrylate": ["CCCCC(CC)COC(=O)C=C", 150.0], "Ethyl Acetate": ["CCOC(=O)C", 75.12], "Ethyl Acrylate": ["CCOC(=O)C=C", 121.0], "Ethylene Dichloride": ["C(CCl)Cl", 42.62], "Ethylene Glycol": ["C(CO)O", 60.0], "Formaldehyde": ["C=O", 19.5], "Formic Acid": ["C(=O)O", 82.0], "Glycerine": ["C(C(CO)O)O", 112.0], "Glyoxal": ["C(=O)C=O", 103.0], "Hexamine": ["C1N2CN3CN1CN(C2)C3", 95.0], "n Hexane": ["CCCCCC", 85.0], "Hexylene glycol": ["CC(O)CC(C)(C)O", 145.0], "Isobutyl Alcohol": ["CC(C)CO", 74.0], "Isopropyl Alcohol": ["CC(C)O", 128.33], "Maleic Anhydride": ["C1=CC(=O)OC1=O", 95.5], "Melamine": ["c1(nc(nc(n1)N)N)N", 104.0], "Methanol": ["CO", 27.5], "Methyl Ethyl Ketone": ["CCC(=O)C", 106.0], "Methyl Isobutyl Ketone": ["CC(C)CC(=O)C", 145.67], "Methylene Dichloride": ["C(Cl)Cl", 37.0], "Monoethanolamine": ["C(CO)N", 170.0], "2 Ethylhexanol": ["CCCCC(CC)CO", 93.5], "Octoic acid": ["CCCCCCCC(=O)O", 100.0], "Oxalic Acid": ["C(=O)(C(=O)O)O", 88.0], "Phenol": ["C1=CC=C(C=C1)O", 85.75], "Phthalic Anhydride": ["C1=CC=C2C(=C1)C(=O)OC2=O", 105.0], "Polyethylene Glycol": ["SMILES_NOT_FOUND", 155.5], "Polyvinyl Alcohol": ["SMILES_NOT_FOUND", 190.0], "Propyl Acetate": ["CCCOC(=O)C", 95.0], "Propylene Glycol": ["CC(CO)O", 91.75], "Purified Terephthalic Acid": ["c1cc(ccc1C(=O)O)C(=O)O", 73.0], "Sodium Alginate": ["SMILES_NOT_FOUND", 280.0], "Sorbitol": ["C(C(C(C(C(CO)O)O)O)O)O", 51.0], "Styrene Monomer": ["C=CC1=CC=CC=C1", 107.12], "Tartaric Acid": ["C(C(C(=O)O)O)(C(=O)O)O", 350.0], "Thiourea": ["C(=S)(N)N", 150.0], "Toluene": ["CC1=CC=CC=C1", 101.25], "Trichloroethylene": ["C(=C(Cl)Cl)Cl", 68.5], "Triethanolamine": ["C(CO)N(CCO)CCO", 115.67], "Triethylene Glycol": ["OCCOCCOCCO", 190.0], "Vinyl Acetate Monomer": ["CC(=O)OC=C", 86.5], "Wax Industrial": ["SMILES_NOT_FOUND", 105.0], "Wax Paraffin": ["SMILES_NOT_FOUND", 98.0], "m Xylene": ["CC1=CC(=CC=C1)C", 57.0], "o Xylene": ["CC1=CC=CC=C1C", 101.5], "Xylene Mixed": ["SMILES_NOT_FOUND", 75.75], "Chlorzoxazone": ["Clc1ccc2nc(O)c(=O)[nH]c12", 620.0], "Aceclofenac": ["Clc1ccccc1Nc2cccc(C(=O)COC(=O)C)c2", 920.0], "Ciprofloxacin": ["C1CC1N2C=C(C(=O)C3=CC(=C(C=C32)N4CCNCC4)F)C(=O)O", 2125.0], "Albendazole": ["CCCSC1=CC2=C(C=C1)N=C(N2)NC(=O)OC", 1500.0], "Clarithromycin": ["CCC1OC(=O)C(C)C(OC2CC(C)(OC)C(N(C)C)C(O)C2)C(C)C(O)C(C)(O)COC(C(O)C1C)OC3CCC(C)(O)C(OC)C3", 13250.0], "Allopurinol": ["c1c2c(ncn1)ncnc2O", 2000.0], "Clopidogrel Bisulphate": ["CS(=O)(=O)O.COC(=O)C(c1cccs1)c2ccccc2Cl.N", 3575.0], "Ambroxol HCI": ["C1CC(CCC1NCC2=C(C(=CC(=C2)Br)Br)N)O.Cl", 2400.0], "Closantel": ["C1=CC(=C(C=C1NC(=O)C2=C(C(=CC(=C2I)I)O)Cl)Cl)C(C#N)C3=CC=C(C=C3)Cl", 5400.0], "Amitriptyline": ["CN(C)CCC=C1c2ccccc2CCc3ccccc13", 2400.0], "Clotrimazole": ["C1=CC=C(C=C1)C(C2=CC=CC=C2)(C3=CC=CC=C3Cl)N4C=CN=C4", 1500.0], "Amlodipine Besylate": ["Clc1ccccc1C2(OCCO2)c3c(c(nc(c3C(=O)OCCOC)N)C(=O)OCC.OS(=O)(=O)c4ccccc4", 2500.0], "Cloxacillin Sodium": ["CC1(C)S[C@@H]2[C@H](NC(=O)C(=C(Cl)c3ccccc3)c4onc(C)c4Cl)C(=O)N2[C@H]1C(=O)[O-].[Na+]", 2550.0], "Amoxycillin Sodium": ["CC1(C(N2C(S1)C(C2=O)NC(=O)C(C3=CC=C(C=C3)O)N)C(=O)[O-])C.[Na+]", 6800.0], "Dexchlorpheniramine Maleate": ["OC(=O)C=CC(=O)O.CN(C)CCC(c1ccc(Cl)cc1)c2ccccn2", 13200.0], "Amoxycillin Trydrate": ["SMILES_NOT_FOUND", 2700.0], "Dextromethorphan HBr": ["Br.COc1ccc2c(c1)[C@H]3CC[C@](C)(N(C)CC3)[C@@H]2CC2", 6100.0], "Ampicillin Sodium": ["CC1(C(N2C(S1)C(C2=O)NC(=O)C(C3=CC=CC=C3)N)C(=O)[O-])C.[Na+]", 4100.0], "Diclofenac Potassium": ["CC1=C(C(=CC=C1)NC2=C(C=CC=C2Cl)Cl)C(=O)[O-].[K+]", 630.0], "Ampicillin Trydrate": ["SMILES_NOT_FOUND", 2500.0], "Diclofenac Sodium": ["CC1=C(C(=CC=C1)NC2=C(C=CC=C2Cl)Cl)C(=O)[O-].[Na+]", 580.0], "Atenolol": ["CC(C)NCC(C1=CC=C(C=C1)CC(=O)N)O", 1450.0], "Dicyclomine": ["CCN(CC)CCOC(=O)C1(CCCCC1)C2CCCCC2", 2100.0], "Azithromycin Dydrate": ["SMILES_NOT_FOUND", 12300.0], "Diethyl Carbamazine Citrate": ["CCN(CC)C(=O)N1CCN(CC1)C.C(C(=O)O)C(CC(=O)O)(C(=O)O)O", 1200.0], "Bromhexine": ["CN(CC1=C(C(=CC(=C1)Br)Br)N)C2CCCCC2", 1775.0], "Diiodohydroxyquinoline": ["Oc1c(I)cc(I)c2cccnc12", 4200.0], "Brompheniramine Maleate": ["CN(C)CCC(C1=CC=C(Br)C=C1)C2=CN=CC=C2.C(=CC(=O)O)C(=O)O", 6100.0], "Diphenhydramine": ["CN(C)CCOC(C1=CC=CC=C1)C2=CC=CC=C2", 875.0], "Caffeine": ["CN1C=NC2=C1C(=O)N(C(=O)N2C)C", 700.0], "Erythromycin Estolate": ["CCCCCCCCCCC(=O)OS(=O)(=O)O.CCC1OC(=O)C(C)C(OC2CC(C)(OC)C(N(C)C)C(O)C2)C(C)C(O)C(C)(O)COC(C(O)C1C)OC3CCC(C)(O)C(OC)C3", 4900.0], "Cefixime Trydrate": ["SMILES_NOT_FOUND", 11100.0], "Erythromycin Stearate": ["CCCCCCCCCCCCCCCCC(=O)O.CCC1OC(=O)C(C)C(OC2CC(C)(OC)C(N(C)C)C(O)C2)C(C)C(O)C(C)(O)COC(C(O)C1C)OC3CCC(C)(O)C(OC)C3", 4550.0], "Cetrizine Di HCI": ["C1=CC=C(C=C1)C(C2=CC=CC=C2)N3CCN(CC3)CCOCCO.Cl.Cl", 2200.0], "Ethamsylate": ["CCNCC1=CC(=C(C=C1O)O)S(=O)(=O)O", 650.0], "Chlorpheniramine Maleate": ["CN(C)CCC(C1=CC=C(Cl)C=C1)C2=CN=CC=C2.C(=CC(=O)O)C(=O)O", 1375.0], "Ethophylline": ["CN1C(=O)N(C)c2nc[nH]c2C1=O.NCCO", 1100.0], "Fenbendazole": ["COC(=O)NC1=NC2=C(N1)C=C(C=C2)SC3=CC=CC=C3", 2250.0], "Fexofenadine": ["CC(C)(C1=CC=C(C=C1)C(CCCN2CCC(CC2)C(C3=CC=CC=C3)(C4=CC=CC=C4)O)O)C(=O)O", 5100.0], "Fluconazole": ["c1c(F)cc(F)c(c1F)C(Cn2cncn2)(Cn3cncn3)O", 6800.0], "Fluoxetine": ["CNCCC(C1=CC=CC=C1)OC2=CC=C(C=C2)C(F)(F)F", 6800.0], "Frusemide": ["C1=CC(=C(C=C1S(=O)(=O)N)Cl)NCCOC2=CC=CO2", 2950.0], "Glibenclamide": ["Clc1ccc(cc1)C(=O)NCCc2ccc(S(=O)(=O)NC(=O)NC3CCCCC3)cc2", 2400.0], "Gliclazide": ["CC1=CC=C(C=C1)S(=O)(=O)NC(=O)NN2CC3CCCC3C2", 4900.0], "Guiafenesin": ["COC1=CC=CC(=C1)OCC(O)CO", 465.0], "Ibuprofen": ["CC(C)CC1=CC=C(C=C1)C(C)C(=O)O", 660.0], "Iodine": ["II", 6900.0], "Iron Hydroxide Polymaltose Complex": ["SMILES_NOT_FOUND", 300.0], "Ketoconazole": ["CC(=O)N1CCN(CC1)C2=CC=C(C=C2)OC3CCN(C(=O)C(C4=CC=C(Cl)C=C4Cl)(O3)CN5C=NC=N5)CC6=CN=CN=C6", 3800.0], "Lidocaine HCI": ["CCN(CC)C(=O)CC1=C(C=CC=C1C)C.Cl", 775.0], "Mebendazole": ["COC(=O)Nc1nc2ccc(cc2[nH]1)C(=O)c3ccccc3", 1800.0], "Mefenamic Acid": ["CC1=C(C(=CC=C1)NC2=CC=CC=C2C(=O)O)C", 500.0], "Mepyramine Maleate": ["CN(C)CCN(CC1=CC=C(C=C1)OC)C2=CC=CC=N2.C(=CC(=O)O)C(=O)O", 3900.0], "Metformin": ["CN(C)C(=N)N=C(N)N", 190.0], "Miconazole Nitrate": ["C1=CC(=C(C=C1Cl)Cl)COC(CN2C=CN=C2)C3=C(C=C(C=C3Cl)Cl)Cl.[N+](=O)(O)[O-]", 1650.0], "Niacinamide": ["NC(=O)c1cccnc1", 520.0], "Nifedipine": ["COC(=O)C1=C(NC(=C(C1C2=CC=CC=C2[N+](=O)[O-])C(=O)OC)C)C", 3550.0], "Nimesulide": ["NS(=O)(=O)c1ccc(Oc2ccccc2[N+](=O)[O-])cc1", 475.0], "Ornidazole": ["ClCH(O)CN1C=NC(=C1C)[N+](=O)[O-]", 960.0], "Oxyclozanide": ["C1=C(C=C(C(=C1NC(=O)C2=C(C(=CC(=C2Cl)Cl)Cl)O)O)Cl)Cl", 1380.0], "Paracetamol": ["CC(=O)NC1=CC=C(C=C1)O", 260.0], "Pheniramine Maleate": ["OC(=O)C=CC(=O)O.CN(C)CCC(c1ccccc1)c2ccccn2", 1425.0], "Phenylbutazone": ["CCCCc1nnc(c(=O)n1c2ccccc2)c3ccccc3", 865.0], "Phenylephrine": ["CNCC(C1=CC(=CC=C1)O)O", 6200.0], "Phenytoin Sodium": ["[Na+].O=c1[nH]c(=O)c(c2ccccc2)(c3ccccc3)[nH]1", 1020.0], "Piperazine Citrate": ["OC(CC(=O)O)(C(=O)O)CC(=O)O.C1CNCCN1", 390.0], "Piperazine Hexahydrate": ["C1CNCCN1.O.O.O.O.O.O", 370.0], "Potassium Iodate": ["[K+].[O-]I(=O)=O", 4500.0], "Potassium Iodide": ["[K+].[I-]", 5500.0], "Povidone Iodine": ["SMILES_NOT_FOUND", 1350.0], "Pregabalin": ["NC[C@H](CC(C)C)C(=O)O", 2825.0], "Pyroxicam": ["CC1=CN=C(S(=O)(=O)N1C2=CC=CC=N2)C(=O)NC3=CC=CC=N3", 3100.0], "Rafoxanide B. P": ["SMILES_NOT_FOUND", 5400.0], "Roxithromycin": ["CCC1OC(=O)C(C)C(OC2CC(C)(OC)C(N(C)C)C(O)C2)C(C)C(O)C(C)(O)COC(C(O)C1C)O[N+](=O)[O-]", 5500.0], "Salbutamol Sulphate": ["OS(=O)(=O)O.CN(C(C)C)CC(O)c1cc(O)c(CO)cc1", 6600.0], "Sildenafil Citrate": ["OC(CC(=O)O)(C(=O)O)CC(=O)O.CCCc1nnc(c2c1[nH]c(=O)c(N)c2OCC)c3cc(ccc3S(=O)(=O)N4CCN(C)CC4)OCC", 1350.0], "Sodium Iodide": ["[Na+].[I-]", 5600.0], "Tinidazole": ["O=N(=O)c1cn(CCOS(=O)(=O)C)c(C)n1", 930.0], "Theophylline Anhydrous": ["CN1C(=O)N(C)c2nc[nH]c2C1=O", 1050.0], "Atazanavir sulphate": ["OS(=O)(=O)O.CC(C)(C)NC(=O)[C@H](N(Cc1ccccc1)[C@H](O)[C@H](Cc2ccccc2)NC(=O)OC(C)(C)C)N3N=C(c4ccc(OC)cc4)C(=O)N3", 36000.0], "Acyclovir": ["Nc1nc(O)c2ncn(CCO)c2n1", 3500.0], "Ambroxol": ["C1CC(CCC1NCC2=C(C(=CC(=C2)Br)Br)N)O", 2700.0], "Ampiciilin Trydrate": ["SMILES_NOT_FOUND", 2950.0], "Atorvastatin Calcium": ["CC(C)c1c(C(=O)Nc2ccccc2F)c(-c2ccccc2)c(-c2ccc(N(CCC(O)CC(O)CC(=O)[O-])S(=O)(=O)c3ccccc3)cc2)c(C(=O)c2ccccc2)n1.[Ca+2]", 8500.0], "Capacetabine": ["CCCCC(=O)N(C1C(C(C(O1)N2C=C(C(=O)N=C2F)N)O)O)C(=O)OC", 15500.0], "Cefuroxime Axetil": ["CC(=O)OCH(C)OC(=O)[C@@H]1N2C(C(NC(=O)C(=NOC)c3csc(N)n3)C2=O)SCC1C", 10800.0], "Cefexime Trydrate": ["SMILES_NOT_FOUND", 12800.0], "Cefpodoxime proxetil": ["COC(=O)OC(C)OC(=O)[C@@H]1N2C(C(NC(=O)C(=NOC)c3csc(N)n3)C2=O)SC=C1COC", 11500.0], "Cyproheptidine": ["CN1CCC(=C2c3ccccc3C=Cc4sccc24)CC1", 6000.0], "Citicoline sodium": ["[Na+].[O-]P(=O)(O)OC[C@H]1O[C@H](n2cnc3c(N)ncnc23)[C@H](O)[C@@H]1OCCN(C)(C)C", 7500.0], "Cephalexin": ["CC1=C(N2C(C(C2=O)NC(=O)C(C3=CC=CC=C3)N)SC1)C(=O)O", 6000.0], "Cetrizine Di HCl": ["SMILES_NOT_FOUND", 2100.0], "Closantel Base": ["C1=CC(=C(C=C1NC(=O)C2=C(C(=CC(=C2I)I)O)Cl)Cl)C(C#N)C3=CC=C(C=C3)Cl", 5800.0], "Dapoxetine": ["CN(C)C(CCOC1=CC=CC2=CC=CC=C21)C3=CC=CC=C3", 7500.0], "Domperidone": ["Clc1ccc(N2CCN(CCC(c3nc4ccccc4[nH]3)c5ccc(Cl)cc5)CC2)cc1", 3000.0], "Drotaverine": ["CCOC1=C(C=C(C=C1)C=C2C3=CC(=C(C=C3CCN2)OCC)OCC)OCC", 3400.0], "Dextromethorphan HBR": ["Br.COc1ccc2c(c1)[C@H]3CC[C@](C)(N(C)CC3)[C@@H]2CC2", 6100.0], "Doxylamine Succinate": ["CC(C1=CC=CC=C1)(C2=CC=CC=N2)OCCN(C)C.C(CC(=O)O)C(=O)O", 8200.0], "Enrofloxacin": ["CCN1CCN(CC1)C2=C(C=C3C(=C2)N(C=C(C3=O)C(=O)O)C4CC4)F", 2500.0], "Esomeprazole Mag Trydrate": ["SMILES_NOT_FOUND", 4800.0], "Escitalopram Oxalate": ["OC(=O)C(=O)O.N#C[C@@]1(c2ccc(F)cc2)c3cc(CN(C)C)ccc3CO1", 12000.0], "Enoxaprin Sodium": ["SMILES_NOT_FOUND", 1500000.0], "Etoricoxib": ["Cc1ccn(c1)c2cc(Cl)c(S(=O)(=O)C)cc2Cl", 8000.0], "Famotidine": ["NS(=O)(=O)c1csc(N)n1CCSCNC(=N)N", 3400.0], "Favipiravir": ["C1=C(N=C(C(=O)N1)C(=O)N)F", 12000.0], "Fexofinadine": ["SMILES_NOT_FOUND", 4800.0], "Famciclovir": ["CC(=O)OCCOCn1cnc2c(N)ncnc12", 38000.0], "Gabapentin": ["NC(CC1CCCCC1)C(=O)O", 2000.0], "Itraconazole": ["Clc1ccc(cc1)N2CCN(CC2)c3nc(onn3)c4ccc(OCC5CN(C(=O)N(C(C)CC)C(O)C(C)(C)c6ccccc6Cl)C5)cc4", 7400.0], "Ketrolac Tromethamine": ["C1C=CC2=C(C1)N(C(=O)C2CC(=O)O)C3=CC=CC=C3C(=O)C4=CC=CC=C4.C(CO)(CO)(CO)N", 10000.0], "Losartan potassium": ["[K+].Oc1nc(Cl)c(N2N=NN=C2Cc3ccc(O)cc3)c(c1CCC)N4N=NN=C4", 3900.0], "Levetiracetam": ["CCC[C@H](N)C(=O)N1CCCC1=O", 2200.0], "Lansoprazole": ["Cc1c(OC(F)(F)F)c(C)cn1CS(=O)c2[nH]c3ccccc3n2", 6000.0], "Lamivudine": ["Nc1nc(O)c(=O)n(c1)[C@H]2CS[C@@H](CO)C2", 8400.0], "Lisinopril": ["NCCCCC[C@H](N)C(=O)N1[C@@H](C(=O)O)C[C@H]2CCCC[N@H]12", 30000.0], "Meropenam": ["CC1C(C2N1C(=C(S2)C(=O)O)SC(C(=O)NC(=O)N(C)C)C3CC3)C(C)O", 52000.0], "Minoxidil": ["Nc1cc(N2CCCCC2=N(=O)[O-])ccn1", 5000.0], "Montelukast Sodium": ["[Na+].[O-]C(=O)C(C)(C)Sc1ccc(cc1)C=CC=C(c2ccccc2Cl)c3c(N4CC[C@@H](O)C4)c(C(=O)O)ccc3", 38000.0], "Nortriptyline": ["CNCCC=C1C2=CC=CC=C2CCC3=CC=CC=C31", 6300.0], "Naproxen sodium": ["[Na+].[O-]C(=O)[C@H](C)c1ccc2cc(OC)ccc12", 3400.0], "Nebivolol": ["C1CC2=C(C=CC(=C2)F)OC1C(CNCC(C3CCC4=C(O3)C=CC(=C4)F)O)O", 125000.0], "Nevirapine": ["Cc1c2C(=O)N(C(C)C)c3nc(N)ncc13N2C", 8000.0], "Omeprazole": ["Cc1c(OC)c(C)cn1CS(=O)c2[nH]c3ccc(OC)cc3n2", 3500.0], "Oxcarbazepine": ["O=C1NC(=O)c2cc(ccc2N1)C(=O)N", 7000.0], "Pantaprazole sodium": ["CC1=CN=C(C(=C1OC)C)CS(=O)C2=NC3=CC(F)=C(F)C=C3N2.[Na+]", 4300.0], "Posaconazole": ["CCC(C(C1=CC=C(C=C1)N2N=CN(C2=O)C(C)CC)O)N3CCN(CC3)C4=CC=C(C=C4)N5N=CN=C5", 500000.0], "Ramipril": ["CCC(=O)O[C@H](C)[C@H](N[C@H](C)C(=O)N1[C@@H](C(=O)O)C[C@H]2CCCC[N@H]12)c3ccccc3", 38500.0], "Ranitidine": ["CNC(=C[N+](=O)[O-])NCCSCC1=CC=C(O1)CN(C)C", 1750.0], "Ranolazine": ["COC1=C(C=C(C=C1)N2CCN(CC2)CC(CN3C(=O)C4=C(C=CC=C4C3=O)NC5=CC=CC=C5OC)O)OC", 1800.0], "Rabeprazole Sodium": ["CC1=CN=C(C(=C1OC)C)CS(=O)C2=NC3=CC=CC=C3N2.[Na+]", 6700.0], "Rosuvastatin calcium": ["[Ca+2].CC(C)c1nc(N(C)S(=O)(=O)C)c(c(n1)c2ccc(F)cc2)C=C[C@H](O)C[C@H](O)CC(=O)[O-]", 15000.0], "Sildenafil citrate": ["OC(CC(=O)O)(C(=O)O)CC(=O)O.CCCc1nnc(c2c1[nH]c(=O)c(N)c2OCC)c3cc(ccc3S(=O)(=O)N4CCN(C)CC4)OCC", 1800.0], "Sertraline": ["CNC1CCC(C2=CC=CC=C12)C3=CC(=C(C=C3)Cl)Cl", 5000.0], "Sulphamethaxazole": ["CC1=CC(=NO1)NS(=O)(=O)C2=CC=C(C=C2)N", 1150.0], "Sulbactm sodium": ["CC1(C(N2C(S1(=O)=O)C(C2=O)NC(=O)C(C3=CC=CC=C3)N)C(=O)[O-])C.[Na+]", 6500.0], "Telmisartan": ["CC1=CC(=C(C=C1N2C3=C(C=C(C=N3)C(=O)O)N=C2CC4=CC=C(C=C4)C5=CC=CC=C5C)C)C", 2600.0], "Teneligliptin": ["O=C([C@H]1N(C(=O)c2nc(N3CCN(Cc4scnc4C)CC3)cs2)CCN1C(C)(C)S(=O)(=O)O)N5CCC(c6ccccc6)CC5", 9500.0], "Tenofovir Disoproxil Fumerate": ["CC(C)OC(=O)OCCOP(=O)(CN1C=NC2=C(N=CN=C21)N)OCCOC(=O)OC(C)C.C(=CC(=O)O)C(=O)O", 8500.0], "Terbinafine": ["CC(C)(C)C#CC=CCN(C)CC1=CC=CC2=CC=CC=C21", 3650.0], "Ticagrelor": ["Fc1ccc(cc1F)[C@H]2SC[C@H](N3C(=O)N(CCC(O)O)C(=N3)c4nc(N)c5ncn(C[C@H]6O[C@H](CSCC(C)C)[C@H](O)[C@@H]6O)c5n4)C2", 30000.0], "Tramadol": ["CN(C)CC1CCCCC1(C2=CC(=CC=C2)OC)O", 2400.0], "Vildagliptin": ["N#C[C@H]1CN(C(=O)CN2C[C@@H](O)CC2)CC1", 5200.0], "Voriconazole": ["Fc1ccc(C(c2nc(F)c(C(F)F)cn2)(Cn3cncn3)O)cc1F", 80000.0], "Zidovudine": ["Nc1nc(O)c(=O)n(c1)[C@H]2C[C@H](N=[N+]=[N-])[C@H](CO)O2", 14000.0], "Acid Slurry": ["CCCCCCCCCCCCc1ccc(S(=O)(=O)O)cc1", 130.0], "Alum Ferric": ["SMILES_NOT_FOUND", 23.0], "Ammonium Bifluoride": ["[NH4+].[F-].[HF]", 178.0], "Ammonium Chloride": ["[NH4+].[Cl-]", 20.0], "Ammonium Phosphate": ["SMILES_NOT_FOUND", 135.0], "Ammonium Sulphate": ["[NH4+].[NH4+].[O-]S(=O)(=O)[O-]", 22.0], "Antimony Trioxide": ["O=[Sb]O[Sb]=O", 800.0], "Barium Chloride": ["[Ba+2].[Cl-].[Cl-]", 58.0], "Bleaching": ["SMILES_NOT_FOUND", 14.0], "Boric Acid": ["B(O)(O)O", 145.0], "Calcium Chloride": ["[Ca+2].[Cl-].[Cl-]", 12.0], "Calcium Chloride Anhydrous": ["[Ca+2].[Cl-].[Cl-]", 28.0], "Camphor Oil": ["SMILES_NOT_FOUND", 135.0], "Chromic Acid": ["O=[Cr](=O)(O)O", 280.0], "Chlorinated Xylene": ["SMILES_NOT_FOUND", 85.0], "Copper Sulphate": ["[Cu+2].[O-]S(=O)(=O)[O-]", 220.0], "Diammonium Phosphate": ["[NH4+].[NH4+].OP(=O)([O-])[O-]", 34.0], "Dioctylmalite": ["SMILES_NOT_FOUND", 82.0], "Ferric Chloride": ["Cl[Fe](Cl)Cl", 38.0], "Ferrous Sulphate": ["[Fe+2].[O-]S(=O)(=O)[O-]", 16.0], "Hydrochloric Acid": ["Cl", 6.0], "Hyflosupercell": ["[Si](=O)=O", 138.0], "Litharge": ["O=[Pb]", 220.0], "Magnesium Sulphate": ["[Mg+2].[O-]S(=O)(=O)[O-]", 16.0], "Naphthalene Balls": ["C1=CC=C2C=CC=CC2=C1", 130.0], "Nickel Chloride": ["[Ni+2].[Cl-].[Cl-]", 620.0], "Potassium Nitrate": ["[N+](=O)([O-])[O-].[K+]", 115.0], "Potassium Phosphate": ["OP(=O)([O-])[O-].[K+].[K+]", 158.0], "S.L.E.S": ["CCCCCCCCCCCCOS(=O)(=O)[O-].[Na+]", 72.0], "Soda Ash Light": ["C(=O)([O-])[O-].[Na+].[Na+]", 30.0], "Sodium Bicarbonate": ["C(=O)(O)[O-].[Na+]", 33.0], "Sodium Bichromate": ["[Na+].[Na+].[O-][Cr](=O)O[Cr](=O)([O-])=O", 165.0], "Sodium Bisulphite": ["OS(=O)[O-].[Na+]", 52.0], "Sodium Chlorite": ["O=Cl[O-].[Na+]", 260.0], "Sodium Cyanide": ["[C-]#N.[Na+]", 650.0], "Sodium Fluoride": ["[F-].[Na+]", 150.0], "Sodium Formate": ["C(=O)[O-].[Na+]", 53.0], "Sodium Hexameta Phosphate": ["SMILES_NOT_FOUND", 128.0], "Sodium Hydrosulphite": ["S(S(=O)(=O)[O-])(=O)=O.[Na+].[Na+]", 180.0], "Sodium Metabisulphite": ["O=S([O-])S(=O)(=O)[O-].[Na+].[Na+]", 35.0], "Sodium Silicate": ["SMILES_NOT_FOUND", 28.5], "Sodium Sulphate": ["[O-]S(=O)(=O)[O-].[Na+].[Na+]", 15.0], "Sodium Sulphide 50": ["[S-2].[Na+].[Na+]", 58.0], "Sodium Sulphide 58": ["[S-2].[Na+].[Na+]", 52.0], "Sodium Sulphite": ["[O-]S(=O)[O-].[Na+].[Na+]", 50.0], "Sodium Tripolyphosphate": ["O=P([O-])(OP(=O)([O-])[O-])OP(=O)([O-])[O-].[Na+].[Na+].[Na+].[Na+].[Na+]", 92.0], "Trisodium Phosphate": ["[Na+].[Na+].[Na+].[O-]P(=O)([O-])[O-]", 28.0], "Zinc Chloride": ["Cl[Zn]Cl", 82.0], "Zinc Stearate": ["CCCCCCCCCCCCCCCCCC(=O)[O-].CCCCCCCCCCCCCCCCCC(=O)[O-].[Zn+2]", 175.0], "Zinc Sulphate": ["[O-]S(=O)(=O)[O-].[Zn+2]", 58.0], "n Butyl Acetate": ["CCCCOC(=O)C", 100.0], "Camphor": ["CC1(C2CCC1(C(=O)C2)C)C", 415.0], "Cresote Oil": ["SMILES_NOT_FOUND", 88.0], "D D Turpentine": ["SMILES_NOT_FOUND", 145.0], "Diacetone Alcohol": ["CC(C)(O)CC(=O)C", 130.0], "Dimethyl Formamide": ["CN(C)C=O", 82.0], "Di Pentene": ["CC1=CCC(CC1)C(=C)C", 125.0], "EDTA Acid": ["C(CN(CC(=O)O)CC(=O)O)N(CC(=O)O)CC(=O)O", 198.0], "EDTA Disodium": ["C(CN(CC(=O)[O-])CC(=O)O)N(CC(=O)[O-])CC(=O)O.[Na+].[Na+]", 188.0], "EDTA Tetrasodium": ["C(CN(CC(=O)[O-])CC(=O)[O-])N(CC(=O)[O-])CC(=O)[O-].[Na+].[Na+].[Na+].[Na+]", 188.0], "Glycerine CP": ["C(C(CO)O)O", 106.0], "Hydroquinone": ["C1=CC(=CC=C1O)O", 150.0], "Mineral Turpentine Oil": ["SMILES_NOT_FOUND", 92.0], "Monochloro Phenol": ["SMILES_NOT_FOUND", 120.0], "Nitrobenzene": ["C1=CC=C(C=C1)[N+](=O)[O-]", 108.0], "Octanol": ["CCCCCCCCCO", 145.0], "Oleic Acid": ["CCCCCCCC=CCCCCCCCC(=O)O", 120.0], "Paraffin Wax": ["SMILES_NOT_FOUND", 107.0], "Paraformaldehyde": ["C=O", 96.0], "Perchloroethylene": ["C(=C(Cl)Cl)(Cl)Cl", 92.0], "Phenyl": ["C1=CC=[C]C=C1", 108.0], "Phthalic anhydride": ["C1=CC=C2C(=C1)C(=O)OC2=O", 105.0], "Pine Oil": ["SMILES_NOT_FOUND", 167.5], "Poly Aluminium Chloride": ["SMILES_NOT_FOUND", 34.0], "Polyethylene Glycol 400": ["OCCOCCOCCOCCOCCOCCOCCOCCOCCOCCOCCOCCOCCOCCOCCOCCO", 125.0], "Polyethylene Glycol 600": ["OCCOCCOCCOCCOCCOCCOCCOCCOCCOCCOCCOCCOCCOCCOCCOCCOCCOCCOCCOCCOCCOCCO", 150.0], "Red Lead": ["[O-2].[O-2].[Pb+2].[Pb+2].[Pb+4]", 220.0], "Renine": ["SMILES_NOT_FOUND", 72.0], "Rosin": ["SMILES_NOT_FOUND", 120.0], "Sodium Acetate": ["CC(=O)[O-].[Na+]", 38.0], "Sodium Benzoate": ["C1=CC=C(C=C1)C(=O)[O-].[Na+]", 108.0], "Stearic Acid": ["CCCCCCCCCCCCCCCCCC(=O)O", 155.0], "Terpeneol Perfumery": ["CC1=CCC(CC1)C(C)(C)O", 230.0], "Styrene": ["C=CC1=CC=CC=C1", 88063.0], "Terephthalic Acid": ["c1cc(ccc1C(=O)O)C(=O)O", 52539.0], "Vinyl Acetate": ["CC(=O)OC=C", 67645.0], "S.L.E.S.": ["CCCCCCCCCCCCOS(=O)(=O)[O-].[Na+]", 72.0]}