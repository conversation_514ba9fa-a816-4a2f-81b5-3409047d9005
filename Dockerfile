# Use Python 3.12 slim image as base
FROM python:3.12-slim

WORKDIR /app

# Install system dependencies
# Install system dependencies (including RDKit requirements)
RUN apt-get update && apt-get install -y --no-install-recommends \
    build-essential \
    libxrender1 \
    libxext6 \
    libsm6 \
    libglib2.0-0 \
    libfreetype6 \
    libpng16-16 \
    libexpat1 \
    fonts-dejavu-core \
    unzip \
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/*




# Environment variables can be added here if needed
# ENV SOME_ENV=value

# Copy source code
COPY . .

# Install Python dependencies
RUN pip install --no-cache-dir --upgrade pip \
    && pip install --no-cache-dir -r requirements.txt

# Unzip assets if needed
# RUN unzip assets.zip -d /app/assets

# Expose the API port
EXPOSE 8000

# Make the start script executable
RUN chmod +x start.sh

# Start API and Celery worker
C<PERSON> ["./start.sh"]
