"""
Test suite for RootAgentOrchestrator
This file contains comprehensive tests to verify the functionality of the orchestrator.
"""

import pytest
import json
import asyncio
import os
import tempfile
import shutil
from unittest.mock import Mock, patch, AsyncMock, MagicMock, PropertyMock # Added PropertyMock
from typing import Dict, Any, Optional
import sys

# Add the parent directory to sys.path to import the modules
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

try:
    from app.orchestration.root_agent import RootAgentOrchestrator
    # Import the module itself to access its global _orchestrator_moi_context
    from app.orchestration import root_agent
    from app.config.settings import get_settings, Settings
    from app.agents.registry import get_agent_instance
    import autogen
except ImportError as e:
    print(f"Import error during test setup: {e}")
    print("Make sure your project structure is correct and all dependencies are installed")
    print(f"Current sys.path: {sys.path}")
    print(f"Current working directory: {os.getcwd()}")
    expected_path = os.path.abspath(os.path.join(os.path.dirname(__file__), '..', 'app', 'orchestration', 'root_agent.py'))
    print(f"Expected root_agent.py path: {expected_path}, Exists: {os.path.exists(expected_path)}")
    sys.exit(1)


class TestRootAgentOrchestrator:
    """Test class for RootAgentOrchestrator functionality"""
    
    @pytest.fixture(autouse=True)
    def clear_moi_context_before_each_test(self):
        """Clears and restores the MOI context around each test, using direct module access."""
        original_moi_content = root_agent._orchestrator_moi_context.copy()
        # print(f"[Fixture Start] MOI ID: {id(root_agent._orchestrator_moi_context)}, Initial Value: {original_moi_content}")

        root_agent._orchestrator_moi_context["name"] = None
        root_agent._orchestrator_moi_context["smiles"] = None
        # print(f"[Fixture Set to None] MOI ID: {id(root_agent._orchestrator_moi_context)}, Value: {root_agent._orchestrator_moi_context}")
        yield
        # print(f"[Fixture End - Before Restore] MOI ID: {id(root_agent._orchestrator_moi_context)}, Current Value: {root_agent._orchestrator_moi_context}")
        root_agent._orchestrator_moi_context.clear() # Clear it first
        root_agent._orchestrator_moi_context.update(original_moi_content) # Then update with original
        # print(f"[Fixture End - After Restore] MOI ID: {id(root_agent._orchestrator_moi_context)}, Value: {root_agent._orchestrator_moi_context}")

    @pytest.fixture
    def temp_dir_fixture(self):
        temp_dir_path = tempfile.mkdtemp()
        yield temp_dir_path
        shutil.rmtree(temp_dir_path)

    @pytest.fixture
    def mock_settings_obj(self, temp_dir_fixture):
        settings = MagicMock(spec=Settings)
        settings.OPENAI_API_KEY = "test-api-key_ok"
        settings.PERPLEXITY_API_KEY = "test-perplexity-key_ok"
        settings.DEFAULT_LLM_PROVIDER = "openai"
        settings.CHATBOT_AGENT_TIMEOUT = 30
        settings.MAX_CHATBOT_TURNS = 10
        
        settings.APP_STATIC_DIR_ABS = os.path.join(temp_dir_fixture, "static")
        settings.VISUALIZATION_SUBDIR_NAME = "autogen_visualizations"
        settings.VISUALIZATION_DIR_ABS = os.path.join(settings.APP_STATIC_DIR_ABS, settings.VISUALIZATION_SUBDIR_NAME)
        settings.VISUALIZATION_WEB_BASE_PATH = f"/static/{settings.VISUALIZATION_SUBDIR_NAME}"
        os.makedirs(settings.VISUALIZATION_DIR_ABS, exist_ok=True)

        settings.APP_DATA_DIR_ABS = os.path.join(temp_dir_fixture, "data")
        os.makedirs(settings.APP_DATA_DIR_ABS, exist_ok=True)

        settings.REACTION_CLASSIFIER_API_URL = "http://mock.api/reaction_class"
        settings.REACTION_DATASET_FILENAME1 = "orderly_retro_mock.parquet"
        settings.REACTION_DATASET_FILENAME2 = "reaction_classification_checkpoint_mock.parquet"
        settings.PRICING_FILENAME_PRIMARY = "pricing_data_mock.json"
        settings.PRICING_FILENAME_SECONDARY = "second_source_mock.json"
        settings.PRICING_FILENAME_TERTIARY = "sigma_source_mock.json"
        settings.SMARTS_FILENAME = "open_babel_mock.txt"

        with open(os.path.join(settings.APP_DATA_DIR_ABS, settings.SMARTS_FILENAME), 'w') as f:
            f.write("test_group:[CH3]\n")
        with open(os.path.join(settings.APP_DATA_DIR_ABS, settings.PRICING_FILENAME_PRIMARY), 'w') as f:
            json.dump({"test_chemical": ["CCO", 100.0, "TestLocation"]}, f)

        type(settings).APP_DIR_ABS = PropertyMock(return_value=temp_dir_fixture)
        type(settings).APP_DATA_DIR_ABS = PropertyMock(return_value=settings.APP_DATA_DIR_ABS)
        type(settings).REACTION_DATASET_PATH1 = PropertyMock(return_value=os.path.join(settings.APP_DATA_DIR_ABS, settings.REACTION_DATASET_FILENAME1))
        type(settings).REACTION_DATASET_PATH2 = PropertyMock(return_value=os.path.join(settings.APP_DATA_DIR_ABS, settings.REACTION_DATASET_FILENAME2))
        type(settings).PRICING_FILE_PRIMARY = PropertyMock(return_value=os.path.join(settings.APP_DATA_DIR_ABS, settings.PRICING_FILENAME_PRIMARY))
        type(settings).PRICING_FILE_SECONDARY = PropertyMock(return_value=os.path.join(settings.APP_DATA_DIR_ABS, settings.PRICING_FILENAME_SECONDARY))
        type(settings).PRICING_FILE_TERTIARY = PropertyMock(return_value=os.path.join(settings.APP_DATA_DIR_ABS, settings.PRICING_FILENAME_TERTIARY))
        type(settings).OPEN_BABEL_SMARTS_FILE = PropertyMock(return_value=os.path.join(settings.APP_DATA_DIR_ABS, settings.SMARTS_FILENAME))
        type(settings).APP_STATIC_DIR_ABS = PropertyMock(return_value=settings.APP_STATIC_DIR_ABS)
        type(settings).VISUALIZATION_DIR_ABS = PropertyMock(return_value=settings.VISUALIZATION_DIR_ABS)
        type(settings).VISUALIZATION_WEB_BASE_PATH = PropertyMock(return_value=settings.VISUALIZATION_WEB_BASE_PATH)
        
        settings.CAS_SEARCH_URL_TEMPLATE = "https://commonchemistry.cas.org/api/search?q={query}"
        settings.CAS_DETAIL_URL_TEMPLATE = "https://commonchemistry.cas.org/api/detail?cas_rn={cas_rn}"
        settings.PUBCHEM_NAME_PROP_URL_TEMPLATE = "https://pubchem.ncbi.nlm.nih.gov/rest/pug/compound/name/{query}/property/IsomericSMILES,CanonicalSMILES/JSON"
        settings.PUBCHEM_NAME_CID_URL_TEMPLATE = "https://pubchem.ncbi.nlm.nih.gov/rest/pug/compound/name/{query}/cids/JSON"
        settings.PUBCHEM_CID_PROP_URL_TEMPLATE = "https://pubchem.ncbi.nlm.nih.gov/rest/pug/compound/cid/{cid}/property/IsomericSMILES,CanonicalSMILES/JSON"
        settings.CACTUS_SMILES_TO_NAME_URL_TEMPLATE = "https://cactus.nci.nih.gov/chemical/structure/{smiles}/iupac_name"
        settings.PUBCHEM_SMILES_PROP_URL_TEMPLATE = "https://pubchem.ncbi.nlm.nih.gov/rest/pug/compound/smiles/{smiles}/property/IUPACName/JSON"
        return settings
    
    @pytest.fixture
    def mock_agent_registry_fixture(self):
        agents = {}
        viz_agent = MagicMock(name="VisualizationAgentMock") 
        mock_viz_path = "/static/autogen_visualizations/test_viz.png" 
        viz_agent.process_message = AsyncMock(return_value={"reply": f"Visualization created. Path: {mock_viz_path}", 
                                                          "visualization_path": mock_viz_path})
        agents["VisualizationAgent"] = viz_agent
        
        bond_agent = MagicMock(name="BondAnalysisAgentMock")
        bond_agent.process_message = AsyncMock(return_value={"reply": "Bond analysis: C-C bond broken, C-O bond formed"})
        agents["BondAnalysisAgent"] = bond_agent
        
        name_agent = MagicMock(name="NameConversionAgentMock")
        async def mock_name_agent_process_side_effect(message_content_arg, session_id=None, **kwargs_passed):
            identifier = message_content_arg.get("identifier")
            if identifier == "ethanol": return {"reply": "SMILES: CCO, Name: ethanol"}
            if identifier == "CCO": return {"reply": "Name: ethanol, SMILES: CCO"}
            return {"reply": "Unknown identifier for name conversion"}
        name_agent.process_message = AsyncMock(side_effect=mock_name_agent_process_side_effect)
        agents["NameConversionAgent"] = name_agent
        
        fg_agent = MagicMock(name="FuncGroupAgentMock")
        fg_agent.process_message = AsyncMock(return_value={"reply": "Functional groups: alcohol (-OH)"})
        agents["FuncGroupAgent"] = fg_agent
        
        props_agent = MagicMock(name="ChemicalPropsAgentMock")
        props_agent.process_message = AsyncMock(return_value={"reply": "Chemical properties: MW=46.07, BP=78.37°C, Density=0.789 g/mL"})
        agents["ChemicalPropsAgent"] = props_agent

        rxn_agent = MagicMock(name="ReactionClassifierAgentMock")
        rxn_agent.process_message = AsyncMock(return_value={"reply": "Reaction type: substitution, Conditions: room temperature"})
        agents["ReactionClassifierAgent"] = rxn_agent
        
        disc_agent = MagicMock(name="DisconnectionSuggesterAgentMock")
        disc_agent.process_message = AsyncMock(return_value={"reply": "Suggested disconnections: C-C bond at position 2"})
        agents["DisconnectionSuggesterAgent"] = disc_agent

        return agents
    
    def _create_orchestrator_with_mocks(self, temp_dir, current_mock_settings, 
                                        mock_get_settings_patch, mock_get_agent_patch,
                                        current_mock_agent_registry,
                                        MockAssistantAgent_patch, MockUserProxyAgent_patch):
        mock_get_settings_patch.return_value = current_mock_settings
        def get_agent_side_effect(name: str, **kwargs):
            if name in current_mock_agent_registry:
                return current_mock_agent_registry[name]
            generic_agent_mock = MagicMock(name=f"GenericMockAgent_{name}")
            generic_agent_mock.process_message = AsyncMock(return_value={"reply": f"Generic reply from {name}"})
            return generic_agent_mock
        mock_get_agent_patch.side_effect = get_agent_side_effect

        mock_assistant_instance = MagicMock(spec=autogen.AssistantAgent)
        mock_assistant_instance.llm_config = {"tools": []} 
        mock_assistant_instance.update_system_message = MagicMock()
        mock_assistant_instance.reset = MagicMock()
        MockAssistantAgent_patch.return_value = mock_assistant_instance

        mock_user_proxy_instance = MagicMock(spec=autogen.UserProxyAgent)
        mock_user_proxy_instance.chat_messages = {} 
        mock_user_proxy_instance.reset = MagicMock()
        mock_user_proxy_instance.a_initiate_chat = AsyncMock(return_value=None)
        MockUserProxyAgent_patch.return_value = mock_user_proxy_instance
        
        with patch('app.orchestration.root_agent.CFG_PROJECT_ROOT_DIR', temp_dir):
            orchestrator = RootAgentOrchestrator()
        orchestrator._save_analysis = Mock()
        return orchestrator, mock_user_proxy_instance, mock_assistant_instance

    @patch('app.orchestration.root_agent.get_agent_instance')
    @patch('app.orchestration.root_agent.get_settings')
    @patch('autogen.UserProxyAgent')
    @patch('autogen.AssistantAgent')
    def test_orchestrator_initialization(self, MockAssistantAgent_patch, MockUserProxyAgent_patch,
                                         mock_get_settings_patch, mock_get_agent_patch, 
                                         temp_dir_fixture, mock_settings_obj, mock_agent_registry_fixture):
        orchestrator, _, _ = self._create_orchestrator_with_mocks(
            temp_dir_fixture, mock_settings_obj, mock_get_settings_patch, mock_get_agent_patch, 
            mock_agent_registry_fixture, MockAssistantAgent_patch, MockUserProxyAgent_patch
        )
        assert hasattr(orchestrator, 'analysis_output_dir')
        assert orchestrator.analysis_output_dir == os.path.join(temp_dir_fixture, "analysis_outputs_hub")

    @patch('app.orchestration.root_agent.get_agent_instance')
    @patch('app.orchestration.root_agent.get_settings')
    @patch('autogen.UserProxyAgent')
    @patch('autogen.AssistantAgent')
    def test_smiles_extraction_reaction(self, MockAssistantAgent_patch, MockUserProxyAgent_patch,
                                        mock_get_settings_patch, mock_get_agent_patch, 
                                        temp_dir_fixture, mock_settings_obj, mock_agent_registry_fixture):
        orchestrator, _, _ = self._create_orchestrator_with_mocks(
            temp_dir_fixture, mock_settings_obj, mock_get_settings_patch, mock_get_agent_patch,
            mock_agent_registry_fixture, MockAssistantAgent_patch, MockUserProxyAgent_patch
        )
        query1 = "reaction smiles: CCO>>CC(=O)O"
        result1 = orchestrator._extract_reaction_smiles(query1)
        assert result1 == "CCO>>CC(=O)O"
    
    @patch('rdkit.Chem.MolFromSmiles')
    @patch('app.orchestration.root_agent.get_agent_instance')
    @patch('app.orchestration.root_agent.get_settings')
    @patch('autogen.UserProxyAgent')
    @patch('autogen.AssistantAgent')
    def test_smiles_extraction_single_compound(self, MockAssistantAgent_patch, MockUserProxyAgent_patch,
                                               mock_get_settings_patch, mock_get_agent_patch, mock_rdkit_mol_from_smiles,
                                               temp_dir_fixture, mock_settings_obj, mock_agent_registry_fixture):
        orchestrator, _, _ = self._create_orchestrator_with_mocks(
            temp_dir_fixture, mock_settings_obj, mock_get_settings_patch, mock_get_agent_patch,
            mock_agent_registry_fixture, MockAssistantAgent_patch, MockUserProxyAgent_patch
        )
        def mol_from_smiles_side_effect(smiles_str, sanitize=True):
            if smiles_str == "CCO":
                mock_mol = Mock()
                mock_mol.GetNumAtoms.return_value = 3
                return mock_mol
            return None 
        mock_rdkit_mol_from_smiles.side_effect = mol_from_smiles_side_effect
        query = "Tell me about CCO molecule"
        result = orchestrator._extract_single_compound_smiles(query)
        assert result == "CCO"
        mock_rdkit_mol_from_smiles.assert_any_call("CCO", sanitize=True)

    @pytest.mark.asyncio
    @patch('app.orchestration.root_agent.get_agent_instance')
    @patch('app.orchestration.root_agent.get_settings')
    @patch('autogen.UserProxyAgent')
    @patch('autogen.AssistantAgent')
    async def test_tool_invoke_visualization_agent(self, MockAssistantAgent_patch, MockUserProxyAgent_patch,
                                                   mock_get_settings_patch, mock_get_agent_patch, 
                                                   temp_dir_fixture, mock_settings_obj, mock_agent_registry_fixture):
        orchestrator, _, _ = self._create_orchestrator_with_mocks(
            temp_dir_fixture, mock_settings_obj, mock_get_settings_patch, mock_get_agent_patch,
            mock_agent_registry_fixture, MockAssistantAgent_patch, MockUserProxyAgent_patch
        )
        result = await orchestrator._tool_invoke_visualization_agent("CCO")
        # Corrected assertion to match the mock's output and regex expectations
        assert "Visualization created. Path: /static/autogen_visualizations/test_viz.png" in result
        # Also assert that the specific agent was called as expected
        mock_agent_registry_fixture["VisualizationAgent"].process_message.assert_called_once_with(
            {"smiles_or_reaction": "CCO"}
        )

    @pytest.mark.asyncio
    @patch('app.orchestration.root_agent.get_agent_instance')
    @patch('app.orchestration.root_agent.get_settings')
    @patch('autogen.UserProxyAgent')
    @patch('autogen.AssistantAgent')
    async def test_tool_invoke_chemical_props_agent(self, MockAssistantAgent_patch, MockUserProxyAgent_patch,
                                                    mock_get_settings_patch, mock_get_agent_patch, 
                                                    temp_dir_fixture, mock_settings_obj, mock_agent_registry_fixture):
        orchestrator, _, _ = self._create_orchestrator_with_mocks(
            temp_dir_fixture, mock_settings_obj, mock_get_settings_patch, mock_get_agent_patch,
            mock_agent_registry_fixture, MockAssistantAgent_patch, MockUserProxyAgent_patch
        )
        result = await orchestrator._tool_invoke_chemical_props_agent("ethanol")
        assert "Chemical properties" in result

    @patch('app.orchestration.root_agent.get_agent_instance')
    @patch('app.orchestration.root_agent.get_settings')
    @patch('autogen.UserProxyAgent')
    @patch('autogen.AssistantAgent')
    def test_moi_context_management(self, MockAssistantAgent_patch, MockUserProxyAgent_patch,
                                    mock_get_settings_patch, mock_get_agent_patch, 
                                    temp_dir_fixture, mock_settings_obj, mock_agent_registry_fixture):
        orchestrator, mock_user_proxy_instance, mock_assistant_instance = self._create_orchestrator_with_mocks(
            temp_dir_fixture, mock_settings_obj, mock_get_settings_patch, mock_get_agent_patch,
            mock_agent_registry_fixture, MockAssistantAgent_patch, MockUserProxyAgent_patch
        )
        root_agent._orchestrator_moi_context["name"] = "test_name_moi"
        root_agent._orchestrator_moi_context["smiles"] = "test_smiles_moi"
        orchestrator.clear_moi_context() 
        assert root_agent._orchestrator_moi_context["name"] is None
        assert root_agent._orchestrator_moi_context["smiles"] is None

    @pytest.mark.asyncio
    @patch('app.orchestration.root_agent.get_agent_instance')
    @patch('app.orchestration.root_agent.get_settings')
    @patch('autogen.UserProxyAgent')
    @patch('autogen.AssistantAgent')
    async def test_moi_priming_query(self, MockAssistantAgent_patch, MockUserProxyAgent_patch,
                                     mock_get_settings_patch, mock_get_agent_patch, 
                                     temp_dir_fixture, mock_settings_obj, mock_agent_registry_fixture):
        orchestrator, _, mock_assistant_instance = self._create_orchestrator_with_mocks(
            temp_dir_fixture, mock_settings_obj, mock_get_settings_patch, mock_get_agent_patch,
            mock_agent_registry_fixture, MockAssistantAgent_patch, MockUserProxyAgent_patch
        )
        priming_query = "Let's discuss the molecule of interest: ethanol with SMILES CCO. Please acknowledge."
        result = await orchestrator.route_query(priming_query)
        assert result["current_moi_name"] == "ethanol"
        assert result["current_moi_smiles"] == "CCO"
        assert root_agent._orchestrator_moi_context["name"] == "ethanol"
        assert root_agent._orchestrator_moi_context["smiles"] == "CCO"

    @patch('app.orchestration.root_agent.get_agent_instance')
    @patch('app.orchestration.root_agent.get_settings')
    @patch('autogen.UserProxyAgent')
    @patch('autogen.AssistantAgent')
    def test_filename_sanitization(self, MockAssistantAgent_patch, MockUserProxyAgent_patch,
                                   mock_get_settings_patch, mock_get_agent_patch, 
                                   temp_dir_fixture, mock_settings_obj, mock_agent_registry_fixture):
        orchestrator, _, _ = self._create_orchestrator_with_mocks(
            temp_dir_fixture, mock_settings_obj, mock_get_settings_patch, mock_get_agent_patch,
            mock_agent_registry_fixture, MockAssistantAgent_patch, MockUserProxyAgent_patch
        )
        result = orchestrator._sanitize_filename_part("test/molecule:name*with?special<chars>")
        assert result == "test_molecule_name_with_special_chars_"

    @patch('app.orchestration.root_agent.get_agent_instance')
    @patch('app.orchestration.root_agent.get_settings')
    @patch('autogen.UserProxyAgent') 
    @patch('autogen.AssistantAgent') 
    def test_save_analysis(self, MockAssistantAgent_patch, MockUserProxyAgent_patch, 
                           mock_get_settings_patch, mock_get_agent_patch, 
                           temp_dir_fixture, mock_settings_obj, mock_agent_registry_fixture):
        mock_get_settings_patch.return_value = mock_settings_obj
        mock_get_agent_patch.side_effect = lambda name, **kwargs: mock_agent_registry_fixture.get(name, MagicMock())
        
        mock_asst_inst = MagicMock(spec=autogen.AssistantAgent, llm_config={"tools":[]}) # Copied from _create_orchestrator...
        mock_asst_inst.update_system_message = MagicMock()
        mock_asst_inst.reset = MagicMock()
        MockAssistantAgent_patch.return_value = mock_asst_inst
        
        mock_usr_proxy_inst = MagicMock(spec=autogen.UserProxyAgent)  # Copied from _create_orchestrator...
        mock_usr_proxy_inst.chat_messages = {}
        mock_usr_proxy_inst.reset = MagicMock()
        MockUserProxyAgent_patch.return_value = mock_usr_proxy_inst

        with patch('app.orchestration.root_agent.CFG_PROJECT_ROOT_DIR', temp_dir_fixture):
            orchestrator = RootAgentOrchestrator()
        orchestrator._save_analysis(
            entity_identifier="CCO", analysis_text="Test analysis content",
            query_context_type="test_context", original_name="ethanol"
        )
        expected_output_dir = os.path.join(temp_dir_fixture, "analysis_outputs_hub")
        assert os.path.exists(expected_output_dir)
        files = os.listdir(expected_output_dir) 
        assert len(files) == 1
        assert files[0].startswith("ethanol_CCO_test_context_")
        with open(os.path.join(expected_output_dir, files[0]), 'r') as f:
            content = f.read()
            assert "Test analysis content" in content

    @pytest.mark.asyncio
    @patch('app.orchestration.root_agent.get_agent_instance')
    @patch('app.orchestration.root_agent.get_settings')
    @patch('autogen.UserProxyAgent')
    @patch('autogen.AssistantAgent')
    async def test_route_query_output_for_specific_query(
        self, MockAssistantAgent_patch, MockUserProxyAgent_patch,
        mock_get_settings_patch, mock_get_agent_patch,
        temp_dir_fixture, mock_settings_obj, mock_agent_registry_fixture
    ):
        orchestrator, mock_user_proxy_instance, _ = self._create_orchestrator_with_mocks(
            temp_dir_fixture, mock_settings_obj, mock_get_settings_patch, mock_get_agent_patch,
            mock_agent_registry_fixture, MockAssistantAgent_patch, MockUserProxyAgent_patch
        )
        query = "What are the chemical properties of CCO and please visualize it?"
        
        expected_analysis_text_from_assistant_main_part = "Synthesized response: Chemical properties of CCO are MW=46.07, BP=78.37°C, Density=0.789 g/mL. Visualization available."
        expected_viz_path_in_result = "/static/autogen_visualizations/test_viz.png" 
        
        # Assistant's final message includes the path textually
        final_assistant_message_content = f"{expected_analysis_text_from_assistant_main_part} The image is at Path: {expected_viz_path_in_result}. TERMINATE"
        # Expected analysis text in the result will be the above, minus "TERMINATE"
        expected_final_analysis_in_result = f"{expected_analysis_text_from_assistant_main_part} The image is at Path: {expected_viz_path_in_result}."


        tool_call_output_viz_agent_reply = (await mock_agent_registry_fixture["VisualizationAgent"].process_message({"smiles_or_reaction":"CCO"}))["reply"]
        
        async def mock_initiate_chat_effect(*args, **kwargs):
            assistant_for_key = kwargs.get('recipient')
            mock_user_proxy_instance.chat_messages[assistant_for_key] = [
                {"role": "user", "content": query},
                {"role": "assistant", "content": None, "tool_calls": [ 
                    {"id": "call_viz_xyz", "type": "function", 
                     "function": {"name": "_tool_invoke_visualization_agent", "arguments": '{"smiles_or_reaction": "CCO"}'}}
                ]},
                {"role": "tool", "tool_call_id": "call_viz_xyz", "name": "_tool_invoke_visualization_agent", 
                 "content": tool_call_output_viz_agent_reply },
                {"role": "assistant", "content": final_assistant_message_content, "tool_calls": None} 
            ]
            return None
        mock_user_proxy_instance.a_initiate_chat = AsyncMock(side_effect=mock_initiate_chat_effect)
        result = await orchestrator.route_query(query)
        
        assert result["analysis"] == expected_final_analysis_in_result
        assert result["visualization_path"] == expected_viz_path_in_result
        orchestrator._save_analysis.assert_called_once()

    @pytest.mark.asyncio
    @patch('app.orchestration.root_agent.get_agent_instance')
    @patch('app.orchestration.root_agent.get_settings')
    @patch('autogen.UserProxyAgent')
    @patch('autogen.AssistantAgent')
    async def test_route_query_name_to_smiles(
        self, MockAssistantAgent_patch, MockUserProxyAgent_patch,
        mock_get_settings_patch, mock_get_agent_patch,
        temp_dir_fixture, mock_settings_obj, mock_agent_registry_fixture
    ):
        orchestrator, mock_user_proxy_instance, _ = self._create_orchestrator_with_mocks(
            temp_dir_fixture, mock_settings_obj, mock_get_settings_patch, mock_get_agent_patch,
            mock_agent_registry_fixture, MockAssistantAgent_patch, MockUserProxyAgent_patch
        )
        query = "What is the SMILES for ethanol?"
        agent_call_result = await mock_agent_registry_fixture["NameConversionAgent"].process_message(
            {"identifier": "ethanol"} 
        )
        expected_agent_tool_reply_str = agent_call_result["reply"]
        final_synthesized_response_content = f"After careful lookup using advanced chemical databases, the SMILES string for the common chemical ethanol is definitively CCO. TERMINATE"

        async def mock_initiate_chat_effect_name_to_smiles(*args, **kwargs):
            assistant_for_key = kwargs.get('recipient')
            mock_user_proxy_instance.chat_messages[assistant_for_key] = [
                {"role": "user", "content": query},
                {"role": "assistant", "content": None, "tool_calls": [
                    {"id": "call_xyz_name", "type": "function", 
                     "function": {"name": "_tool_invoke_name_conversion_agent", "arguments": '{"identifier": "ethanol"}'}}
                ]},
                {"role": "tool", "tool_call_id": "call_xyz_name", "name": "_tool_invoke_name_conversion_agent", 
                 "content": expected_agent_tool_reply_str },
                {"role": "assistant", "content": final_synthesized_response_content, "tool_calls": None}
            ]
            return None
        mock_user_proxy_instance.a_initiate_chat = AsyncMock(side_effect=mock_initiate_chat_effect_name_to_smiles)
        result = await orchestrator.route_query(query)
        assert result["analysis"] == "After careful lookup using advanced chemical databases, the SMILES string for the common chemical ethanol is definitively CCO."
        mock_agent_registry_fixture["NameConversionAgent"].process_message.assert_any_call(
            {"identifier": "ethanol"} 
        )
        orchestrator._save_analysis.assert_called_once()

    @pytest.mark.asyncio
    @patch('app.orchestration.root_agent.get_agent_instance')
    @patch('app.orchestration.root_agent.get_settings')
    @patch('autogen.UserProxyAgent')
    @patch('autogen.AssistantAgent')
    async def test_route_query_smiles_to_name(
        self, MockAssistantAgent_patch, MockUserProxyAgent_patch,
        mock_get_settings_patch, mock_get_agent_patch,
        temp_dir_fixture, mock_settings_obj, mock_agent_registry_fixture
    ):
        orchestrator, mock_user_proxy_instance, _ = self._create_orchestrator_with_mocks(
            temp_dir_fixture, mock_settings_obj, mock_get_settings_patch, mock_get_agent_patch,
            mock_agent_registry_fixture, MockAssistantAgent_patch, MockUserProxyAgent_patch
        )
        query = "What is the name of CCO?"
        agent_call_result = await mock_agent_registry_fixture["NameConversionAgent"].process_message(
           {"identifier": "CCO"} 
        )
        expected_agent_tool_reply_str = agent_call_result["reply"]
        final_synthesized_response_content = f"The chemical name for the SMILES string CCO, as determined by our systems, is ethanol. TERMINATE"

        async def mock_initiate_chat_effect_smiles_to_name(*args, **kwargs):
            assistant_for_key = kwargs.get('recipient')
            mock_user_proxy_instance.chat_messages[assistant_for_key] = [
                {"role": "user", "content": query},
                {"role": "assistant", "content": None, "tool_calls": [
                    {"id": "call_abc_smiles", "type": "function", 
                     "function": {"name": "_tool_invoke_name_conversion_agent", "arguments": '{"identifier": "CCO"}'}}
                ]},
                {"role": "tool", "tool_call_id": "call_abc_smiles", "name": "_tool_invoke_name_conversion_agent", 
                 "content": expected_agent_tool_reply_str},
                {"role": "assistant", "content": final_synthesized_response_content, "tool_calls": None}
            ]
            return None
        mock_user_proxy_instance.a_initiate_chat = AsyncMock(side_effect=mock_initiate_chat_effect_smiles_to_name)
        result = await orchestrator.route_query(query)
        assert result["analysis"] == "The chemical name for the SMILES string CCO, as determined by our systems, is ethanol."
        mock_agent_registry_fixture["NameConversionAgent"].process_message.assert_any_call(
            {"identifier": "CCO"} 
        )
        orchestrator._save_analysis.assert_called_once()

    @pytest.mark.asyncio
    @patch('app.orchestration.root_agent.get_agent_instance')
    @patch('app.orchestration.root_agent.get_settings')
    @patch('autogen.UserProxyAgent')
    @patch('autogen.AssistantAgent')
    async def test_route_query_bond_analysis(
        self, MockAssistantAgent_patch, MockUserProxyAgent_patch,
        mock_get_settings_patch, mock_get_agent_patch,
        temp_dir_fixture, mock_settings_obj, mock_agent_registry_fixture
    ):
        orchestrator, mock_user_proxy_instance, _ = self._create_orchestrator_with_mocks(
            temp_dir_fixture, mock_settings_obj, mock_get_settings_patch, mock_get_agent_patch,
            mock_agent_registry_fixture, MockAssistantAgent_patch, MockUserProxyAgent_patch
        )
        reaction_smiles = "CCO>>CC(=O)O"
        query = f"Analyze bond changes for reaction {reaction_smiles} using advanced methods."
        agent_call_result = await mock_agent_registry_fixture["BondAnalysisAgent"].process_message(
            {"reaction_smiles": reaction_smiles}
        )
        expected_agent_tool_reply_str = agent_call_result["reply"]
        final_synthesized_response_content = f"For the reaction {reaction_smiles}, the bond analysis shows: C-C bond broken, C-O bond formed. This is a detailed report. TERMINATE"

        async def mock_initiate_chat_effect_bond(*args, **kwargs):
            assistant_for_key = kwargs.get('recipient')
            mock_user_proxy_instance.chat_messages[assistant_for_key] = [
                {"role": "user", "content": query},
                {"role": "assistant", "content": None, "tool_calls": [
                    {"id": "call_bond_an", "type": "function", 
                     "function": {"name": "_tool_invoke_bond_analyzer_agent", "arguments": f'{{"reaction_smiles": "{reaction_smiles}"}}'}}
                ]},
                {"role": "tool", "tool_call_id": "call_bond_an", "name": "_tool_invoke_bond_analyzer_agent", 
                 "content": expected_agent_tool_reply_str},
                {"role": "assistant", "content": final_synthesized_response_content, "tool_calls": None}
            ]
            return None
        mock_user_proxy_instance.a_initiate_chat = AsyncMock(side_effect=mock_initiate_chat_effect_bond)
        result = await orchestrator.route_query(query)
        assert "C-C bond broken, C-O bond formed" in result["analysis"]
        mock_agent_registry_fixture["BondAnalysisAgent"].process_message.assert_called_once_with(
            {"reaction_smiles": reaction_smiles}
        )
        orchestrator._save_analysis.assert_called_once()

    @pytest.mark.asyncio
    @patch('app.orchestration.root_agent.get_agent_instance')
    @patch('app.orchestration.root_agent.get_settings')
    @patch('autogen.UserProxyAgent')
    @patch('autogen.AssistantAgent')
    async def test_route_query_functional_groups(
        self, MockAssistantAgent_patch, MockUserProxyAgent_patch,
        mock_get_settings_patch, mock_get_agent_patch,
        temp_dir_fixture, mock_settings_obj, mock_agent_registry_fixture
    ):
        orchestrator, mock_user_proxy_instance, _ = self._create_orchestrator_with_mocks(
            temp_dir_fixture, mock_settings_obj, mock_get_settings_patch, mock_get_agent_patch,
            mock_agent_registry_fixture, MockAssistantAgent_patch, MockUserProxyAgent_patch
        )
        smiles = "CCO"
        query = f"What are the functional groups in {smiles}? Please provide a comprehensive list."
        agent_call_result = await mock_agent_registry_fixture["FuncGroupAgent"].process_message(
            {"smiles_or_reaction": smiles}
        )
        expected_agent_tool_reply_str = agent_call_result["reply"]
        final_synthesized_response_content = f"The molecule {smiles} contains the following functional groups: alcohol (-OH). This is based on standard chemical definitions. TERMINATE"

        async def mock_initiate_chat_effect_fg(*args, **kwargs):
            assistant_for_key = kwargs.get('recipient')
            mock_user_proxy_instance.chat_messages[assistant_for_key] = [
                {"role": "user", "content": query},
                {"role": "assistant", "content": None, "tool_calls": [
                    {"id": "call_fg_id", "type": "function", 
                     "function": {"name": "_tool_invoke_func_group_agent", "arguments": f'{{"smiles_or_reaction": "{smiles}"}}'}}
                ]},
                {"role": "tool", "tool_call_id": "call_fg_id", "name": "_tool_invoke_func_group_agent", 
                 "content": expected_agent_tool_reply_str},
                {"role": "assistant", "content": final_synthesized_response_content, "tool_calls": None}
            ]
            return None
        mock_user_proxy_instance.a_initiate_chat = AsyncMock(side_effect=mock_initiate_chat_effect_fg)
        result = await orchestrator.route_query(query)
        assert "functional groups: alcohol (-OH)" in result["analysis"]
        mock_agent_registry_fixture["FuncGroupAgent"].process_message.assert_called_once_with(
             {"smiles_or_reaction": smiles}
        )
        orchestrator._save_analysis.assert_called_once()

# --- Standalone Test Functions ---
@patch('autogen.AssistantAgent')
@patch('autogen.UserProxyAgent')
@patch('app.orchestration.root_agent.get_agent_instance')
@patch('app.orchestration.root_agent.get_settings')
def test_basic_functionality_standalone(mock_get_settings_s, mock_get_agent_s, MockUserProxyAgent_s, MockAssistantAgent_s):
    print("\n--- Testing basic orchestrator functionality (standalone) ---")
    settings_obj_s = MagicMock(spec=Settings)
    settings_obj_s.OPENAI_API_KEY = "test-key_ok_standalone" 
    settings_obj_s.CHATBOT_AGENT_TIMEOUT = 30
    settings_obj_s.MAX_CHATBOT_TURNS = 10
    settings_obj_s.VISUALIZATION_SUBDIR_NAME = "autogen_visualizations" 
    mock_get_settings_s.return_value = settings_obj_s
    mock_agent_instance_s = MagicMock()
    mock_agent_instance_s.process_message = AsyncMock(return_value={"reply": "Test response from standalone"})
    mock_get_agent_s.return_value = mock_agent_instance_s
    mock_asst_autogen_s = MagicMock(spec=autogen.AssistantAgent)
    mock_asst_autogen_s.llm_config = {"tools": []} 
    mock_asst_autogen_s.update_system_message = MagicMock()
    mock_asst_autogen_s.reset = MagicMock()
    MockAssistantAgent_s.return_value = mock_asst_autogen_s
    mock_usr_proxy_autogen_s = MagicMock(spec=autogen.UserProxyAgent)
    mock_usr_proxy_autogen_s.chat_messages = {}
    mock_usr_proxy_autogen_s.reset = MagicMock()
    MockUserProxyAgent_s.return_value = mock_usr_proxy_autogen_s
    with tempfile.TemporaryDirectory() as temp_dir_standalone:
        type(settings_obj_s).APP_STATIC_DIR_ABS = PropertyMock(return_value=os.path.join(temp_dir_standalone, "static_standalone"))
        type(settings_obj_s).VISUALIZATION_DIR_ABS = PropertyMock(return_value=os.path.join(settings_obj_s.APP_STATIC_DIR_ABS, settings_obj_s.VISUALIZATION_SUBDIR_NAME))
        os.makedirs(settings_obj_s.VISUALIZATION_DIR_ABS, exist_ok=True)
        with patch('app.orchestration.root_agent.CFG_PROJECT_ROOT_DIR', temp_dir_standalone):
            orchestrator_s = RootAgentOrchestrator()
    print("✓ Orchestrator created successfully (standalone)")
    reaction_smiles = orchestrator_s._extract_reaction_smiles("reaction: CCO>>CC(=O)O")
    assert reaction_smiles == "CCO>>CC(=O)O", f"Expected 'CCO>>CC(=O)O', got '{reaction_smiles}'"
    print("✓ SMILES extraction working (standalone)")
    sanitized = orchestrator_s._sanitize_filename_part("test/file:name")
    assert sanitized == "test_file_name", f"Expected 'test_file_name', got '{sanitized}'"
    print("✓ Filename sanitization working (standalone)")
    print("--- ✓ All basic standalone tests passed! ---")


@pytest.mark.asyncio 
@patch('autogen.AssistantAgent')
@patch('autogen.UserProxyAgent')
@patch('app.orchestration.root_agent.get_agent_instance')
@patch('app.orchestration.root_agent.get_settings')
async def test_async_functionality_standalone(mock_get_settings_s, mock_get_agent_s, MockUserProxyAgent_s, MockAssistantAgent_s):
    print("\n--- Testing async functionality (standalone) ---")
    settings_obj_s = MagicMock(spec=Settings)
    settings_obj_s.OPENAI_API_KEY = "test-key_ok_async_standalone"
    settings_obj_s.CHATBOT_AGENT_TIMEOUT = 30
    settings_obj_s.MAX_CHATBOT_TURNS = 10
    settings_obj_s.VISUALIZATION_SUBDIR_NAME = "autogen_visualizations"
    mock_get_settings_s.return_value = settings_obj_s

    viz_agent_mock_s = MagicMock(name="VizAgentStandaloneMock")
    viz_agent_mock_s.process_message = AsyncMock(return_value={"reply": "Test viz response. Path: /static/autogen_visualizations/viz_standalone.png", "visualization_path": "/static/autogen_visualizations/viz_standalone.png"}) # Corrected path
    name_conv_agent_mock_s = MagicMock(name="NameConvAgentStandaloneMock")
    name_conv_agent_mock_s.process_message = AsyncMock(return_value={"reply": "Name: Ethanol, SMILES: CCO from standalone mock"})
    def side_effect_get_agent_s(name, **kwargs):
        if name == "VisualizationAgent": return viz_agent_mock_s
        if name == "NameConversionAgent": return name_conv_agent_mock_s
        generic_mock = MagicMock(name=f"GenericStandaloneMock_{name}")
        generic_mock.process_message = AsyncMock(return_value={"reply": f"Generic reply from {name} standalone"})
        return generic_mock
    mock_get_agent_s.side_effect = side_effect_get_agent_s
    
    mock_asst_autogen_s = MagicMock(spec=autogen.AssistantAgent)
    mock_asst_autogen_s.llm_config = {"tools": []} 
    mock_asst_autogen_s.update_system_message = MagicMock()
    mock_asst_autogen_s.reset = MagicMock()
    MockAssistantAgent_s.return_value = mock_asst_autogen_s
    
    mock_usr_proxy_autogen_s = MagicMock(spec=autogen.UserProxyAgent)
    mock_usr_proxy_autogen_s.chat_messages = {}
    mock_usr_proxy_autogen_s.reset = MagicMock()
    MockUserProxyAgent_s.return_value = mock_usr_proxy_autogen_s

    with tempfile.TemporaryDirectory() as temp_dir_standalone:
        type(settings_obj_s).APP_STATIC_DIR_ABS = PropertyMock(return_value=os.path.join(temp_dir_standalone, "static_standalone_async"))
        type(settings_obj_s).VISUALIZATION_DIR_ABS = PropertyMock(return_value=os.path.join(settings_obj_s.APP_STATIC_DIR_ABS, settings_obj_s.VISUALIZATION_SUBDIR_NAME))
        os.makedirs(settings_obj_s.VISUALIZATION_DIR_ABS, exist_ok=True)
        with patch('app.orchestration.root_agent.CFG_PROJECT_ROOT_DIR', temp_dir_standalone):
            orchestrator_s = RootAgentOrchestrator()
    
    orchestrator_s._save_analysis = Mock()
    
    result_tool_invoke = await orchestrator_s._tool_invoke_visualization_agent("CCO")
    assert "Path: /static/autogen_visualizations/viz_standalone.png" in result_tool_invoke # Check for corrected path
    print("✓ Tool invocation working (standalone)")
    
    priming_query = "Let's discuss the molecule of interest: ethanol with SMILES CCO. Please acknowledge."
    result_moi = await orchestrator_s.route_query(priming_query)
    assert result_moi["current_moi_name"] == "ethanol"
    print("✓ MOI priming working (standalone)")

    query_simple = "What is CCO? Give me a detailed answer about this specific chemical." 
    final_response_from_assistant = "The chemical compound CCO is ethanol, a simple alcohol. It is widely used. TERMINATE" 
    async def mock_initiate_chat_simple(*args, **kwargs):
        mock_usr_proxy_autogen_s.chat_messages[kwargs.get('recipient')] = [
            {"role": "user", "content": query_simple},
            {"role": "assistant", "content": final_response_from_assistant}
        ]
        return None
    mock_usr_proxy_autogen_s.a_initiate_chat = AsyncMock(side_effect=mock_initiate_chat_simple)
    result_simple_query = await orchestrator_s.route_query(query_simple)
    assert result_simple_query["analysis"] == "The chemical compound CCO is ethanol, a simple alcohol. It is widely used."
    print("✓ Simple route_query working (standalone)")
    orchestrator_s._save_analysis.assert_called()
    print("--- ✓ All async standalone tests passed! ---")


def run_all_standalone_tests():
    print("\nRunning all standalone tests...")
    all_passed_standalone = True
    try:
        test_basic_functionality_standalone()
    except AssertionError as e:
        print(f"✗ Basic standalone functionality test FAILED: {e}")
        all_passed_standalone = False
    except Exception as e:
        print(f"✗ Basic standalone functionality test FAILED with EXCEPTION: {e}")
        import traceback; traceback.print_exc()
        all_passed_standalone = False
    try:
        asyncio.run(test_async_functionality_standalone())
    except AssertionError as e:
        print(f"✗ Async standalone functionality test FAILED: {e}")
        all_passed_standalone = False
    except Exception as e:
        print(f"✗ Async standalone functionality test FAILED with EXCEPTION: {e}")
        import traceback; traceback.print_exc()
        all_passed_standalone = False
    if all_passed_standalone:
        print("\n✓✓✓ ALL STANDALONE TESTS COMPLETED SUCCESSFULLY! ✓✓✓")
    else:
        print("\nXXX SOME STANDALONE TESTS FAILED! XXX")
    return all_passed_standalone
        
if __name__ == "__main__":
    print("=" * 70)
    print("RootAgentOrchestrator Test Suite (Standalone Run - Not using Pytest runner)")
    print("=" * 70)
    success_standalone = run_all_standalone_tests()
    print("\n" + "=" * 70)
    if success_standalone: print("✓ ALL STANDALONE CHECKS PASSED!") 
    else: print("✗ SOME STANDALONE CHECKS FAILED!")  
    print("=" * 70)
    print("\nFor full test suite, run with pytest from the AGENT-HUB project root directory:")
    print("pytest tests/test_agents.py -v -s")