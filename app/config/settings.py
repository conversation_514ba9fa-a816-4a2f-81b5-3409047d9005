import os
from typing import Optional, List
from pydantic_settings import BaseSettings, SettingsConfigDict
from dotenv import load_dotenv
import logging

# --- Determine Project Root and .env Path ---
_MODULE_FILE_PATH = os.path.abspath(__file__)
_CONFIG_DIR = os.path.dirname(_MODULE_FILE_PATH)
_APP_DIR_FOR_SETTINGS = os.path.dirname(_CONFIG_DIR) 
_PROJECT_ROOT_DIR_CALCULATED = os.path.dirname(_APP_DIR_FOR_SETTINGS)

ENV_FILE_PATH = os.path.join(_PROJECT_ROOT_DIR_CALCULATED, '.env')

if os.path.exists(ENV_FILE_PATH):
    load_dotenv(dotenv_path=ENV_FILE_PATH)
else:
    print(f"Warning: .env file not found at {ENV_FILE_PATH}. Relying on system environment variables.")

class Settings(BaseSettings):
    APP_NAME: str = "AGENT-HUB ChemCopilot"
    API_VERSION: str = "1.0.1" # Updated version

    OPENAI_API_KEY: Optional[str] = None
    PERPLEXITY_API_KEY: Optional[str] = None
    DEFAULT_LLM_PROVIDER: str = "openai"
    OPENAI_API_BASE_URL: Optional[str] = None
    OPENAI_API_TYPE: str = "azure"
    OPENAI_API_VERSION: str = "2024-12-01-preview"
    AZURE_OPENAI_API_KEY: Optional[str] = None
    AZURE_OPENAI_ENDPOINT: Optional[str] = None
    
    # Specific model names for different components
    OPENAI_CHAT_MODEL: str = "gpt-4o"  # General purpose chat model
    OPENAI_CHAT_MODEL_ORCHESTRATOR: str = "gpt-4o" # Model for the RootAgentOrchestrator's Autogen assistant
    OPENAI_EMBEDDING_MODEL: str = "text-embedding-ada-002"
    PERPLEXITY_CHAT_MODEL: str = "llama-3-sonar-large-32k-online" # Default for Perplexity

    _APP_DATA_DIR_NAME: str = "data"
    REACTION_DATASET_FILENAME1: str = "orderly_retro.parquet"
    REACTION_DATASET_FILENAME2: str = "reaction_classification_checkpoint.parquet"
    PRICING_FILENAME_PRIMARY: str = "pricing_data.json"
    PRICING_FILENAME_SECONDARY: str = "second_source.json"
    PRICING_FILENAME_TERTIARY: str = "sigma_source.json"
    SMARTS_FILENAME: str = "open_babel.txt"
    GEMINI_API_KEY: Optional[str] = None
    AZURE_SEARCH_SERVICE_NAME: str = "mstack-reactions-search-service"
    AZURE_SEARCH_API_KEY: Optional[str] = None
    SERP_API_KEY: Optional[str] = None
    REDIS_URL : str = "redis://localhost:6379/0"  # Default Redis URL, can be overridden in .env
    PATENT_INPUT_REDIS_QUEUE: str = "patent_input_redis_queue"
    PATENT_OUTPUT_REDIS_QUEUE: str = "patent_output_redis_queue"
    MONGO_URL: str = "**************************************"  # Default MongoDB URL, can be overridden in .env
    AZURE_PATENT_SUMMARY_INDEX_NAME: str = "patent_summary_data"
    AZURE_RAW_PATENT_DATA_INDEX_NAME: str = "raw_patents_index"
    AZURE_PATENT_DATA_INDEX_NAME: str = "patent_data"
    SEARCH_PROVIDER: str = "azure"  # Default search provider

    ENRICH_INPUT_REDIS_QUEUE : str =  'enrich_input_job_queue'
    ENRICH_OUTPUT_REDIS_QUEUE : str =  'enrich_output_job_queue'

    AZURE_BLOB_CONNECTION_STRING: Optional[str] = None
    AZURE_BLOB_CONTAINER_NAME: str = "literature-data-store"

    _APP_STATIC_DIR_NAME: str = "static"
    VISUALIZATION_SUBDIR_NAME: str = "autogen_visualizations"
    AZURE_ASKOS_HOSTNAME : str = "************"
    REACTION_CLASSIFIER_API_URL: str = "http://{AZURE_HOSTNAME}:9621/reaction_class"
    CAS_SEARCH_URL_TEMPLATE: str = "https://commonchemistry.cas.org/api/search?q={query}"
    CAS_DETAIL_URL_TEMPLATE: str = "https://commonchemistry.cas.org/api/detail?cas_rn={cas_rn}"
    PUBCHEM_NAME_PROP_URL_TEMPLATE: str = "https://pubchem.ncbi.nlm.nih.gov/rest/pug/compound/name/{query}/property/IsomericSMILES,CanonicalSMILES/JSON"
    PUBCHEM_NAME_CID_URL_TEMPLATE: str = "https://pubchem.ncbi.nlm.nih.gov/rest/pug/compound/name/{query}/cids/JSON"
    PUBCHEM_CID_PROP_URL_TEMPLATE: str = "https://pubchem.ncbi.nlm.nih.gov/rest/pug/compound/cid/{cid}/property/IsomericSMILES,CanonicalSMILES/JSON"
    CACTUS_SMILES_TO_NAME_URL_TEMPLATE: str = "https://cactus.nci.nih.gov/chemical/structure/{smiles}/iupac_name"
    PUBCHEM_SMILES_PROP_URL_TEMPLATE: str = "https://pubchem.ncbi.nlm.nih.gov/rest/pug/compound/smiles/{smiles}/property/IUPACName/JSON"

    USD_TO_INR_RATE: float = 83.0
    HTTP_REQUEST_TIMEOUT: int = 30 # Default timeout for external HTTP requests
    TOOL_AGENT_TIMEOUT: int = 120 
    CHATBOT_AGENT_TIMEOUT: int = 180
    MAX_CHATBOT_TURNS: int = 7 

    model_config = SettingsConfigDict(env_file=ENV_FILE_PATH, env_file_encoding='utf-8', extra='ignore')

    @property
    def APP_DIR_ABS(self) -> str: return _PROJECT_ROOT_DIR_CALCULATED
    @property
    def APP_DATA_DIR_ABS(self) -> str: return os.path.join(self.APP_DIR_ABS, self._APP_DATA_DIR_NAME)
    @property
    def REACTION_DATASET_PATH1(self) -> str: return os.path.join(self.APP_DATA_DIR_ABS, self.REACTION_DATASET_FILENAME1)
    @property
    def REACTION_DATASET_PATH2(self) -> str: return os.path.join(self.APP_DATA_DIR_ABS, self.REACTION_DATASET_FILENAME2)
    @property
    def PRICING_FILE_PRIMARY(self) -> str: return os.path.join(self.APP_DATA_DIR_ABS, self.PRICING_FILENAME_PRIMARY)
    @property
    def PRICING_FILE_SECONDARY(self) -> str: return os.path.join(self.APP_DATA_DIR_ABS, self.PRICING_FILENAME_SECONDARY)
    @property
    def PRICING_FILE_TERTIARY(self) -> str: return os.path.join(self.APP_DATA_DIR_ABS, self.PRICING_FILENAME_TERTIARY)
    @property
    def OPEN_BABEL_SMARTS_FILE(self) -> str: return os.path.join(self.APP_DATA_DIR_ABS, self.SMARTS_FILENAME)
    @property
    def APP_STATIC_DIR_ABS(self) -> str:
        path = os.path.join(self.APP_DIR_ABS, self._APP_STATIC_DIR_NAME); os.makedirs(path, exist_ok=True); return path
    @property
    def VISUALIZATION_DIR_ABS(self) -> str:
        path = os.path.join(self.APP_STATIC_DIR_ABS, self.VISUALIZATION_SUBDIR_NAME); os.makedirs(path, exist_ok=True); return path
    @property
    def VISUALIZATION_WEB_BASE_PATH(self) -> str: return f"/static/{self.VISUALIZATION_SUBDIR_NAME}"

PROJECT_ROOT_DIR = _PROJECT_ROOT_DIR_CALCULATED # Expose for other modules if needed
_settings_instance: Optional[Settings] = None

def get_settings() -> Settings:
    global _settings_instance
    if _settings_instance is None:
        try:
            _settings_instance = Settings()
            # Critical API Key Validation
            # if not _settings_instance.OPENAI_API_KEY or \
            #    any(placeholder in _settings_instance.OPENAI_API_KEY.lower() for placeholder in ["your_openai_key", "none", "sk-"]): # Basic check for actual key vs placeholder
            #     # More robust check if sk- is present but very short or clearly invalid
            #     if not (_settings_instance.OPENAI_API_KEY.startswith("sk-") and len(_settings_instance.OPENAI_API_KEY) > 30):
            #         print( # Changed to print, will be caught by FastAPI startup if needed
            #             "CRITICAL WARNING: OPENAI_API_KEY appears to be missing, a placeholder, or invalid. "
            #             "Please provide it in the .env file or as an environment variable."
            #         )
            if _settings_instance.DEFAULT_LLM_PROVIDER == "perplexity" and \
               (not _settings_instance.PERPLEXITY_API_KEY or "your_perplexity_api_key" in _settings_instance.PERPLEXITY_API_KEY.lower()):
                 print(
                    "Warning: PERPLEXITY_API_KEY is not set correctly, but Perplexity is the default LLM provider. "
                    "Perplexity calls may fail."
                )
        except Exception as e:
            # Log more visibly or raise a specific configuration error
            initial_error_logger = logging.getLogger("app.config.settings") # Use a specific logger for settings errors
            initial_error_logger.critical(f"FATAL ERROR during Settings instantiation: {e}", exc_info=True)
            raise RuntimeError(f"Could not initialize application settings: {e}") from e # Re-raise to halt app startup
    return _settings_instance

try:
    settings_on_import = get_settings()
except RuntimeError: # Catch the specific RuntimeError from get_settings()
    # This block will execute if get_settings() raises the RuntimeError
    # Allow sys.exit only if not in a reload loop to avoid Uvicorn issues
    if os.environ.get("FASTAPI_RUNNING_AS_WORKER") or not os.environ.get("UVORN_RELOAD"): # Heuristic for main process
        import sys
        sys.exit(1) # Exit if settings fail critically on first load
    # If in a reload worker, just let the error propagate for Uvicorn to handle
    raise
