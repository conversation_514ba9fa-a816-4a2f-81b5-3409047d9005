"""Patent processing agent implementation."""

from typing import Dict, Any, List, Optional
from ..base import BaseAgent
from ...tools.patent.patent_processor import PatentProcessor
from ...tools.patent.chemical_converter import ChemicalConverter
from ...tools.patent.patent_preprocessor import PatentPreprocessor
from ...core.exceptions import PatentProcessingError
from ...models.patent_models import ProcessingStatus

class PatentProcessorAgent(BaseAgent):
    """Agent for processing patent documents."""
    
    name = "patent_processor"
    description = "Processes patents to extract chemical reactions"
    
    def __init__(self):
        super().__init__()
        # Register tools
        self.register_tool(PatentProcessor())
        self.register_tool(ChemicalConverter())
        self.register_tool(PatentPreprocessor())
    
    async def execute(self, **kwargs) -> Dict[str, Any]:
        """Execute patent processing workflow."""
        try:
            self.logger.info(f"Processing patent with parameters: {kwargs}")
            
            # Validate inputs
            if not self._validate_inputs(kwargs):
                raise PatentProcessingError("Missing required parameters")
            
            # Get required tools
            processor = self.get_tool("patent_processor")
            converter = self.get_tool("chemical_converter")
            preprocessor = self.get_tool("patent_preprocessor")
            
            # Extract parameters
            patent_id = kwargs.get("patent_id")
            material = kwargs.get("material")
            content = kwargs.get("content")
            model_type = kwargs.get("model_type", "gemini")
            
            # Step 1: Convert chemical if provided
            smiles = None
            if material:
                success, result = converter.convert(material)
                if success:
                    smiles = result
                    self.logger.info(f"Converted {material} to SMILES: {smiles}")
                else:
                    self.logger.warning(f"Chemical conversion failed: {result}")
            
            # Step 2: Preprocess patent
            if kwargs.get("enable_preprocessing", True):
                preprocess_result = await preprocessor.analyze_patent_relevance(
                    patent_id=patent_id,
                    chemical_name=material,
                    smiles=smiles,
                    patent_details={"content": content}
                )
                
                # Skip processing if patent is not relevant
                if not preprocess_result.get("isPatentRelated", False):
                    return {
                        "status": "skipped",
                        "reason": "Patent not relevant",
                        "preprocessing_result": preprocess_result
                    }
            
            # Step 3: Process patent
            process_result = await processor.process_patent(
                patent_id=patent_id,
                patent_content=content
            )
            
            return {
                "status": "success",
                "patent_id": patent_id,
                "material": material,
                "smiles": smiles,
                "preprocessing_result": preprocess_result if kwargs.get("enable_preprocessing") else None,
                "processing_result": process_result
            }
            
        except Exception as e:
            self.logger.error(f"Patent processing failed: {str(e)}")
            raise PatentProcessingError(f"Patent processing failed: {str(e)}")
    
    def _validate_inputs(self, params: Dict[str, Any]) -> bool:
        """Validate input parameters."""
        return bool(params.get("patent_id") and params.get("content"))
    
    async def plan(self, task: str) -> List[Dict[str, Any]]:
        """Plan patent processing steps."""
        return [
            {"step": 1, "action": "convert_chemical", "tool": "chemical_converter"},
            {"step": 2, "action": "preprocess_patent", "tool": "patent_preprocessor"},
            {"step": 3, "action": "process_patent", "tool": "patent_processor"},
            {"step": 4, "action": "format_results", "tool": "patent_processor"}
        ]