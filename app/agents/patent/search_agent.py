"""Patent search agent implementation."""

from typing import Dict, Any, List, Optional
from ..base import BaseAgent
from ...tools.patent.patent_search_service import PatentSearchService
from ...core.exceptions import PatentSearchError
from ...models.patent_models import PatentDetails

class PatentSearchAgent(BaseAgent):
    """Agent for searching patent documents."""
    
    name = "patent_search"
    description = "Searches for relevant patents using chemical structures"
    
    def __init__(self):
        super().__init__()
        self.register_tool(PatentSearchService())
    
    async def execute(self, **kwargs) -> Dict[str, Any]:
        """Execute patent search."""
        try:
            # Validate inputs
            if not self._validate_inputs(kwargs):
                raise PatentSearchError("Missing required parameters: 'smiles' or 'patent_id'")
            
            smiles = kwargs.get("smiles")
            patent_id = kwargs.get("patent_id")
            max_results = kwargs.get("max_results", 10)
            
            search_tool = self.get_tool("patent_search")
            
            # Execute search based on input type
            if patent_id:
                results = await search_tool.execute(patent_id=patent_id)
            else:
                results = await search_tool.execute(smiles=smiles, max_results=max_results)
            
            return {
                "status": "success",
                "query": {"smiles": smiles} if smiles else {"patent_id": patent_id},
                "max_results": max_results,
                "results": results,
                "total_found": len(results) if results else 0
            }
            
        except Exception as e:
            self.logger.error(f"Patent search failed: {str(e)}")
            raise PatentSearchError(f"Patent search failed: {str(e)}")
    
    def _validate_inputs(self, params: Dict[str, Any]) -> bool:
        """Validate input parameters."""
        return bool(params.get("smiles") or params.get("patent_id"))
    
    async def plan(self, task: str) -> List[Dict[str, Any]]:
        """Plan search steps."""
        return [
            {"step": 1, "action": "validate_input", "tool": "patent_search"},
            {"step": 2, "action": "search_patents", "tool": "patent_search"},
            {"step": 3, "action": "filter_results", "tool": "patent_search"},
            {"step": 4, "action": "format_results", "tool": "patent_search"}
        ]