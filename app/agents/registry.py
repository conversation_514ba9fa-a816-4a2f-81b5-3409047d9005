from typing import Dict, Type, Any
from .base import BaseAgent
from ..config.settings import get_settings # Relative import

_agent_classes: Dict[str, Type[BaseAgent]] = {}
_agent_instances: Dict[str, BaseAgent] = {}

def register_agent(cls: Type[BaseAgent]):
    # Use class name as default registration key if no explicit name_attr_for_registry
    reg_name = getattr(cls, 'name_attr_for_registry', cls.__name__) 
    if reg_name in _agent_classes:
        print(f"Warning: Agent class '{reg_name}' ({cls.__name__}) is being re-registered.")
    _agent_classes[reg_name] = cls
    # print(f"Agent class '{reg_name}' ({cls.__name__}) registered.")
    return cls

def get_agent_instance(name: str, **kwargs) -> BaseAgent: # Name is the registration key
    if name not in _agent_instances:
        if name in _agent_classes:
            agent_class = _agent_classes[name]
            current_settings = get_settings()
            
            # Agents' __init__ should accept 'name', 'description', 'settings'
            # Use class attributes for default name/description if not passed in kwargs
            agent_name_arg = kwargs.pop('name', getattr(agent_class, 'name', name)) # 'name' is an expected attr by BaseAgent now
            agent_description_arg = kwargs.pop('description', getattr(agent_class, 'description', 'No description available.'))

            init_kwargs = {
                "name": agent_name_arg,
                "description": agent_description_arg,
                "settings": current_settings, 
                **kwargs
            }
            try:
                _agent_instances[name] = agent_class(**init_kwargs)
                # print(f"Agent instance '{name}' created and cached.")
            except TypeError as e:
                raise TypeError(f"Error initializing agent '{name}'. Check its __init__ signature. Original error: {e}\nPassed kwargs: {init_kwargs}")
        else:
            raise ValueError(f"Agent '{name}' not registered. Available: {list(_agent_classes.keys())}")
    return _agent_instances[name]

def get_all_agent_names() -> list[str]:
    return list(_agent_classes.keys())

def load_all_agents():
    import pkgutil
    import importlib
    package_name_to_scan = 'app.agents.custom'
    
    # print(f"Attempting to load custom agents from package: {package_name_to_scan}")
    try:
        package = importlib.import_module(package_name_to_scan)
    except ModuleNotFoundError:
        print(f"WARNING: Could not import custom agent package '{package_name_to_scan}'.")
        return
    
    loaded_modules_count = 0
    for _, module_name_suffix, _ in pkgutil.walk_packages(package.__path__, package.__name__ + '.'):
        short_module_name = module_name_suffix.split('.')[-1]
        if short_module_name not in ['__init__']: 
            try:
                importlib.import_module(module_name_suffix)
                loaded_modules_count += 1
            except Exception as e:
                print(f"  Error importing custom agent module {module_name_suffix}: {e}")
    # print(f"  Triggered import for {loaded_modules_count} potential custom agent modules.")
    # print(f"Agents registered after load_all_agents: {get_all_agent_names()}")
