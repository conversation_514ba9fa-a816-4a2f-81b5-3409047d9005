from typing import Dict, Any, Optional

from ..base import BaseAgent
from ..registry import register_agent
from ...tools.registry import get_tool_instance, ToolNotFoundException
from ...config.settings import Settings

@register_agent
class ReactionRecipeAgent(BaseAgent):
    name_attr_for_registry = "ReactionRecipeAgent" 
    name = "Reaction Recipe Generator Agent"
    description = "Generates a detailed JSON recipe card for a chemical reaction."

    # --- THIS IS THE FIX ---
    # Rename 'init' to the standard Python constructor '__init__'
    def __init__(self, name: str, description: str, settings: Settings, **kwargs):
        # The call to the parent's constructor might also need to be __init__
        # Assuming BaseAgent also uses __init__
        super().__init__(name=name, description=description, settings=settings, **kwargs)
        
        # Initialize the attribute before the try block
        self.reaction_recipe_tool: Optional[Any] = None
        try:
            # Tool name must match the class name used in @register_tool
            print(f"[{self.name}] Initializing and getting ReactionRecipeGenerator tool instance...")
            self.reaction_recipe_tool = get_tool_instance("ReactionRecipeGenerator")
            print(f"[{self.name}] Successfully got ReactionRecipeGenerator tool instance.")
        except ToolNotFoundException:
            print(f"[{self.name} CRITICAL ERROR] ReactionRecipeGenerator tool not found. This agent will not function.")
        except Exception as e:
            print(f"[{self.name} CRITICAL ERROR] Failed to initialize ReactionRecipeGenerator tool: {e}")

    def _is_likely_smiles_for_conversion(self, query: str) -> bool:
        # Required by the abstract base class
        return False

    async def process_message(self, message_content: Dict[str, Any], session_id: Optional[str] = None, **kwargs) -> Dict[str, Any]:
        reaction_smiles = message_content.get("reaction_smiles")
        original_name_for_saving = message_content.get("original_name")

        if not reaction_smiles:
            return {"reply": {"error": "Missing 'reaction_smiles' input for recipe generation."}, 
                    "status": "error", "error_message": "Missing 'reaction_smiles' input."}

        # This check will now succeed because __init__ was called
        if not self.reaction_recipe_tool:
            print(f"[{self.name} ERROR] The reaction_recipe_tool was not initialized correctly.")
            return {"reply": {"error": "ReactionRecipeGenerator tool is not available or failed to initialize."}, 
                    "status": "error", "error_message": "Tool not initialized."}

        print(f"[{self.name}] Passing raw SMILES to tool: {reaction_smiles}")
        try:
            tool_params = {"reaction_smiles": reaction_smiles}
            if original_name_for_saving:
                tool_params["original_name"] = original_name_for_saving
            
            reaction_class = kwargs.get("reaction_class")
            if reaction_class:
                self.reaction_recipe_tool.reaction_class = reaction_class

            recipe_json_or_error: Dict[str, Any] = self.reaction_recipe_tool.execute(**tool_params)

            if isinstance(recipe_json_or_error, dict) and "error" in recipe_json_or_error: 
                 return {"reply": recipe_json_or_error, "status": "error", "error_message": recipe_json_or_error.get("error")}

            return {"reply": recipe_json_or_error, "status": "success"}

        except ValueError as ve: 
            print(f"[{self.name} Error] Value error during tool execution: {ve}")
            return {"reply": {"error": str(ve)}, "status": "error", "error_message": str(ve)}
        except Exception as e:
            print(f"[{self.name} Error] Exception during tool execution: {e}")
            return {"reply": {"error": f"Internal error in tool execution: {str(e)}"}, 
                    "status": "error", "error_message": f"Internal error: {str(e)}"}