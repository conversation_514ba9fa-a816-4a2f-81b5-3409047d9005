import os
import time
import re
from typing import Dict, Any, Optional

from ..base import BaseAgent # Agent base class
from ..registry import register_agent # Agent registry
from ...tools.registry import get_tool_instance # Tool registry
from ...config.settings import Settings # For type hinting settings

def _sanitize_filename_for_viz(name: str) -> str:
    if not isinstance(name, str): name = str(name)
    name = re.sub(r'[^\w\.\-]+', '_', name); return name[:100]

@register_agent # Registers with class name "VisualizationAgent" by default
class VisualizationAgent(BaseAgent):
    # Default name and description if not overridden during instantiation
    name_attr_for_registry = "VisualizationAgent" # Explicit registry key
    name = "Chemical Visualizer Agent"
    description = "Generates 2D visualizations of chemical molecules or reactions from SMILES strings."

    def __init__(self, name: str, description: str, settings: Settings, **kwargs):
        super().__init__(name=name, description=description, settings=settings)
        # Get the underlying tool instance
        self.visualizer_tool = get_tool_instance("ChemVisualizer") # Name from @register_tool in visualizer_tool.py

    async def process_message(self, message_content: Dict[str, str], session_id: Optional[str] = None, **kwargs) -> Dict[str, Any]:
        smiles_or_reaction = message_content.get("smiles_or_reaction")
        if not smiles_or_reaction:
            return {"reply": "Error: 'smiles_or_reaction' key not provided for visualization.", 
                    "status": "error", "error_message": "Missing 'smiles_or_reaction' input."}

        print(f"[{self.name}] Processing visualization for: {smiles_or_reaction}")
        try:
            # Construct paths for the tool
            input_type = self.visualizer_tool.detect_input_type(smiles_or_reaction)
            timestamp = time.strftime("%Y%m%d_%H%M%S")
            sanitized_name = _sanitize_filename_for_viz(smiles_or_reaction)
            
            relative_filename = f"{input_type}_{sanitized_name}_{timestamp}.png"
            absolute_output_filepath = os.path.join(self.settings.VISUALIZATION_DIR_ABS, relative_filename)
            web_path = f"{self.settings.VISUALIZATION_WEB_BASE_PATH}/{relative_filename}"

            # Execute the tool
            tool_result = self.visualizer_tool.execute(
                smiles_or_reaction_smiles=smiles_or_reaction, 
                output_file=absolute_output_filepath
            )

            if isinstance(tool_result, str) and tool_result == absolute_output_filepath:
                return {"reply": f"Visualization generated successfully. Image available at: {web_path}", 
                        "visualization_path": web_path, 
                        "status": "success"}
            elif isinstance(tool_result, dict) and "error" in tool_result:
                return {"reply": f"Visualization tool error: {tool_result['error']}", 
                        "status": "error", "error_message": tool_result['error']}
            else: # Unexpected tool result
                return {"reply": f"Unexpected result from visualization tool: {tool_result}", 
                        "status": "error", "error_message": f"Unexpected tool output: {str(tool_result)}"}
        except Exception as e:
            import traceback
            print(f"[{self.name}] Exception: {str(e)}\n{traceback.format_exc()}")
            return {"reply": f"Agent error during visualization: {str(e)}", 
                    "status": "error", "error_message": str(e)}