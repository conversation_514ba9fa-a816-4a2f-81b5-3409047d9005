from typing import Dict, Any, Optional

from ..base import BaseAgent
from ..registry import register_agent
from ...tools.registry import get_tool_instance
from ...config.settings import Settings

@register_agent
class FuncGroupAgent(BaseAgent):
    name_attr_for_registry = "FuncGroupAgent"
    name = "Functional Group Identifier Agent"
    description = "Identifies functional groups in a molecule or reaction SMILES."

    def __init__(self, name: str, description: str, settings: Settings, **kwargs):
        super().__init__(name=name, description=description, settings=settings)
        self.func_group_tool = get_tool_instance("FuncGroups") # Tool registered name

    async def process_message(self, message_content: Dict[str, str], session_id: Optional[str] = None, **kwargs) -> Dict[str, Any]:
        query_smiles = message_content.get("smiles_or_reaction") # Consistent input key
        if not query_smiles:
            return {"reply": "Error: 'smiles_or_reaction' not provided for functional group analysis.", 
                    "status": "error", "error_message": "Missing input."}

        print(f"[{self.name}] Identifying functional groups for: {query_smiles}")
        tool_result = self.func_group_tool.execute(query_smiles=query_smiles) # 'query_smiles' is the arg for FuncGroupTool._run

        if isinstance(tool_result, dict) and "error" not in tool_result:
            # Format the result into a readable string
            reply_str = f"Functional Group Analysis for: {tool_result.get('smiles', tool_result.get('reaction_smiles', 'Input'))}\n"
            if tool_result.get("type") == "molecule_analysis":
                reply_str += f"  Functional Groups: {', '.join(tool_result.get('functional_groups', ['None'])) or 'None'}"
            elif tool_result.get("type") == "reaction_analysis":
                reply_str += "Reactants:\n"
                for r_info in tool_result.get("reactants_analysis", []):
                    reply_str += f"  - {r_info.get('smiles')}: {', '.join(r_info.get('functional_groups', ['None'])) or 'None'}\n"
                reply_str += "Products:\n"
                for p_info in tool_result.get("products_analysis", []):
                    reply_str += f"  - {p_info.get('smiles')}: {', '.join(p_info.get('functional_groups', ['None'])) or 'None'}\n"
                summary = tool_result.get("transformation_summary", {})
                reply_str += "Transformation Summary:\n"
                reply_str += f"  - Lost: {', '.join(summary.get('groups_lost', ['None'])) or 'None'}\n"
                reply_str += f"  - Gained: {', '.join(summary.get('groups_gained', ['None'])) or 'None'}\n"
                reply_str += f"  - Retained: {', '.join(summary.get('groups_retained', ['None'])) or 'None'}"
            else:
                reply_str = str(tool_result) # Fallback for unexpected structure

            return {"reply": reply_str.strip(), "data": tool_result, "status": "success"}
        elif isinstance(tool_result, dict) and "error" in tool_result:
            return {"reply": f"Functional group tool error: {tool_result['error']}", 
                    "status": "error", "error_message": tool_result['error']}
        else:
            return {"reply": f"Unexpected result from functional group tool: {tool_result}", 
                    "status": "error", "error_message": f"Unexpected tool output: {str(tool_result)}"}