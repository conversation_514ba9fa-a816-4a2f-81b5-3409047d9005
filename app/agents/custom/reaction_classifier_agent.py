from typing import Dict, Any, Optional

from ..base import BaseAgent
from ..registry import register_agent
from ...tools.registry import get_tool_instance
from ...config.settings import Settings

@register_agent
class ReactionClassifierAgent(BaseAgent):
    name_attr_for_registry = "ReactionClassifierAgent"
    name = "Reaction Classifier Agent"
    description = "Classifies reactions using an external API and local datasets, can also query specific properties."

    def __init__(self, name: str, description: str, settings: Settings, **kwargs):
        super().__init__(name=name, description=description, settings=settings)
        self.reaction_classifier_tool = get_tool_instance("ReactionClassifier")

    async def process_message(self, message_content: Dict[str, str], session_id: Optional[str] = None, **kwargs) -> Dict[str, Any]:
        reaction_smiles = message_content.get("reaction_smiles")
        property_query = message_content.get("property_query") # Optional

        if not reaction_smiles:
            return {"reply": "Error: 'reaction_smiles' not provided for classification/query.", 
                    "status": "error", "error_message": "Missing 'reaction_smiles' input."}

        print(f"[{self.name}] Processing reaction: {reaction_smiles}, Property query: {property_query}")
        # The ReactionClassifierTool._run method handles both full classification and property query
        tool_result_str = self.reaction_classifier_tool.execute(
            reaction_smiles=reaction_smiles, 
            query=property_query
        )

        if isinstance(tool_result_str, str) and not tool_result_str.startswith(f"[{self.reaction_classifier_tool.name}] Error:") and not tool_result_str.startswith("Error:"):
            return {"reply": tool_result_str, "status": "success"}
        elif isinstance(tool_result_str, str): # It's an error string from the tool
             return {"reply": tool_result_str, "status": "error", "error_message": tool_result_str}
        elif isinstance(tool_result_str, dict) and "error" in tool_result_str: # From BaseTool.execute
             return {"reply": tool_result_str["error"], "status": "error", "error_message": tool_result_str["error"]}
        else:
            return {"reply": f"Unexpected result from reaction classification tool: {tool_result_str}", 
                    "status": "error", "error_message": f"Unexpected tool output: {str(tool_result_str)}"}