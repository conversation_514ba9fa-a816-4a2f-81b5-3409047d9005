from typing import Dict, Any, Optional

from ..base import BaseAgent
from ..registry import register_agent
from ...tools.registry import get_tool_instance
from ...config.settings import Settings

@register_agent
class ChemicalPropsAgent(BaseAgent):
    name_attr_for_registry = "ChemicalPropsAgent"
    name = "Chemical Properties Reporter Agent"
    description = "Generates a comprehensive report on chemical properties, safety, and pricing."

    def __init__(self, name: str, description: str, settings: Settings, **kwargs):
        super().__init__(name=name, description=description, settings=settings)
        self.chemical_props_tool = get_tool_instance("ChemicalProperties")

    async def process_message(self, message_content: Dict[str, str], session_id: Optional[str] = None, **kwargs) -> Dict[str, Any]:
        chemical_identifier = message_content.get("chemical_identifier")
        if not chemical_identifier:
            return {"reply": "Error: 'chemical_identifier' (name, SMILES, or CAS) not provided.", 
                    "status": "error", "error_message": "Missing 'chemical_identifier' input."}

        print(f"[{self.name}] Generating report for: {chemical_identifier}")
        # The ChemicalPropsTool._run method returns the full report string or an error string
        report_or_error_str = self.chemical_props_tool.execute(chemical_identifier=chemical_identifier)

        if isinstance(report_or_error_str, str) and not report_or_error_str.startswith(f"[{self.chemical_props_tool.name}]") and not report_or_error_str.startswith("Error:"):
            return {"reply": report_or_error_str, "status": "success"}
        elif isinstance(report_or_error_str, str): # It's an error string
            return {"reply": report_or_error_str, "status": "error", "error_message": report_or_error_str}
        elif isinstance(report_or_error_str, dict) and "error" in report_or_error_str: # From BaseTool.execute
            return {"reply": report_or_error_str["error"], "status": "error", "error_message": report_or_error_str["error"]}
        else:
            return {"reply": f"Unexpected result from chemical properties tool: {report_or_error_str}", 
                    "status": "error", "error_message": f"Unexpected tool output: {str(report_or_error_str)}"}
