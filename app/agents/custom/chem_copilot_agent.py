import re
from typing import Dict, Any, Optional, List
from rdkit import Chem

from ..base import BaseAgent
from ..registry import register_agent
from ...tools.registry import get_tool_instance, ToolNotFoundException
from ...config.settings import Settings
# It might need access to SMILES extraction utilities if not handled by tools
# from ...core.utils import extract_reaction_smiles, extract_single_compound_smiles # Assuming you might create such a util
from ...core.chemical_analysis import ChemicalAnalysisLogic # For direct LLM calls if needed for routing or simple Q&A

# Placeholder for SMILES extraction functions (can be moved to a core.utils later)
def extract_reaction_smiles(query: str) -> Optional[str]: 
    smi_core_chars = r"[\w@\[\]\(\)\.\+\-\=\#\:\$\%\~\<\>]" 
    explicit_pattern = rf"(?i:\b(?:reaction\s+smiles|rxn)\s*[:=]?\s*)({smi_core_chars}+(?:>>{smi_core_chars}+)+)"
    match = re.search(explicit_pattern, query)
    if match:
        smiles = match.group(1).strip(); parts = smiles.split(">>")
        if len(parts) >= 2 and all(p.strip() for p in parts): return smiles
    standalone_pattern_strict = rf"(?<![\w\/])({smi_core_chars}+(?:>>{smi_core_chars}+)+)(?![\w\/])"
    potential_matches = re.findall(standalone_pattern_strict, query)
    for smi_candidate_match in potential_matches:
        smi_candidate = smi_candidate_match if isinstance(smi_candidate_match, str) else smi_candidate_match[0]
        smi_candidate = smi_candidate.strip(); parts = smi_candidate.split(">>")
        if len(parts) >= 2 and all(p.strip() for p in parts):
            try: # Basic RDKit check
                if Chem.MolFromSmiles(parts[0].split('.')[0]) and Chem.MolFromSmiles(parts[-1].split('.')[0]): return smi_candidate
            except: pass
    gt_pattern = rf"(?<![\w\/])({smi_core_chars}+(?:>{smi_core_chars}+)+)(?![\w\/])" # More general >
    match_gt = re.search(gt_pattern, query)
    if match_gt:
        temp_smi = match_gt.group(1).strip()
        if ">>" not in temp_smi:
            gt_parts = [p.strip() for p in temp_smi.split('>') if p.strip()]
            if len(gt_parts) >= 2:
                reactants_gt, products_gt = ".".join(gt_parts[:-1]), gt_parts[-1]
                if reactants_gt and products_gt:
                    try:
                        if Chem.MolFromSmiles(reactants_gt.split('.')[0]) and Chem.MolFromSmiles(products_gt.split('.')[0]):
                            return f"{reactants_gt}>>{products_gt}"
                    except: pass
    return None

def extract_single_compound_smiles(query: str) -> Optional[str]: 
    # Simplified from your original for brevity, focusing on common patterns
    # This regex is very basic and might need refinement from your original
    # It tries to find SMILES-like strings that are not part of URLs or paths
    # and don't contain '>>'.
    # A more robust solution would use RDKit's MolFromSmiles in a loop over potential candidates.
    potential_smiles_pattern = r"(?<![=/])\b([A-Za-z0-9@\[\]\(\)\.\+\-\=\#\:\$\%]{3,}(?<!>>[A-Za-z0-9@\[\]\(\)\.\+\-\=\#\:\$\%]+))\b(?![=/])"
    
    candidates = re.findall(potential_smiles_pattern, query)
    # Sort by length and complexity (more non-alphanumeric chars)
    # candidates.sort(key=lambda x: (len(x[0]), sum(1 for c in x[0] if not c.isalnum())), reverse=True) # x is a tuple from findall

    from rdkit import Chem # Local import for this function
    for cand_tuple in candidates:
        s_cand = cand_tuple[0] # The capturing group
        if '>>' in s_cand or '>' in s_cand or '<' in s_cand: continue # Skip reaction-like
        if s_cand.isnumeric() and not ('[' in s_cand and ']' in s_cand) : continue

        try:
            mol = Chem.MolFromSmiles(s_cand, sanitize=True)
            if mol and mol.GetNumAtoms() > 0:
                # Avoid common words that look like SMILES but are too simple
                if mol.GetNumAtoms() <= 2 and s_cand.isalpha() and s_cand.lower() in [
                    'as', 'in', 'is', 'at', 'or', 'to', 'be', 'of', 'on', 'no', 'do', 
                    'go', 'so', 'if', 'it', 'me', 'my', 'he', 'we', 'by', 'up', 'us', 'an', 'am', 'are'
                ]:
                    # Require some SMILES-specific characters if it's a short common word
                    if not any(c in s_cand for c in '()[]=#.-+@:/\\%{}**********'):
                        continue 
                return s_cand
        except Exception:
            pass
    return None


@register_agent
class ChemCopilotAgent(BaseAgent):
    name_attr_for_registry = "ChemCopilotAgent" # For unique registration
    name = "ChemCopilot Main Agent"
    description = "Orchestrates chemical analysis tasks, routing queries to specialized tools for compound and reaction analysis."

    def __init__(self, name: str, description: str, settings: Settings, **kwargs):
        super().__init__(name=name, description=description, settings=settings)
        try:
            self.chemical_props_tool = get_tool_instance("ChemicalProperties")
            self.reaction_recipe_tool = get_tool_instance("ReactionRecipeGenerator")
            # self.reaction_classifier_tool = get_tool_instance("ReactionClassifier") # If direct classification is also an option
            # Add other tools like NameToSMILES, FunctionalGroupsTool etc. as needed
            # self.name_to_smiles_tool = get_tool_instance("NameToSMILES") 
        except ToolNotFoundException as e:
            print(f"[{self.name} Error] Failed to initialize a required tool: {e}")
            # Decide how to handle this: raise error, or operate in a limited mode.
            # For now, we'll let it proceed, but tool calls might fail.
            pass
        
        # For simple LLM interactions not fitting a tool (e.g., very general chemistry Q&A if desired)
        self.basic_llm_logic = ChemicalAnalysisLogic(settings=settings)

        # Basic session cache for follow-up questions (in-memory, replace with persistent if needed)
        self.session_cache: Dict[str, Dict[str, Any]] = {} # session_id -> {last_reaction_smiles: ..., last_recipe: ...}


    async def process_message(self, message_content: Dict[str, str], session_id: Optional[str] = None, **kwargs) -> Dict[str, Any]:
        user_query = message_content.get("query")
        if not user_query:
            return {"reply": "Error: 'query' not provided.", "status": "error"}

        print(f"[{self.name}] Processing query: '{user_query}' for session: {session_id}")

        # State for this request
        # original_compound_name_context = message_content.get("original_compound_name") # If provided from UI

        # --- 1. Entity Extraction ---
        reaction_smiles = extract_reaction_smiles(user_query)
        compound_smiles: Optional[str] = None
        if not reaction_smiles:
            compound_smiles = extract_single_compound_smiles(user_query)

        # --- 2. Intent Detection & Routing ---
        query_lower = user_query.lower()
        
        # Keywords indicating a request for full, detailed information
        full_info_keywords = ["full info", "full data", "complete analysis", "details for", 
                              "details on", "tell me about", "analyze this", "report for", "recipe card"]

        is_full_info_request = any(keyword in query_lower for keyword in full_info_keywords)

        # --- Route to ReactionRecipeTool ---
        if reaction_smiles and is_full_info_request:
            if hasattr(self, 'reaction_recipe_tool'):
                try:
                    # ReactionRecipeTool returns a dictionary (the JSON recipe card)
                    recipe_json: Dict[str, Any] = self.reaction_recipe_tool.execute(reaction_smiles=reaction_smiles)
                    if session_id: # Cache for follow-ups
                        self.session_cache[session_id] = {
                            "last_entity_type": "reaction",
                            "last_smiles": reaction_smiles,
                            "last_recipe_json": recipe_json 
                        }
                    # The UI will typically handle displaying this JSON nicely.
                    # For a direct agent reply, we might just confirm success and pass the JSON.
                    return {"reply": "Generated reaction recipe.", "status": "success", "data_type": "reaction_recipe_json", "data": recipe_json}
                except Exception as e:
                    return {"reply": f"Error generating reaction recipe: {str(e)}", "status": "error"}
            else:
                return {"reply": "Reaction recipe tool is not available.", "status": "error"}

        # --- Route to ChemicalPropsTool ---
        elif compound_smiles and is_full_info_request:
            if hasattr(self, 'chemical_props_tool'):
                try:
                    # ChemicalPropsTool returns a string (the text report)
                    report_text: str = self.chemical_props_tool.execute(chemical_identifier=compound_smiles)
                    if session_id: # Cache for follow-ups
                         self.session_cache[session_id] = {
                            "last_entity_type": "compound",
                            "last_smiles": compound_smiles,
                            "last_report_text": report_text
                        }
                    return {"reply": "Generated chemical report.", "status": "success", "data_type": "chemical_report_text", "data": report_text}
                except Exception as e:
                    return {"reply": f"Error generating chemical report: {str(e)}", "status": "error"}
            else:
                return {"reply": "Chemical properties tool is not available.", "status": "error"}

        # --- Handle Follow-up Questions (Basic Example) ---
        # This needs more sophisticated intent recognition for "what was the yield?", "tell me about safety?" etc.
        if session_id and session_id in self.session_cache:
            cached_data = self.session_cache[session_id]
            if cached_data.get("last_entity_type") == "reaction" and "yield" in query_lower:
                last_recipe = cached_data.get("last_recipe_json", {})
                yield_info = last_recipe.get("reaction_conditions", {}).get("yield_from_source", "N/A")
                source = last_recipe.get("experimental_data_source_note", "previous analysis")
                return {"reply": f"The yield for {cached_data.get('last_smiles')} was {yield_info} (Source: {source}).", "status": "success"}
            # Add more follow-up intents for reaction and compound...

        # --- Fallback or Other Tool Calls ---
        # If no "full info" and no specific follow-up, try other tools or general LLM
        # Example: if user asks "What is the SMILES for Benzene?"
        # if "smiles for" in query_lower and hasattr(self, 'name_to_smiles_tool'):
        #     # Extract name, call name_to_smiles_tool
        #     return {"reply": name_to_smiles_tool.execute(...), "status": "success"}

        # --- General LLM Fallback (Simplistic) ---
        # You might want a dedicated "ChatbotTool" or more refined logic here
        if hasattr(self.basic_llm_logic, '_get_llm_completion_with_fallback'):
            system_prompt = "You are a helpful chemistry assistant. Answer the user's general chemistry question concisely."
            llm_response = self.basic_llm_logic._get_llm_completion_with_fallback(system_prompt, user_query)
            if llm_response:
                return {"reply": llm_response, "status": "success", "data_type": "llm_text_response"}
        
        return {"reply": "Sorry, I could not process your request with the available tools or understand it as a general query.", "status": "not_understood"}