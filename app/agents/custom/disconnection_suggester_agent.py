from typing import Dict, Any, Optional, List

from ..base import BaseAgent
from ..registry import register_agent
from ...tools.registry import get_tool_instance
from ...config.settings import Settings

@register_agent
class DisconnectionSuggesterAgent(BaseAgent):
    name_attr_for_registry = "DisconnectionSuggesterAgent"
    name = "Disconnection Suggester Agent"
    description = "Suggests retrosynthetic disconnections for a molecule, optionally using pre-identified functional groups."

    def __init__(self, name: str, description: str, settings: Settings, **kwargs):
        super().__init__(name=name, description=description, settings=settings)
        self.disconnection_tool = get_tool_instance("DisconnectionSuggester")
        # Optionally, get FuncGroupTool if this agent needs to fetch FGs itself
        # self.fg_tool = get_tool_instance("FuncGroups") 

    async def process_message(self, message_content: Dict[str, Any], session_id: Optional[str] = None, **kwargs) -> Dict[str, Any]:
        smiles = message_content.get("smiles")
        # Functional groups are optional; the tool can handle if they are missing
        functional_groups = message_content.get("functional_groups", []) 

        if not smiles:
            return {"reply": "Error: 'smiles' string not provided for disconnection suggestions.", 
                    "status": "error", "error_message": "Missing 'smiles' input."}
        
        if not isinstance(functional_groups, list):
            return {"reply": "Error: 'functional_groups' must be a list if provided.",
                    "status": "error", "error_message": "Invalid 'functional_groups' format."}


        print(f"[{self.name}] Suggesting disconnections for: {smiles}, FGs: {functional_groups if functional_groups else 'Not provided'}")
        
        # If this agent were responsible for getting FGs:
        # if not functional_groups:
        #     fg_result = self.fg_tool.execute(query_smiles=smiles)
        #     if isinstance(fg_result, dict) and "functional_groups" in fg_result:
        #         functional_groups = fg_result["functional_groups"]
        #         print(f"[{self.name}] Fetched FGs: {functional_groups}")
        #     elif isinstance(fg_result, dict) and "error" in fg_result:
        #         # Proceed without FGs or return error, for now proceed
        #         print(f"[{self.name}] Could not fetch FGs: {fg_result['error']}")
        #         functional_groups = []


        tool_result = self.disconnection_tool.execute(smiles=smiles, functional_groups=functional_groups)

        if isinstance(tool_result, dict) and "error" not in tool_result:
            reply_parts = [
                f"Disconnection Suggestions for SMILES: {tool_result.get('smiles', smiles)}",
                f"Functional Groups Considered: {', '.join(tool_result.get('functional_groups_identified', ['N/A'])) or 'N/A'}",
                "\nSuggestions from LLM:",
                tool_result.get('disconnection_suggestions', "No suggestions available or LLM error.")
            ]
            return {"reply": "\n".join(reply_parts), "data": tool_result, "status": "success"}
        elif isinstance(tool_result, dict) and "error" in tool_result:
             return {"reply": f"Disconnection suggester tool error: {tool_result['error']}", 
                     "status": "error", "error_message": tool_result['error']}
        else:
            return {"reply": f"Unexpected result from disconnection suggester tool: {tool_result}", 
                    "status": "error", "error_message": f"Unexpected tool output: {str(tool_result)}"}
