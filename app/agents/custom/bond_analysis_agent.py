from typing import Dict, Any, Optional

from ..base import BaseAgent
from ..registry import register_agent
from ...tools.registry import get_tool_instance
from ...config.settings import Settings

@register_agent
class BondAnalysisAgent(BaseAgent):
    name_attr_for_registry = "BondAnalysisAgent" # Explicit key for registry
    name = "Bond Analyzer Agent"
    description = "Analyzes bond changes (broken, formed, changed) in a chemical reaction."

    def __init__(self, name: str, description: str, settings: Settings, **kwargs):
        super().__init__(name=name, description=description, settings=settings)
        self.bond_analyzer_tool = get_tool_instance("BondAnalyzer") # Tool registered name

    async def process_message(self, message_content: Dict[str, str], session_id: Optional[str] = None, **kwargs) -> Dict[str, Any]:
        reaction_smiles = message_content.get("reaction_smiles")
        if not reaction_smiles:
            return {"reply": "Error: 'reaction_smiles' not provided for bond analysis.", 
                    "status": "error", "error_message": "Missing 'reaction_smiles' input."}

        print(f"[{self.name}] Analyzing bonds for: {reaction_smiles}")
        tool_result = self.bond_analyzer_tool.execute(reaction_smiles=reaction_smiles)

        if isinstance(tool_result, dict) and "error" not in tool_result:
            # Format the dictionary into a readable string for the reply
            reply_parts = [f"Bond Analysis for {reaction_smiles}:"]
            if "mapped_reaction" in tool_result:
                reply_parts.append(f"  Mapped Reaction: {tool_result['mapped_reaction']}")
            if "note" in tool_result:
                reply_parts.append(f"  Note: {tool_result['note']}")
            reply_parts.append(f"  Bonds Broken: {', '.join(tool_result.get('bonds_broken', ['None'])) or 'None'}")
            reply_parts.append(f"  Bonds Formed: {', '.join(tool_result.get('bonds_formed', ['None'])) or 'None'}")
            reply_parts.append(f"  Bonds Changed: {', '.join(tool_result.get('bonds_changed', ['None'])) or 'None'}")
            
            return {"reply": "\n".join(reply_parts), "data": tool_result, "status": "success"}
        elif isinstance(tool_result, dict) and "error" in tool_result:
            return {"reply": f"Bond analysis tool error: {tool_result['error']}", 
                    "status": "error", "error_message": tool_result['error']}
        else:
            return {"reply": f"Unexpected result from bond analysis tool: {tool_result}", 
                    "status": "error", "error_message": f"Unexpected tool output: {str(tool_result)}"}