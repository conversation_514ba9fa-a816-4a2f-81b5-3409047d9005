from typing import Dict, Any, Optional

from ..base import BaseAgent
from ..registry import register_agent
from ...tools.registry import get_tool_instance
from ...config.settings import Settings

@register_agent
class NameConversionAgent(BaseAgent):
    name_attr_for_registry = "NameConversionAgent"
    name = "Name/SMILES Converter Agent"
    description = "Converts chemical names to SMILES strings and vice-versa."

    def __init__(self, name: str, description: str, settings: Settings, **kwargs):
        super().__init__(name=name, description=description, settings=settings)
        self.name_to_smiles_tool = get_tool_instance("NameToSMILES")
        self.smiles_to_name_tool = get_tool_instance("SMILES2Name")
        
        # Check if RDKit is available
        self.rdkit_available = False
        try:
            from rdkit import Chem
            self.rdkit_available = True
            print(f"[{self.name}] RDKit is available for SMILES validation.")
        except ImportError:
            print(f"[{self.name}] RDKit not available, falling back to heuristic detection.")

    def _is_likely_smiles_for_conversion(self, query: str) -> bool:
        """
        Detect if a string is likely a SMILES notation using RDKit validation 
        with heuristic fallback.
        
        Args:
            query: The input string to check
            
        Returns:
            bool: True if the string is likely a SMILES string, False otherwise
        """
        if not query or not isinstance(query, str):
            return False
        
        query = query.strip()
        
        # Empty string
        if len(query) < 1:
            return False
        
        # Reaction SMILES (contains >>) - not for single molecule conversion
        if ">>" in query:
            return False
        
        # If RDKit is available, use it for definitive validation
        if self.rdkit_available:
            return self._rdkit_validate_smiles(query)
        else:
            # Fallback to heuristic method
            return self._heuristic_smiles_check(query)

    def _rdkit_validate_smiles(self, query: str) -> bool:
        """
        Use RDKit to validate if a string is a valid SMILES.
        
        Args:
            query: The input string to check
            
        Returns:
            bool: True if RDKit can parse it as a valid SMILES, False otherwise
        """
        try:
            from rdkit import Chem
            from rdkit.Chem import rdMolDescriptors
            
            # Suppress RDKit warnings/errors during parsing
            from rdkit import RDLogger
            RDLogger.DisableLog('rdApp.*')
            
            # Try to parse as SMILES
            mol = Chem.MolFromSmiles(query)
            
            if mol is not None:
                # Additional validation checks
                num_atoms = mol.GetNumAtoms()
                
                # Must have at least 1 atom and be reasonable size
                if num_atoms > 0 and num_atoms < 1000:
                    # Check if it's not just a single element symbol that might be a name
                    # Single letters like "C" or "N" could be ambiguous
                    if len(query) == 1 and query.upper() in ['C', 'N', 'O', 'S', 'P', 'F', 'I']:
                        # Single element symbols are ambiguous - use heuristic
                        return self._heuristic_smiles_check(query)
                    
                    # Check for obvious chemical names that RDKit might still parse
                    # (RDKit is very permissive and might parse some names)
                    obvious_names = [
                        'water', 'methanol', 'ethanol', 'acetone', 'benzene', 
                        'toluene', 'phenol', 'aniline', 'pyridine', 'furan',
                        'sodium', 'chloride', 'sulfate', 'nitrate', 'carbonate'
                    ]
                    
                    if query.lower() in obvious_names:
                        return False
                    
                    # If it contains spaces and no dots, probably not SMILES
                    if " " in query and "." not in query:
                        return False
                    
                    print(f"[{self.name}] RDKit validation: '{query}' -> Valid SMILES ({num_atoms} atoms)")
                    return True
                else:
                    print(f"[{self.name}] RDKit validation: '{query}' -> Invalid (unreasonable atom count: {num_atoms})")
                    return False
            else:
                print(f"[{self.name}] RDKit validation: '{query}' -> Invalid SMILES")
                return False
                
        except Exception as e:
            print(f"[{self.name}] RDKit validation error for '{query}': {str(e)}")
            # If RDKit fails, fall back to heuristic
            return self._heuristic_smiles_check(query)

    def _heuristic_smiles_check(self, query: str) -> bool:
        """
        Heuristic SMILES detection for fallback when RDKit is not available.
        
        Args:
            query: The input string to check
            
        Returns:
            bool: True if heuristics suggest it's a SMILES string
        """
        # If it contains spaces, it's probably not SMILES (unless it's a multi-component mixture with dots)
        if " " in query and "." not in query:
            return False
        
        # Common SMILES characteristics
        smiles_indicators = 0
        
        # Check for common SMILES characters
        smiles_chars = set("()[]=#@+-.CNOSPFBrClI")
        common_smiles_chars = "()[]=#@"
        
        # Count SMILES-specific characters
        for char in common_smiles_chars:
            if char in query:
                smiles_indicators += 1
        
        # Check for aromatic characters (lowercase letters)
        if any(c.islower() for c in query if c.isalpha()):
            smiles_indicators += 1
        
        # Check for numbers (bond indices, ring closures)
        if any(c.isdigit() for c in query):
            smiles_indicators += 1
        
        # Check if most characters are valid SMILES characters
        valid_chars = sum(1 for c in query if c in smiles_chars or c.isalnum())
        char_ratio = valid_chars / len(query) if len(query) > 0 else 0
        
        # Common patterns that suggest SMILES
        smiles_patterns = [
            'CC', 'C=C', 'C#C', 'c1c', 'C1C', 'N(', 'O=', 'S(',
            'Br', 'Cl', 'c1ccccc1', 'C(=O)'
        ]
        
        pattern_matches = sum(1 for pattern in smiles_patterns if pattern in query)
        
        # Decision logic
        if smiles_indicators >= 2 and char_ratio > 0.7:
            return True
        
        if pattern_matches >= 1 and smiles_indicators >= 1:
            return True
        
        if char_ratio > 0.8 and smiles_indicators >= 1:
            return True
        
        # Special case for simple molecules like "CCO" (ethanol)
        if len(query) <= 10 and all(c in "CNOSPFBrClI0123456789" for c in query):
            return True
        
        # High confidence NOT SMILES for common chemical names
        common_names = [
            'methanol', 'ethanol', 'propanol', 'butanol', 'pentanol',
            'methane', 'ethane', 'propane', 'butane', 'pentane',
            'benzene', 'toluene', 'xylene', 'phenol', 'aniline',
            'acetone', 'formaldehyde', 'acetaldehyde', 'acetic acid',
            'formic acid', 'propionic acid', 'butyric acid',
            'sodium chloride', 'water', 'ammonia', 'hydrogen peroxide'
        ]
        
        if query.lower() in common_names:
            return False
        
        return False

    async def process_message(self, message_content: Dict[str, str], session_id: Optional[str] = None, **kwargs) -> Dict[str, Any]:
        identifier = message_content.get("identifier")
        if not identifier:
            return {"reply": "Error: 'identifier' (name or SMILES) not provided.", 
                    "status": "error", "error_message": "Missing 'identifier' input."}

        print(f"[{self.name}] Processing identifier: {identifier}")
        
        tool_result_str: Optional[str] = None
        # Use improved detection with RDKit validation
        if self._is_likely_smiles_for_conversion(identifier):
            print(f"[{self.name}] Identified as likely SMILES, using SmilesToNameTool.")
            tool_result = self.smiles_to_name_tool.execute(smiles_string=identifier)
        else:
            print(f"[{self.name}] Identified as likely Name, using NameToSmilesTool.")
            tool_result = self.name_to_smiles_tool.execute(chemical_name=identifier)
        
        if isinstance(tool_result, str): # Both tools return string
            if "Error:" in tool_result or "No SMILES found" in tool_result or "Could not resolve" in tool_result :
                return {"reply": tool_result, "status": "partial_error", "error_message": tool_result}
            else:
                return {"reply": tool_result, "status": "success"}
        elif isinstance(tool_result, dict) and "error" in tool_result: # From BaseTool.execute
             return {"reply": tool_result["error"], "status": "error", "error_message": tool_result["error"]}
        else:
            return {"reply": f"Unexpected result from conversion tool: {tool_result}", 
                    "status": "error", "error_message": f"Unexpected tool output: {str(tool_result)}"}