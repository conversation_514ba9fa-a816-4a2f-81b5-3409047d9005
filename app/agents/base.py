from abc import ABC, abstractmethod
from typing import Any, Dict, Optional
from ..config.settings import Settings # Relative import for settings

class BaseAgent(ABC):
    name: str
    description: str

    def __init__(self, name: str, description: str, settings: Settings, **kwargs):
        self.name = name
        self.description = description
        self.settings = settings
        # print(f"Agent '{self.name}' initialized.")

    @abstractmethod
    async def process_message(self, message_content: Any, session_id: Optional[str] = None, **kwargs) -> Dict[str, Any]:
        """
        Process an incoming message/task and return a structured response.
        'message_content' can be a string or a dictionary depending on the agent.
        Response should be a dictionary, e.g., 
        {"reply": "...", "data": ..., "status": "success/error", "error_message": "..."}
        """
        pass

    @abstractmethod
    async def _is_likely_smiles_for_conversion(self, query: str) -> bool:
        """
        Check if the input query is likely a SMILES string.
        This is used to determine which conversion tool to use.
        """
        pass

    def reset_state(self, session_id: Optional[str] = None):
        # print(f"Agent '{self.name}' state reset for session: {session_id}")
        pass