import requests
import re
import pandas as pd
import os
# import re # Not used in this version
from typing import Dict, Any, Optional, List

from .base import BaseTool
from .registry import register_tool
from ..config.settings import Settings
from .reaction_info import ReactionRecipeGeneratorTool # Import the tool that has the ASKCOS call

@register_tool
class ReactionClassifierTool(BaseTool):
    name = "ReactionClassifier"
    description = ("Tool to classify reaction types based on reaction SMILES using an external API (via ReactionRecipeGeneratorTool's helper) "
                   "and provide detailed information from local parquet datasets.")

    def __init__(self, settings: Settings, **kwargs):
        super().__init__(settings=settings)
        # self.api_url = getattr(settings, 'REACTION_CLASSIFIER_API_URL', "http://************:9621/reaction_class") # No longer needed directly
        
        # Instantiate ReactionRecipeGeneratorTool to use its _call_askcos_api method
        # This means ReactionRecipeGeneratorTool must be initialized before this tool if there are dependencies.
        # Alternatively, move _call_askcos_api to a shared utility or ChemicalAnalysisLogic.
        try:
            # If ReactionRecipeGeneratorTool itself needs settings for ChemicalAnalysisLogic, this is fine.
            self.recipe_tool_for_askcos = ReactionRecipeGeneratorTool(settings=settings)
        except Exception as e:
            print(f"[{self.name} Error] Failed to initialize internal ReactionRecipeGeneratorTool for ASKCOS call: {e}")
            self.recipe_tool_for_askcos = None


        self.dataset_path1 = self.settings.REACTION_DATASET_PATH1
        self.dataset_path2 = self.settings.REACTION_DATASET_PATH2
        
        self.dataset1: Optional[pd.DataFrame] = None
        self.dataset2: Optional[pd.DataFrame] = None
        self._try_load_datasets()
        
        self.property_mappings = {
            'temp': ['temperature', 'temp', 'reaction_temperature', 'conditions_temperature'],
            'yield': ['yield', 'reaction_yield', 'product_yield', 'yields'],
            # ... (rest of your mappings)
        }
        self.min_certainty = 0.9 # For dataset filtering

    def _try_load_datasets(self):
        # ... (same as before)
        try:
            if self.dataset_path1 and os.path.exists(self.dataset_path1):
                self.dataset1 = pd.read_parquet(self.dataset_path1)
                print(f"[{self.name}] Successfully loaded dataset 1: {self.dataset_path1}")
            elif self.dataset_path1:
                 print(f"[{self.name}] Warning: Dataset 1 path provided but file not found: {self.dataset_path1}")
            
            if self.dataset_path2 and os.path.exists(self.dataset_path2):
                self.dataset2 = pd.read_parquet(self.dataset_path2)
                print(f"[{self.name}] Successfully loaded dataset 2: {self.dataset_path2}")
            elif self.dataset_path2:
                 print(f"[{self.name}] Warning: Dataset 2 path provided but file not found: {self.dataset_path2}")
                
        except Exception as e:
            print(f"[{self.name}] Error loading datasets: {str(e)}")

    def _run(self, reaction_smiles: str, query: Optional[str] = None) -> str:
        print(f"[{self.name} Tool] _run called for SMILES: {reaction_smiles}, Query: {query}")

        if not self.recipe_tool_for_askcos:
            return f"[{self.name} Error] Cannot call ASKCOS API due to internal tool initialization failure."

        # Use the centralized _call_askcos_api from ReactionRecipeGeneratorTool
        askcos_results_list = self.recipe_tool_for_askcos._call_askcos_api(reaction_smiles)

        if askcos_results_list and isinstance(askcos_results_list, list) and len(askcos_results_list) > 0:
            # Assuming askcos_results_list is a list of classification dicts, sorted by rank
            top_reaction = askcos_results_list[0] # Get the top-ranked classification
            
            reaction_name = top_reaction.get("reaction_name", top_reaction.get("reaction_classname", "Unknown Classification"))
            certainty = top_reaction.get("prediction_certainty", 0.0) # Default to 0.0 if not present
            
            if query: # If a specific property is queried
                # Note: _get_specific_property needs the reaction_name from ASKCOS
                specific_info = self._get_specific_property(reaction_name, query)
                return specific_info

            # Format general classification result
            result_str = f"## Reaction Classification for {reaction_smiles} (via ASKCOS API)\n"
            result_str += f"- **Top Predicted Type**: {reaction_name}\n"
            # The reaction_num and reaction_classname might be specific to how your ASKCOS returns data
            result_str += f"- **Class (from API)**: {top_reaction.get('reaction_classname', 'N/A')}\n"
            result_str += f"- **Prediction Certainty**: {certainty * 100:.2f}%\n"
            
            # Optionally, list other predictions if askcos_results_list has more entries
            if len(askcos_results_list) > 1:
                result_str += "\n**Other Predictions:**\n"
                for i, item in enumerate(askcos_results_list[1:4]): # Show next 3
                    item_name = item.get("reaction_name", item.get("reaction_classname", "N/A"))
                    item_cert = item.get("prediction_certainty", 0.0)
                    result_str += f"  - Rank {i+2}: {item_name} (Certainty: {item_cert*100:.2f}%)\n"
            result_str += "\n"
            
            # Now, fetch details from local datasets using the top reaction_name
            reaction_details_from_datasets = self._get_reaction_info_from_datasets(reaction_name)
            result_str += f"## Detailed Information from Local Datasets for '{reaction_name}' (if found with certainty >= {self.min_certainty*100}%)\n{reaction_details_from_datasets}\n"
            
            return result_str
        else:
            return f"[{self.name}] No reaction classification results from ASKCOS API for {reaction_smiles} or API call failed."

    # _get_specific_property, _search_property_in_dataset, _get_reaction_info_from_datasets,
    # _search_reaction_in_dataset, _format_reaction_info, _get_general_reaction_info
    # remain largely the same as you provided, as they operate on local datasets.
    # Ensure they correctly use self.dataset1 and self.dataset2.
    # ... (paste those methods here) ...
    def _get_specific_property(self, reaction_name: str, property_query: str) -> str:
        # (Make sure this method and its helpers are correctly defined as per your previous version)
        try:
            if self.dataset1 is None and self.dataset2 is None:
                return f"[{self.name}] No datasets loaded. Cannot retrieve {property_query} information for {reaction_name}."
            
            property_query_lower = property_query.lower().strip()
            target_category = None
            for category, keywords in self.property_mappings.items():
                if any(keyword in property_query_lower for keyword in keywords) or \
                   any(property_query_lower in keyword for keyword in keywords):
                    target_category = category
                    break
            
            if not target_category:
                return f"[{self.name}] Could not identify property type for query: '{property_query}' concerning reaction '{reaction_name}'."
            
            matches: List[Dict[str, Any]] = [] # Ensure matches is typed
            if self.dataset1 is not None:
                matches.extend(self._search_property_in_dataset(self.dataset1, reaction_name, target_category))
            if self.dataset2 is not None:
                matches.extend(self._search_property_in_dataset(self.dataset2, reaction_name, target_category))
            
            if matches:
                result = f"## {target_category.title()} Information for {reaction_name} (from local datasets)\n\n"
                for match_item in matches: # Renamed 'match' to 'match_item' to avoid conflict
                    result += f"- **{match_item['column']}**: {match_item['value']}\n"
                return result
            else:
                return f"[{self.name}] No {target_category} information found for {reaction_name} in local datasets."
        except Exception as e:
            return f"[{self.name}] Error retrieving specific property '{property_query}' for '{reaction_name}': {str(e)}"

    def _search_property_in_dataset(self, dataset: pd.DataFrame, reaction_name: str, property_category: str) -> List[Dict[str, Any]]:
        # (Make sure this method and its helpers are correctly defined as per your previous version)
        matches = []
        try:
            # Find columns that might contain the reaction name
            reaction_name_cols = [col for col in dataset.columns if 'name' in col.lower() or 'reaction_class' in col.lower() or 'class' in col.lower()]
            if not reaction_name_cols: return matches
            
            matching_rows_df = pd.DataFrame()
            for col in reaction_name_cols:
                if dataset[col].dtype == 'object' and reaction_name: # Ensure reaction_name is not empty
                    # Case-insensitive partial match for reaction name
                    col_matches = dataset[dataset[col].astype(str).str.contains(reaction_name, case=False, na=False, regex=False)]
                    if not col_matches.empty:
                        matching_rows_df = pd.concat([matching_rows_df, col_matches]).drop_duplicates()
            
            if matching_rows_df.empty: return matches
                
            # Filter by certainty if certainty column exists
            certainty_cols = [col for col in dataset.columns if 'certainty' in col.lower() or 'confidence' in col.lower()]
            if certainty_cols:
                certainty_col_name = certainty_cols[0] # Take the first one found
                if certainty_col_name in matching_rows_df.columns:
                    # Convert to numeric, coercing errors (non-numeric will become NaN)
                    matching_rows_df[certainty_col_name] = pd.to_numeric(matching_rows_df[certainty_col_name], errors='coerce')
                    matching_rows_df = matching_rows_df.dropna(subset=[certainty_col_name]) # Remove rows where conversion failed
                    matching_rows_df = matching_rows_df[matching_rows_df[certainty_col_name] >= self.min_certainty] # Filter
                    if matching_rows_df.empty: return matches # No matches after certainty filter
            
            # Find columns related to the property category
            property_keywords = self.property_mappings[property_category]
            potential_columns_for_prop = []
            for col in dataset.columns: # Iterate over all columns of the original dataset
                col_lower = col.lower()
                if any(keyword in col_lower for keyword in property_keywords):
                    potential_columns_for_prop.append(col)
                # Heuristic for general "conditions" or "details" columns that might contain the keyword
                elif ('condition' in col_lower or 'detail' in col_lower or 'parameter' in col_lower) and \
                     not matching_rows_df[col].isnull().all(): # Check if column in matching_rows_df has any non-nulls
                    # Check if first non-null content contains keywords (if column has mixed types or dict-like strings)
                    first_valid_content_series = matching_rows_df[col].dropna()
                    if not first_valid_content_series.empty:
                        first_valid_content = str(first_valid_content_series.iloc[0]).lower()
                        if any(keyword in first_valid_content for keyword in property_keywords):
                            potential_columns_for_prop.append(col)
            
            # Extract values from potential columns for the matched rows
            for _, row in matching_rows_df.iterrows():
                for col in potential_columns_for_prop:
                    if col in row and pd.notna(row[col]):
                        value = row[col]
                        # If value is a dict-like string, try to extract the specific property
                        if isinstance(value, str) and ('{' in value and ':' in value): 
                            for keyword in property_keywords:
                                try: # Use a non-greedy match for the value
                                    pattern = rf'[\'"]?{re.escape(keyword)}[\'"]?\s*[:=]\s*[\'"]?(.*?)[\'"]?(?:,|\s*}}|\s*$)'
                                    match_obj = re.search(pattern, value, re.IGNORECASE)
                                    if match_obj:
                                        extracted_val = match_obj.group(1).strip().strip("'\"") # Strip quotes
                                        if len(extracted_val) < 100 and '{' not in extracted_val: # Avoid huge/nested
                                            value = extracted_val
                                        break 
                                except re.error: pass 
                        matches.append({'column': col, 'value': str(value)}) # Ensure value is string
        except Exception as e:
            print(f"[{self.name}] Error in _search_property_in_dataset for {reaction_name} / {property_category}: {str(e)}")
        return matches
    
    def _get_reaction_info_from_datasets(self, reaction_name: str) -> str:
        # (Same as before)
        try:
            if self.dataset1 is None and self.dataset2 is None:
                return f"[{self.name}] No local datasets loaded. Cannot provide detailed info for {reaction_name} from them."
            
            search_result1 = self._search_reaction_in_dataset(self.dataset1, reaction_name) if self.dataset1 is not None else ""
            search_result2 = self._search_reaction_in_dataset(self.dataset2, reaction_name) if self.dataset2 is not None else ""
            
            combined_results = []
            if search_result1.strip(): combined_results.append(f"From Dataset 1:\n{search_result1}")
            if search_result2.strip(): combined_results.append(f"From Dataset 2:\n{search_result2}")

            if combined_results:
                return "\n\n---\n\n".join(combined_results)
            else:
                return (f"[{self.name}] No detailed information found for reaction '{reaction_name}' "
                        f"in local datasets matching certainty >= {self.min_certainty*100}%.")
        except Exception as e:
            return f"[{self.name}] Error retrieving information from datasets for '{reaction_name}': {str(e)}"

    def _search_reaction_in_dataset(self, dataset: Optional[pd.DataFrame], reaction_name: str) -> str:
        # (Same as before)
        if dataset is None or dataset.empty: return ""
        try:
            # More flexible search for reaction name columns
            reaction_name_cols = [col for col in dataset.columns if 'name' in col.lower() or 'class' in col.lower()]
            if not reaction_name_cols: return ""
            
            all_matching_rows_df = pd.DataFrame()
            for col_name_field in reaction_name_cols:
                if dataset[col_name_field].dtype == 'object' and reaction_name:
                    # Case-insensitive contains match
                    matches_df_for_col = dataset[dataset[col_name_field].astype(str).str.contains(reaction_name, case=False, na=False, regex=False)]
                    if not matches_df_for_col.empty:
                        all_matching_rows_df = pd.concat([all_matching_rows_df, matches_df_for_col])
            
            if all_matching_rows_df.empty: return ""
            all_matching_rows_df = all_matching_rows_df.drop_duplicates() # Remove duplicates if matched on multiple name cols

            # Filter by certainty if applicable
            certainty_col_options = ['prediction_certainty', 'confidence', 'probability'] # Add more if needed
            certainty_col_to_use = next((c for c in all_matching_rows_df.columns if any(opt_c.lower() in c.lower() for opt_c in certainty_col_options)), None)

            if certainty_col_to_use:
                all_matching_rows_df[certainty_col_to_use] = pd.to_numeric(all_matching_rows_df[certainty_col_to_use], errors='coerce')
                all_matching_rows_df = all_matching_rows_df.dropna(subset=[certainty_col_to_use])
                filtered_matches = all_matching_rows_df[all_matching_rows_df[certainty_col_to_use] >= self.min_certainty]
                if filtered_matches.empty: return ""
                all_matching_rows_df = filtered_matches
            
            if all_matching_rows_df.empty: return "" # No matches after filtering or if no certainty col

            # Format results
            dataset_source_name = "Dataset1" if dataset is self.dataset1 else "Dataset2" if dataset is self.dataset2 else "Dataset"
            result_str = f"Found {len(all_matching_rows_df)} entries in {dataset_source_name} related to '{reaction_name}':\n"
            for _, row in all_matching_rows_df.head(5).iterrows(): # Limit to top 5 matches for brevity
                result_str += self._format_reaction_info(row) + "\n---\n"
            if len(all_matching_rows_df) > 5:
                result_str += f"... and {len(all_matching_rows_df) - 5} more entries.\n"
            return result_str.strip()
        except Exception as e:
            return f"[{self.name}] Error searching dataset for '{reaction_name}': {str(e)}"

    def _format_reaction_info(self, row: pd.Series) -> str:
        # (Same as before)
        result = ""
        # Attempt to find a primary name for the entry
        name_keys = ['reaction_name', 'name', 'template_name', 'NAME', 'CLASS']
        display_name = "Unknown Reaction Entry"
        for key in name_keys:
            if key in row and pd.notna(row[key]):
                display_name = str(row[key])
                break
        result += f"### {display_name}\n"

        # Display certainty if available
        certainty_keys = ['prediction_certainty', 'confidence', 'probability']
        certainty_val_str = "N/A"
        for key in certainty_keys:
            if key in row and pd.notna(row[key]):
                try:
                    cert_float = float(row[key])
                    certainty_val_str = f"{cert_float*100 if 0 <= cert_float <= 1 else cert_float:.2f}%"
                except (ValueError, TypeError):
                    certainty_val_str = str(row[key])
                result += f"**Certainty/Confidence**: {certainty_val_str}\n"
                break
        
        # Display reaction SMILES if available
        smiles_keys = ['rxn_str', 'reaction_smiles', 'smiles', 'canonical_rxn_smiles']
        for key in smiles_keys:
            if key in row and pd.notna(row[key]):
                result += f"**Reaction SMILES**: {str(row[key])}\n"
                break # Show first one found

        # Display some key fields if present
        key_fields_to_display = {
            'description': "Description", 'procedure_details': "Procedure Details",
            'temperature': "Temperature", 'rxn_time': "Reaction Time", 'yield_000': "Yield",
            'solvent_000': "Solvent 1", 'agent_000': "Agent 1", # Example for first solvent/agent
        }
        for field_key, display_label in key_fields_to_display.items():
            if field_key in row and pd.notna(row[field_key]):
                value = str(row[field_key])
                if field_key == 'yield_000' and '%' not in value: value += "%" # Add % to yield if missing
                result += f"**{display_label}**: {value}\n"
        
        # Add a small note about data source if not already clear
        # result += "(Information from local dataset)\n"
        return result.strip()
    
    def _get_general_reaction_info(self, reaction_name: str) -> str:
        # (Same as before)
        return (f"The {reaction_name} is a chemical reaction identified by the classification API. "
                f"For more detailed information, consult chemistry literature or specific databases, or query for specific properties if datasets are loaded.")