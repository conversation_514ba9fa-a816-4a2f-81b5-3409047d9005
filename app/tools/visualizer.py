import os
import traceback
from rdkit import Chem
from rdkit.Chem import AllChem
from rdkit.Chem.Draw import rdMolDraw2D 
from typing import Dict, Any

from .base import BaseTool
from .registry import register_tool
from ..config.settings import Settings

@register_tool
class ChemVisualizerTool(BaseTool):
    name = "ChemVisualizer"
    description = "Visualizes a chemical molecule or reaction from its SMILES string and saves it to a specified file path."

    def __init__(self, settings: Settings, **kwargs):
        super().__init__(settings=settings)
        # No specific state needed from settings during init for this tool,
        # as output path is passed during _run.

    def detect_input_type(self, smiles_input: str) -> str: 
        if not isinstance(smiles_input, str): # Basic type check
            return 'unknown'
        return 'reaction' if '>>' in smiles_input else 'molecule'

    def _visualize_reaction(self, rxn_smiles: str, output_file_abs_path: str) -> str:
        try:
            # print(f"[{self.name} Tool] Visualizing reaction: {rxn_smiles} to {output_file_abs_path}")
            rxn = AllChem.ReactionFromSmarts(rxn_smiles, useSmiles=True)
            if rxn is None or rxn.GetNumReactantTemplates() == 0 or rxn.GetNumProductTemplates() == 0:
                return f"Error: RDKit could not parse reaction SMILES '{rxn_smiles}' or found no reactants/products."

            num_components = rxn.GetNumReactantTemplates() + rxn.GetNumProductTemplates()
            width_per_component = 200; min_width = 400
            width = max(min_width, width_per_component * num_components if num_components > 0 else min_width)
            
            max_mols_on_one_side = max(rxn.GetNumReactantTemplates(), rxn.GetNumProductTemplates(), 1) 
            height_per_row = 200; min_height = 250
            height = max(min_height, height_per_row * max_mols_on_one_side)

            drawer = rdMolDraw2D.MolDraw2DCairo(int(width), int(height))
            dopts = drawer.drawOptions()
            dopts.dummiesAreAttachments = True
            dopts.includeAtomTags = False 
            dopts.bondLineWidth = 1.5 
            dopts.padding = 0.1
            drawer.DrawReaction(rxn)
            drawer.FinishDrawing()

            with open(output_file_abs_path, 'wb') as f:
                f.write(drawer.GetDrawingText())
            return output_file_abs_path
        except Chem.rdchem.AtomValenceException as ave: # type: ignore
            return f"Error visualizing reaction (AtomValenceException for '{rxn_smiles}'): {str(ave)}"
        except Chem.rdchem.KekulizeException as ke: # type: ignore
            return f"Error visualizing reaction (KekulizeException for '{rxn_smiles}'): {str(ke)}"
        except Exception as e:
            return f"General error visualizing reaction ('{rxn_smiles}'): {type(e).__name__}: {str(e)}"

    def _visualize_molecule(self, smiles: str, output_file_abs_path: str) -> str:
        try:
            # print(f"[{self.name} Tool] Visualizing molecule: {smiles} to {output_file_abs_path}")
            mol = Chem.MolFromSmiles(smiles)
            if mol is None:
                return f"Error: Failed to parse molecule SMILES: '{smiles}'"

            # Ensure 2D coordinates are generated for drawing
            if mol.GetNumConformers() == 0 or mol.GetConformer().Is3D(): # Check if 2D coords are missing or it's 3D
                AllChem.Compute2DCoords(mol)
            
            drawer = rdMolDraw2D.MolDraw2DCairo(400, 300) 
            dopts = drawer.drawOptions()
            dopts.bondLineWidth = 1.5 
            dopts.padding = 0.1
            # dopts.addStereoAnnotation = True # Optional: to show stereo chemistry if present
            # dopts.includeChiralFlagLabel = True # Optional

            drawer.DrawMolecule(mol)
            drawer.FinishDrawing()
            
            with open(output_file_abs_path, 'wb') as f:
                f.write(drawer.GetDrawingText())
            return output_file_abs_path
        except Exception as e:
            return f"Error visualizing molecule ('{smiles}'): {type(e).__name__}: {str(e)}"

    def _run(self, smiles_or_reaction_smiles: str, output_file: str) -> str:
        print(f"[{self.name} Tool] _run for: {smiles_or_reaction_smiles}, Output: {output_file}")
        if not output_file:
            return "Error: Output file path not provided for visualization."
        if not smiles_or_reaction_smiles:
             return "Error: SMILES or reaction SMILES string not provided for visualization."

        output_dir = os.path.dirname(output_file)
        if output_dir and not os.path.exists(output_dir): # Check if output_dir is not empty string
            try:
                os.makedirs(output_dir, exist_ok=True)
            except Exception as e_dir:
                return f"Error creating output directory '{output_dir}': {str(e_dir)}"

        input_type = self.detect_input_type(smiles_or_reaction_smiles)
        
        if input_type == 'reaction':
            return self._visualize_reaction(smiles_or_reaction_smiles, output_file)
        elif input_type == 'molecule':
            return self._visualize_molecule(smiles_or_reaction_smiles, output_file)
        else:
            return f"Error: Could not determine input type for visualization from '{smiles_or_reaction_smiles}'"