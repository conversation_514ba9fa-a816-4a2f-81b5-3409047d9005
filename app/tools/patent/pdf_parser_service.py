"""
PDF parser service using Mistral OCR for extracting text from PDF documents
"""

import os
from typing import <PERSON>ple, Optional
from mistralai import Mistral
from app.utils.logging import get_logger
from app.core.exceptions import PatentProcessingError
from config.settings import get_settings


class PDFParserService:
    """
    Service that uses Mistral OCR to extract text from PDF documents
    """
    
    def __init__(self, api_key: str = None):
        """
        Initialize the PDF parser service

        Args:
            api_key: Mistral API key (defaults to MISTRAL_API_KEY from settings)
        """
        self.settings = get_settings()
        self.api_key = api_key or self.settings.mistral_api_key
        if not self.api_key:
            raise ValueError("Mistral API key is required. Set MISTRAL_API_KEY environment variable or pass it directly.")

        self.client = Mistral(api_key=self.api_key)
        self.logger = get_logger(__name__)
        
        print(f"🔧 PDF Parser Service initialized")
        self.logger.info("PDF Parser Service initialized")
    
    def parse_pdf(self, pdf_path: str) -> Tuple[str, str]:
        """
        Parse a PDF document using Mistral OCR
        
        Args:
            pdf_path: Path to the PDF file
            
        Returns:
            Tuple containing:
                - Extracted text in markdown format
                - Signed URL for the document
                
        Raises:
            PatentProcessingError: If parsing fails
        """
        try:
            print(f"📄 Parsing PDF with Mistral OCR: {os.path.basename(pdf_path)}")
            self.logger.info(f"Starting PDF parsing for: {pdf_path}")
            
            # Check if file exists
            if not os.path.exists(pdf_path):
                raise FileNotFoundError(f"PDF file not found: {pdf_path}")
            
            # Check file size
            file_size = os.path.getsize(pdf_path)
            print(f"   📊 File size: {file_size:,} bytes")
            self.logger.info(f"PDF file size: {file_size} bytes")
            
            # Upload the file to Mistral for OCR processing
            print(f"   ⬆️ Uploading to Mistral OCR...")
            with open(pdf_path, "rb") as file:
                uploaded_pdf = self.client.files.upload(
                    file={"file_name": os.path.basename(pdf_path), "content": file},
                    purpose="ocr"
                )
            
            print(f"   ✅ Upload complete. File ID: {uploaded_pdf.id}")
            self.logger.info(f"File uploaded successfully. ID: {uploaded_pdf.id}")

            # Get the signed URL to allow secure processing
            print(f"   🔗 Getting signed URL...")
            signed_url = self.client.files.get_signed_url(file_id=uploaded_pdf.id)
            print(f"   ✅ Signed URL obtained")
            self.logger.info("Signed URL obtained")

            # Process OCR on the document using the signed URL
            print(f"   🔍 Processing OCR...")
            ocr_response = self.client.ocr.process(
                model="mistral-ocr-latest",
                document={"type": "document_url", "document_url": signed_url.url},
            )

            # Combine all pages into a single markdown string
            print(f"   📝 Combining {len(ocr_response.pages)} pages...")
            mistral_md = ""
            for i, page in enumerate(ocr_response.pages, 1):
                mistral_md += f"## Page {i}\n\n"
                mistral_md += page.markdown + "\n\n"
            
            print(f"   ✅ OCR complete. Extracted {len(mistral_md):,} characters")
            self.logger.info(f"OCR processing complete. Extracted {len(mistral_md)} characters")
            
            if self.debug_mode:
                print(f"   🔧 DEBUG: First 500 characters of extracted text:")
                print(f"   {mistral_md[:500]}...")
            
            return mistral_md, signed_url.url
            
        except Exception as e:
            error_msg = f"Error parsing PDF {pdf_path}: {str(e)}"
            print(f"   ❌ {error_msg}")
            self.logger.error(error_msg)
            raise PatentProcessingError(error_msg)
    
    def parse_multiple_pdfs(self, pdf_paths: list, output_dir: Optional[str] = None) -> dict:
        """
        Parse multiple PDF documents

        Args:
            pdf_paths: List of paths to PDF files
            output_dir: Optional directory to save parsed text files

        Returns:
            Dictionary mapping PDF paths to parsed text
        """
        results = {}

        print(f"📚 Starting batch parsing of {len(pdf_paths)} PDF documents...")
        print(f"🤖 Using Mistral OCR for text extraction")
        self.logger.info(f"Starting batch parsing of {len(pdf_paths)} PDFs")

        successful_parses = 0
        failed_parses = 0
        total_characters = 0

        for i, pdf_path in enumerate(pdf_paths, 1):
            try:
                filename = os.path.basename(pdf_path)
                file_size = os.path.getsize(pdf_path)

                # Show progress for first 5 or every 5th file
                if i <= 5 or i % 5 == 0:
                    print(f"📄 [{i}/{len(pdf_paths)}] Parsing: {filename} ({file_size:,} bytes)")

                parsed_text, signed_url = self.parse_pdf(pdf_path)
                text_length = len(parsed_text)
                total_characters += text_length

                results[pdf_path] = {
                    'parsed_text': parsed_text,
                    'signed_url': signed_url,
                    'success': True,
                    'error': None
                }

                successful_parses += 1

                if i <= 5 or i % 5 == 0:
                    print(f"   ✅ Extracted {text_length:,} characters")

                # Save parsed text to file if output directory is provided
                if output_dir:
                    os.makedirs(output_dir, exist_ok=True)
                    base_name = os.path.splitext(os.path.basename(pdf_path))[0]
                    output_file = os.path.join(output_dir, f"{base_name}_parsed.md")

                    with open(output_file, 'w', encoding='utf-8') as f:
                        f.write(f"# Parsed Text from {os.path.basename(pdf_path)}\n\n")
                        f.write(f"**Source PDF:** {pdf_path}\n")
                        f.write(f"**Signed URL:** {signed_url}\n")
                        f.write(f"**Parsed on:** {self._get_timestamp()}\n\n")
                        f.write("---\n\n")
                        f.write(parsed_text)

                    if i <= 5 or i % 5 == 0:
                        print(f"   💾 Saved to: {os.path.basename(output_file)}")
                    results[pdf_path]['output_file'] = output_file

            except Exception as e:
                error_msg = f"Failed to parse {pdf_path}: {str(e)}"
                if i <= 5 or i % 5 == 0:
                    print(f"   ❌ {error_msg}")
                self.logger.error(error_msg)

                results[pdf_path] = {
                    'parsed_text': None,
                    'signed_url': None,
                    'success': False,
                    'error': error_msg
                }
                failed_parses += 1

            # Show progress every 20 files
            if i % 20 == 0:
                print(f"📊 Progress: {i}/{len(pdf_paths)} processed, {successful_parses} successful, {failed_parses} failed")

        print(f"📊 Batch Parsing Summary:")
        print(f"   Total PDFs: {len(pdf_paths)}")
        print(f"   Successfully parsed: {successful_parses}")
        print(f"   Failed to parse: {failed_parses}")
        print(f"   Total characters extracted: {total_characters:,}")
        if successful_parses > 0:
            avg_chars = total_characters / successful_parses
            print(f"   Average characters per PDF: {avg_chars:,.0f}")
            print(f"   Success rate: {(successful_parses/len(pdf_paths)*100):.1f}%")

        self.logger.info(f"Batch parsing complete: {successful_parses}/{len(pdf_paths)} successful")

        return results
    
    def _get_timestamp(self) -> str:
        """Get current timestamp string"""
        import time
        return time.strftime("%Y-%m-%d %H:%M:%S")
