"""Patent processing service for extracting chemical reactions."""

import os
import json
from typing import Dict, List, Optional, Tuple, Any
from datetime import datetime
from google import genai
from google.genai import types
from app.utils.logging import get_logger
from app.utils.cache_manager import Cache<PERSON>anager
from app.core.providers.gpt import GPTClient
from app.core.exceptions import PatentProcessingError
from app.models.patent_models import ModelType
from app.prompts.reaction_extraction import get_reaction_extraction_prompt
from app.config.settings import get_settings


class PatentProcessor:
    """Service for processing patents to extract chemical reactions."""
    
    def __init__(
        self, 
        model_type: ModelType = ModelType.GEMINI,
        cache_enabled: bool = True
    ):
        """Initialize the patent processor service.
        
        Args:
            model_type: AI model to use (gemini, gpt, or both)
            debug_mode: Enable debug logging
            cache_enabled: Enable caching of processing results
        """
        self.model_type = model_type
        self.logger = get_logger(__name__)
        self.settings = get_settings()
        
        # Initialize cache
        self.cache_manager = CacheManager(
            enabled=cache_enabled
        ) if cache_enabled else None
        
        # Initialize AI clients based on model type
        self.gemini_client = None
        self.gpt_client = None
        
        if model_type in [ModelType.GEMINI, ModelType.BOTH]:
            self._init_gemini_client()
        
        if model_type in [ModelType.GPT, ModelType.BOTH]:
            self._init_gpt_client()
    
    def _init_gemini_client(self):
        """Initialize Gemini client."""
        try:
            if not self.settings.GEMINI_API_KEY:
                raise PatentProcessingError("Gemini API key is required")
            
            self.gemini_client = genai.Client(api_key=self.settings.GEMINI_API_KEY)
            self.gemini_model = "gemini-2.5-pro-preview-05-06"
            
            self.logger.info(f"Initialized Gemini client with model: {self.gemini_model}")
            
        except Exception as e:
            error_msg = f"Failed to initialize Gemini client: {str(e)}"
            self.logger.error(error_msg)
            raise PatentProcessingError(error_msg)
    
    def _init_gpt_client(self):
        """Initialize GPT client."""
        try:
            # Import GPT client (assuming it exists in the original codebase)
                        
            self.gpt_client = GPTClient(
                api_key=self.settings.openai_api_key,
                temperature=1.0
            )
            
            self.logger.info("Initialized GPT client")
            
        except Exception as e:
            error_msg = f"Failed to initialize GPT client: {str(e)}"
            self.logger.error(error_msg)
            raise PatentProcessingError(error_msg)
    
    def process_patent(
        self,
        patent_id: str,
        patent_content: str
    ) -> Dict:
        """Process a patent to extract chemical reactions.
        
        Args:
            patent_id: Patent ID
            patent_content: Combined patent content (abstract, description, claims)
            
        Returns:
            Dictionary with processing results
            
        Raises:
            PatentProcessingError: If processing fails
        """
        try:
            # Check cache first
            if self.cache_manager:
                cache_key = f"{patent_id}_{str(self.model_type)}"
                cached_result = self.cache_manager.get(cache_key)
                if cached_result:
                    self.logger.info(f"Using cached processing result for {patent_id}")
                    return cached_result
            
            self.logger.info(f"Processing patent: {patent_id} with model: {str(self.model_type)}")
            
            # Validate content length
            if len(patent_content) > 1500000:
                raise PatentProcessingError(f"Patent content too long: {len(patent_content)} characters")
            
            # Process based on model type
            results = {}
            
            if self.model_type in [ModelType.GEMINI, ModelType.BOTH]:
                results['gemini'] = self._process_with_gemini(patent_id, patent_content)
            
            if self.model_type in [ModelType.GPT, ModelType.BOTH]:
                results['gpt'] = self._process_with_gpt(patent_id, patent_content)
            
            # Format final result
            if self.model_type == ModelType.GEMINI:
                final_result = {
                    'result': results['gemini'],
                    'model_used': 'gemini'
                }
            elif self.model_type == ModelType.GPT:
                final_result = {
                    'result': results['gpt'],
                    'model_used': 'gpt'
                }
            else:  # BOTH
                final_result = {
                    'gemini_result': results['gemini'],
                    'gpt_result': results['gpt'],
                    'model_used': 'both'
                }
            
            # Cache the result
            if self.cache_manager:
                cache_key = f"{patent_id}_{str(self.model_type)}"
                self.cache_manager.set(cache_key, final_result)
            
            self.logger.info(f"Patent {patent_id} processing complete")
            return final_result
            
        except Exception as e:
            error_msg = f"Error processing patent {patent_id}: {str(e)}"
            self.logger.error(error_msg)
            raise PatentProcessingError(error_msg)

    def extract_reactions(
        self,
        paper_id: str,
        paper_content: str,
        use_paper_prompt: bool = False
    ) -> Dict:
        """Extract chemical reactions from paper content.

        Args:
            paper_id: Paper ID
            paper_content: Paper content (parsed text)
            use_paper_prompt: If True, use paper-specific reaction extraction prompt

        Returns:
            Dictionary with processing results

        Raises:
            PatentProcessingError: If processing fails
        """
        try:
            # Check cache first
            cache_suffix = "_paper" if use_paper_prompt else "_patent"
            if self.cache_manager:
                cache_key = f"{paper_id}_{str(self.model_type)}{cache_suffix}"
                cached_result = self.cache_manager.get(cache_key)
                if cached_result:
                    self.logger.info(f"Using cached processing result for {paper_id}")
                    return cached_result

            self.logger.info(f"Processing {'paper' if use_paper_prompt else 'patent'}: {paper_id} with model: {str(self.model_type)}")

            # Validate content length
            if len(paper_content) > 1500000:
                raise PatentProcessingError(f"Content too long: {len(paper_content)} characters")

            # Process based on model type
            results = {}

            if self.model_type in [ModelType.GEMINI, ModelType.BOTH]:
                results['gemini'] = self._process_with_gemini_paper(paper_id, paper_content, use_paper_prompt)

            if self.model_type in [ModelType.GPT, ModelType.BOTH]:
                results['gpt'] = self._process_with_gpt_paper(paper_id, paper_content, use_paper_prompt)

            # Format final result
            if self.model_type == ModelType.GEMINI:
                final_result = {
                    'result': results['gemini'],
                    'model_used': 'gemini'
                }
            elif self.model_type == ModelType.GPT:
                final_result = {
                    'result': results['gpt'],
                    'model_used': 'gpt'
                }
            else:  # BOTH
                final_result = {
                    'gemini_result': results['gemini'],
                    'gpt_result': results['gpt'],
                    'model_used': 'both'
                }

            # Cache the result
            if self.cache_manager:
                cache_key = f"{paper_id}_{str(self.model_type)}{cache_suffix}"
                self.cache_manager.set(cache_key, final_result)

            self.logger.info(f"{'Paper' if use_paper_prompt else 'Patent'} {paper_id} processing complete")
            return final_result

        except Exception as e:
            error_msg = f"Error processing {'paper' if use_paper_prompt else 'patent'} {paper_id}: {str(e)}"
            self.logger.error(error_msg)
            raise PatentProcessingError(error_msg)
    
    def _process_with_gemini(self, patent_id: str, content: str) -> str:
        """Process patent content with Gemini model."""
        try:
            prompt = get_reaction_extraction_prompt("gemini")

            self.logger.debug(f"Sending request to Gemini for patent: {patent_id}")

            response = self._generate_with_gemini(prompt, content)

            # Post-process response
            processed_response = self._post_process_response(response)

            self.logger.debug(f"Received response from Gemini for patent: {patent_id}")
            return processed_response

        except Exception as e:
            error_msg = f"Gemini processing error for {patent_id}: {str(e)}"
            self.logger.error(error_msg)
            return f"Error: {error_msg}"

    def _process_with_gpt(self, patent_id: str, content: str) -> str:
        """Process patent content with GPT model."""
        try:
            prompt = get_reaction_extraction_prompt("gpt")

            self.logger.debug(f"Sending request to GPT for patent: {patent_id}")

            response = self._generate_with_gpt(prompt, content)

            # Post-process response
            processed_response = self._post_process_response(response)

            self.logger.debug(f"Received response from GPT for patent: {patent_id}")
            return processed_response

        except Exception as e:
            error_msg = f"GPT processing error for {patent_id}: {str(e)}"
            self.logger.error(error_msg)
            return f"Error: {error_msg}"

    def _process_with_gemini_paper(self, paper_id: str, content: str, use_paper_prompt: bool = False) -> str:
        """Process paper content with Gemini model."""
        try:
            prompt = get_reaction_extraction_prompt("gemini", use_paper_prompt)

            self.logger.debug(f"Sending request to Gemini for {'paper' if use_paper_prompt else 'patent'}: {paper_id}")

            response = self._generate_with_gemini(prompt, content)

            # Post-process response
            processed_response = self._post_process_response(response)

            self.logger.debug(f"Received response from Gemini for {'paper' if use_paper_prompt else 'patent'}: {paper_id}")
            return processed_response

        except Exception as e:
            error_msg = f"Gemini processing error for {paper_id}: {str(e)}"
            self.logger.error(error_msg)
            return f"Error: {error_msg}"

    def _process_with_gpt_paper(self, paper_id: str, content: str, use_paper_prompt: bool = False) -> str:
        """Process paper content with GPT model."""
        try:
            prompt = get_reaction_extraction_prompt("gpt", use_paper_prompt)

            self.logger.debug(f"Sending request to GPT for {'paper' if use_paper_prompt else 'patent'}: {paper_id}")

            response = self._generate_with_gpt(prompt, content)

            # Post-process response
            processed_response = self._post_process_response(response)

            self.logger.debug(f"Received response from GPT for {'paper' if use_paper_prompt else 'patent'}: {paper_id}")
            return processed_response

        except Exception as e:
            error_msg = f"GPT processing error for {paper_id}: {str(e)}"
            self.logger.error(error_msg)
            return f"Error: {error_msg}"

    def _generate_with_gemini(self, prompt: str, content: str) -> str:
        """Generate response using Gemini API."""
        try:
            full_prompt = f"{prompt}\n\n{content}"

            contents = [
                types.Content(
                    role="user",
                    parts=[types.Part.from_text(text=full_prompt)],
                ),
            ]

            generate_content_config = types.GenerateContentConfig(
                response_mime_type="text/plain",
            )

            response_text = ""
            for chunk in self.gemini_client.models.generate_content_stream(
                model=self.gemini_model,
                contents=contents,
                config=generate_content_config,
            ):
                response_text += chunk.text

            return response_text

        except Exception as e:
            raise PatentProcessingError(f"Gemini API error: {str(e)}")

    def _generate_with_gpt(self, prompt: str, content: str) -> str:
        """Generate response using GPT API."""
        try:
            if not self.gpt_client:
                raise PatentProcessingError("GPT client not initialized")

            response = self.gpt_client.answer_wo_vision(prompt, content)
            return response

        except Exception as e:
            raise PatentProcessingError(f"GPT API error: {str(e)}")

    def _post_process_response(self, response: str) -> str:
        """Post-process AI response to clean up formatting."""
        try:
            # Replace zeros in reactants and products (from original code)
            import re
            response = re.sub(r'\b0\b', '', response)

            # Extract final output section if it exists
            if "Final Output:" in response:
                final_output = response.split("Final Output:")[-1].strip()
            else:
                final_output = response

            return final_output

        except Exception as e:
            self.logger.warning(f"Post-processing error: {str(e)}")
            return response
