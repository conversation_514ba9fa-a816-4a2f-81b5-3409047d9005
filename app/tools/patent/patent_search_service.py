"""Patent search service using Azure Cognitive Search."""

import json
import requests
from typing import List, Dict, Optional
from app.core.exceptions import PatentSearchError
from app.models.patent_models import PatentDetails
from app.config.settings import get_settings
from app.utils.logging import get_logger
from app.utils.cache_manager import CacheManager
from app.prompts.reaction_extraction import get_reaction_extraction_prompt
from app.config.settings import get_settings


class PatentSearchService:
    """Service for searching patents using Azure Cognitive Search."""
    
    def __init__(self):
        """Initialize the patent search service.
        
        
        """
        self.logger = get_logger(__name__)
        self.settings = get_settings()
        
    def search_patents_by_smiles(self, smiles: str, max_results: int = 10) -> List[str]:
        """Search for patents related to a SMILES string.

        Args:
            smiles: SMILES string to search for
            max_results: Maximum number of patent IDs to return

        Returns:
            List of patent IDs

        Raises:
            PatentSearchError: If search fails
        """
        try:
            self.logger.info(f"Searching patents for SMILES: {smiles}")

            # Clean SMILES (remove stereochemistry like in the example)
            cleaned_smiles = self._clean_smiles(smiles)

            # Use the same approach as azure_patent_search_example.py
            service_name = self.settings.AZURE_SEARCH_SERVICE_NAME or "mstack-reactions-search-service"
            api_key = self.settings.AZURE_SEARCH_API_KEY
            if not api_key:
                raise PatentSearchError("Azure search API key not configured")

            # Construct URL like in the example
            url = f"https://{service_name}.search.windows.net/indexes/molecule_patents/docs"

            headers = {
                "api-key": api_key,
                "Content-Type": "application/json"
            }

            # Use GET with filter parameter like in the example
            params = {
                'api-version': '2023-11-01',
                '$filter': f"smiles eq '{cleaned_smiles}'"
            }

            self.logger.debug(f"Azure search URL: {url}")
            self.logger.debug(f"Search filter: smiles eq '{cleaned_smiles}'")

            # Make search request
            response = requests.get(url, headers=headers, params=params, timeout=30)

            if response.status_code != 200:
                raise PatentSearchError(f"Azure search failed: {response.status_code} - {response.text}")

            # Parse response
            search_results = response.json()
            patent_ids = self._extract_patent_ids_from_azure(search_results, max_results)

            self.logger.info(f"Found {len(patent_ids)} patent IDs for SMILES: {smiles}")
            return patent_ids

        except requests.RequestException as e:
            error_msg = f"Network error during patent search: {str(e)}"
            self.logger.error(error_msg)
            raise PatentSearchError(error_msg)
        except Exception as e:
            error_msg = f"Unexpected error during patent search: {str(e)}"
            self.logger.error(error_msg)
            raise PatentSearchError(error_msg)

    def _clean_smiles(self, smiles: str) -> str:
        """Clean SMILES string by removing stereochemistry."""
        try:
            # Try to import RDKit for SMILES cleaning
            from rdkit import Chem
            mol = Chem.MolFromSmiles(smiles)
            if mol is None:
                return smiles  # Return original if invalid
            Chem.RemoveStereochemistry(mol)
            return Chem.MolToSmiles(mol, isomericSmiles=False)
        except ImportError:
            # If RDKit not available, return original SMILES
            self.logger.warning("RDKit not available for SMILES cleaning")
            return smiles
        except Exception as e:
            self.logger.warning(f"Error cleaning SMILES: {str(e)}")
            return smiles

    def _extract_patent_ids_from_azure(self, search_results: Dict, max_results: int) -> List[str]:
        """Extract patent IDs from Azure search results using the same logic as the example."""
        patent_ids = []
        try:
            if 'value' in search_results:
                for document in search_results['value']:
                    if 'patcid_patents' in document and isinstance(document['patcid_patents'], list):
                        patent_ids.extend(document['patcid_patents'])

            # Filter for US patents
            # us_patent_ids = [pid for pid in patent_ids if str(pid).upper()]

            # Deduplicate patents that differ only by kind codes, preferring versions with kind codes
            # deduplicated_ids = self._deduplicate_patent_ids(patent_ids)

            return patent_ids[:max_results]

        except Exception as e:
            self.logger.error(f"Error extracting patent IDs from Azure results: {str(e)}")
            return []

    def _extract_patent_ids(self, search_results: Dict, max_results: int) -> List[str]:
        """Extract patent IDs from Azure search results.
        
        Args:
            search_results: Raw search results from Azure
            max_results: Maximum number of IDs to return
            
        Returns:
            List of unique patent IDs
        """
        patent_ids = set()
        
        try:
            for result in search_results.get("value", []):
                patents_field = result.get("patcid_patents", [])
                
                if isinstance(patents_field, list):
                    for patent_entry in patents_field:
                        if isinstance(patent_entry, str):
                            # Extract patent ID from string format
                            patent_id = self._parse_patent_id(patent_entry)
                            if patent_id:
                                patent_ids.add(patent_id)
                        elif isinstance(patent_entry, dict):
                            # Extract from dictionary format
                            patent_id = patent_entry.get("patent_id") or patent_entry.get("id")
                            if patent_id:
                                patent_ids.add(patent_id)
                
                # Stop if we have enough results
                if len(patent_ids) >= max_results:
                    break
            
            # Convert to list and limit results
            result_list = list(patent_ids)[:max_results]
            self.logger.debug(f"Extracted {len(result_list)} unique patent IDs")
            return result_list
            
        except Exception as e:
            self.logger.error(f"Error extracting patent IDs: {str(e)}")
            return []
    
    def _parse_patent_id(self, patent_string: str) -> Optional[str]:
        """Parse patent ID from various string formats.
        
        Args:
            patent_string: String containing patent information
            
        Returns:
            Extracted patent ID or None
        """
        try:
            # Handle different formats like "*********" or "**********-A1"
            import re
            
            # Pattern for US patents
            us_pattern = r'US[- ]?(\d{7,8})[- ]?[A-Z]?\d?'
            match = re.search(us_pattern, patent_string, re.IGNORECASE)
            if match:
                return f"US{match.group(1)}"
            
            # Pattern for other patent formats
            general_pattern = r'([A-Z]{2})(\d{7,10})'
            match = re.search(general_pattern, patent_string, re.IGNORECASE)
            if match:
                return f"{match.group(1).upper()}{match.group(2)}"
            
            return None
            
        except Exception:
            return None

    def _parse_patent_components(self, patent_id: str) -> tuple[str, str]:
        """Parse patent ID into base number and kind code.

        Args:
            patent_id: Patent ID (e.g., "US20140135317A1" or "US20140135317")

        Returns:
            Tuple of (base_number, kind_code) where kind_code is empty string if not present
        """
        try:
            import re

            # Pattern to match US patent with optional kind code
            pattern = r'^(US\d{7,11})([A-Z]\d?)?$'
            match = re.match(pattern, patent_id.upper().strip())

            if match:
                base_number = match.group(1)
                kind_code = match.group(2) or ""
                return base_number, kind_code

            # If no match, return the original as base with empty kind code
            return patent_id.upper().strip(), ""

        except Exception:
            return patent_id.upper().strip(), ""

    def _deduplicate_patent_ids(self, patent_ids: List[str]) -> List[str]:
        """Deduplicate patent IDs that differ only by kind codes.

        When duplicates are found (same base number), prefer the version with kind code.

        Args:
            patent_ids: List of patent IDs that may contain duplicates

        Returns:
            List of deduplicated patent IDs, preferring versions with kind codes
        """
        try:
            # Group patents by base number
            patent_groups = {}

            for patent_id in patent_ids:
                base_number, kind_code = self._parse_patent_components(patent_id)

                if base_number not in patent_groups:
                    patent_groups[base_number] = []

                patent_groups[base_number].append((patent_id, kind_code))

            # For each group, select the best version
            deduplicated = []
            for base_number, versions in patent_groups.items():
                # Sort by: 1) has kind code (True first), 2) alphabetically by kind code
                # This ensures we prefer versions with kind codes, and if multiple have kind codes,
                # we get a consistent ordering
                best_version = sorted(versions, key=lambda x: (x[1] == "", x[1]))[0]
                deduplicated.append(best_version[0])

            self.logger.debug(f"Deduplicated {len(patent_ids)} patents to {len(deduplicated)} unique patents")

            # Log some examples of deduplication for debugging
            if len(patent_ids) > len(deduplicated):
                examples = []
                for base_number, versions in patent_groups.items():
                    if len(versions) > 1:
                        version_strs = [v[0] for v in versions]
                        selected = sorted(versions, key=lambda x: (x[1] == "", x[1]))[0][0]
                        examples.append(f"{version_strs} -> {selected}")
                        if len(examples) >= 3:  # Show max 3 examples
                            break

                if examples:
                    self.logger.info(f"Deduplication examples: {'; '.join(examples)}")

            return deduplicated

        except Exception as e:
            self.logger.error(f"Error during patent ID deduplication: {str(e)}")
            # If deduplication fails, return original list
            return patent_ids
