"""Chemical name to SMILES conversion service."""

import re
import urllib.parse
from typing import <PERSON><PERSON>, Optional, List
import requests
import aiohttp
from rdkit import Chem
from app.utils.logging import get_logger
from app.core.exceptions import ChemicalConversionError


class ChemicalConverter:
    """Service for converting chemical names to SMILES strings."""

    def __init__(self):
        """Initialize the chemical converter.

        
        """
        self.logger = get_logger(__name__)

    def convert(self, chemical_input: str) -> Tuple[bool, str]:
        """Convert chemical name to SMILES string.

        Args:
            chemical_input: Chemical name or SMILES string

        Returns:
            Tuple of (success, result_or_error_message)
        """
        try:
            # Check if input is already a valid SMILES
            if self._is_likely_smiles(chemical_input):
                self.logger.info(f"Input appears to be a SMILES string: {chemical_input}")
                return True, chemical_input

            # Generate name variants for better success rate
            name_variants = self._generate_name_variants(chemical_input)

            # Try conversion methods with all variants
            conversion_methods = [
                ('PubChem', self._convert_via_pubchem),
                ('CIR', self._convert_via_cir),
                ('OPSIN', self._convert_via_opsin)
            ]

            for variant in name_variants:
                for method_name, method in conversion_methods:
                    try:
                        success, result = method(variant)
                        if success:
                            self.logger.info(f"Successfully converted '{chemical_input}' (variant: '{variant}') to SMILES via {method_name}: {result}")
                            return True, result
                    except Exception as e:
                        self.logger.debug(f"{method_name} failed for '{variant}': {str(e)}")
                        continue

            # If all methods fail
            error_msg = f"Could not convert '{chemical_input}' to SMILES using any available method"
            self.logger.error(error_msg)
            return False, error_msg

        except Exception as e:
            error_msg = f"Unexpected error during conversion: {str(e)}"
            self.logger.error(error_msg)
            return False, error_msg
    
    def _is_likely_smiles(self, input_str: str) -> bool:
        """Check if input string is likely a SMILES string."""
        return bool(re.search(r"[=#@\\/[\]]", input_str) and not input_str.startswith("InChI="))

    def _generate_name_variants(self, name: str) -> List[str]:
        """Generate name variants for better conversion success."""
        variants = set()
        original = name.strip()
        variants.add(original)

        # Case variations
        variants.add(original.lower())
        variants.add(original.title())

        # Remove common suffixes (salts, hydrates)
        cleaned = self._remove_common_suffixes(original)
        if cleaned != original:
            variants.add(cleaned)
            variants.add(cleaned.lower())

        return [v for v in variants if v.strip()]

    def _remove_common_suffixes(self, name: str) -> str:
        """Remove common salt and hydrate suffixes."""
        suffixes = ['hydrochloride', 'hcl', 'sulfate', 'phosphate', 'acetate',
                   'monohydrate', 'dihydrate', 'hydrate', 'sodium salt', 'potassium salt']

        name_lower = name.lower()
        for suffix in suffixes:
            patterns = [f' {suffix}$', f', {suffix}$']
            for pattern in patterns:
                if re.search(pattern, name_lower):
                    match = re.search(pattern, name_lower)
                    if match:
                        return name[:match.start()].strip()
        return name
    
    def _validate_smiles(self, smiles: str) -> bool:
        """Validate SMILES string using RDKit."""
        try:
            mol = Chem.MolFromSmiles(smiles)
            return mol is not None
        except:
            return False
    
    def _convert_via_pubchem(self, name: str) -> Tuple[bool, str]:
        """Convert chemical name to SMILES using PubChem."""
        try:
            encoded_name = urllib.parse.quote(name)
            url = f"https://pubchem.ncbi.nlm.nih.gov/rest/pug/compound/name/{encoded_name}/property/IsomericSMILES,CanonicalSMILES/JSON"
            response = requests.get(url, timeout=10)

            if response.status_code == 200:
                data = response.json()
                properties = data['PropertyTable']['Properties'][0]
                smiles = properties.get('IsomericSMILES', properties.get('CanonicalSMILES'))
                if smiles:
                    return True, smiles

            return False, "PubChem conversion failed"

        except Exception as e:
            return False, f"PubChem error: {str(e)}"
    
    def _convert_via_cir(self, name: str) -> Tuple[bool, str]:
        """Convert chemical name to SMILES using Chemical Identifier Resolver."""
        try:
            encoded_name = urllib.parse.quote(name)
            url = f"http://cactus.nci.nih.gov/chemical/structure/{encoded_name}/smiles"
            response = requests.get(url, timeout=10)

            if response.status_code == 200:
                smiles = response.text.strip()
                if smiles and len(smiles) > 0:
                    return True, smiles

            return False, "CIR conversion failed"

        except Exception as e:
            return False, f"CIR error: {str(e)}"
    
    def _convert_via_opsin(self, name: str) -> Tuple[bool, str]:
        """Convert chemical name to SMILES using OPSIN API."""
        try:
            encoded_name = urllib.parse.quote(name)
            url = f"https://opsin.ch.cam.ac.uk/opsin/{encoded_name}.json"
            response = requests.get(url, timeout=10)

            if response.status_code == 200:
                data = response.json()
                if data.get("status") == "SUCCESS":
                    smiles = data.get("smiles")
                    if smiles:
                        return True, smiles

            return False, "OPSIN conversion failed"

        except Exception as e:
            return False, f"OPSIN error: {str(e)}"
