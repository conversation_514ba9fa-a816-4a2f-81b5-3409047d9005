"""Patent preprocessing service for filtering patents by relevance."""

import json
import datetime
import time
from typing import Dict, Optional, Tuple, List
from google import genai
from google.genai import types
from app.core.exceptions import PatentPreprocessingError

from app.utils.logging import get_logger
from app.utils.cache_manager import CacheManager
from app.utils.token_manager import TokenManager
from app.models.patent_models import ModelType
from app.prompts.reaction_extraction import get_reaction_extraction_prompt
from app.prompts.patent_preprocessing import get_preprocessing_prompt
from app.config.settings import get_settings
from app.utils.context_helpers import request_id_var
from app.utils.es_helper import *
import dateutil.parser





class PatentPreprocessor:
    """Service for preprocessing patents to filter by relevance to input compound."""
    
    def __init__(self, cache_enabled: bool = True, token_optimized: bool = True):
        """Initialize the patent preprocessor service.

        Args:
            debug_mode: Enable debug logging
            cache_enabled: Enable caching of preprocessing results
            token_optimized: Enable token-optimized batch processing
        """
        self.token_optimized = token_optimized
        self.logger = get_logger(__name__)
        self.settings = get_settings()
        self.es_client = get_search_client(
            {
                "provider": self.settings.SEARCH_PROVIDER,
                "endpoint": f"https://{self.settings.AZURE_SEARCH_SERVICE_NAME}.search.windows.net",
                "index_name": self.settings.AZURE_PATENT_SUMMARY_INDEX_NAME,
                "api_key": self.settings.AZURE_SEARCH_API_KEY
            }
        )

        # Initialize cache
        self.cache_manager = CacheManager(
            enabled=cache_enabled
        ) if cache_enabled else None

        # Initialize token manager for optimization
        self.token_manager = TokenManager() if token_optimized else None

        # Initialize Gemini client
        self._init_gemini_client()
    
    def _init_gemini_client(self):
        """Initialize Gemini client for preprocessing."""
        try:
            if not self.settings.GEMINI_API_KEY:
                raise PatentPreprocessingError("Gemini API key is required")
            
            self.gemini_client = genai.Client(api_key=self.settings.GEMINI_API_KEY)
            self.gemini_model = "gemini-2.5-flash-preview-05-20"
            
            self.logger.info(f"Initialized Gemini client with model: {self.gemini_model}")
            
        except Exception as e:
            error_msg = f"Failed to initialize Gemini client: {str(e)}"
            self.logger.error(error_msg)
            raise PatentPreprocessingError(error_msg)
    
    def analyze_patent_relevance(
        self,
        patent_id: str,
        chemical_name: str,
        smiles: str,
        patent_details: Dict
    ) -> Dict:
        """Analyze if a patent is relevant to the input compound.
        
        Args:
            patent_id: Patent ID
            chemical_name: Chemical name of input compound
            smiles: SMILES string of input compound
            patent_details: Patent details (abstract, description, claims)
            
        Returns:
            Dictionary with relevance analysis results
            
        Raises:
            PatentPreprocessingError: If analysis fails
        """
        try:
            # Check cache first
            if self.cache_manager:
                cache_key = f"{patent_id}_{chemical_name}_{smiles}"
                cached_result = self.cache_manager.get(cache_key)
                if cached_result:
                    self.logger.info(f"Using cached preprocessing result for {patent_id}")
                    return cached_result
            
            self.logger.info(f"Analyzing patent relevance for: {patent_id}")
            
            # Prepare prompt
            prompt = get_preprocessing_prompt().format(
                chemical_name=chemical_name,
                smiles=smiles,
                abstract=patent_details.get('abstract', 'Not available'),
                description=patent_details.get('description', 'Not available'),
                claims=patent_details.get('claims', 'Not available')
            )
            
            # Generate response using Gemini
            response_text = self._generate_with_gemini(prompt)
            
            # Parse JSON response
            result = self._parse_response(response_text, patent_id)

            # Add metadata fields from SERP response
            result = self._add_metadata_fields(result, patent_details)

            # Cache the result
            if self.cache_manager:
                cache_key = f"{patent_id}_{chemical_name}_{smiles}"
                self.cache_manager.set(cache_key, result)

            self.logger.info(
                f"Patent {patent_id} analysis complete. "
                f"Score: {result.get('relevancyScore', 0)}/10"
            )

            return result

        except Exception as e:
            error_msg = f"Error analyzing patent relevance for {patent_id}: {str(e)}"
            self.logger.error(error_msg)
            raise PatentPreprocessingError(error_msg)

    def analyze_patents_batch_optimized(
        self,
        patent_data: List[Tuple[str, str, str, Dict]],
        use_paper_prompt: bool = False,
        params : Dict = {},
    ) -> Dict[str, Dict]:
        """Analyze multiple patents with token optimization.

        Args:
            patent_data: List of (patent_id, chemical_name, smiles, patent_details) tuples
            use_paper_prompt: If True, use paper-specific preprocessing prompt

        Returns:
            Dictionary mapping patent_id to analysis results
        """
        if not self.token_optimized or not self.token_manager:
            # Fallback to individual processing
            return self._analyze_patents_individually(patent_data, use_paper_prompt)

        results = {}

        # Create token-optimized batches
        batches = self.token_manager.create_token_optimized_batches(patent_data)

        print(f"   📊 Created {len(batches)} token-optimized batches for {len(patent_data)} items")
        self.logger.info(f"Processing {len(patent_data)} patents in {len(batches)} token-optimized batches")

        for batch_idx, batch in enumerate(batches, 1):
            print(f"   ⚙️  Processing batch {batch_idx}/{len(batches)} with {len(batch)} items...")
            self.logger.info(f"Processing batch {batch_idx}/{len(batches)} with {len(batch)} patents")

            # Wait for rate limit if necessary
            self.token_manager.wait_for_rate_limit_reset()

            # Process batch
            batch_results = self._process_batch(batch, batch_idx, use_paper_prompt)
            results.update(batch_results)
            for patent_id, details in batch_results.items():
                try:
                    self.es_client.insert_document(
                        document={
                            "patent_id": patent_id,
                            "request_id": params.get("request_id"),
                            "chemical_name": params.get("chemical_name"),
                            "created_at" : datetime.datetime.now(),
                            **details
                        }
                    )
                except Exception as e:
                    self.logger.error(f"Failed to insert patent {patent_id} into ES: {str(e)}")

            # Log progress
            usage = self.token_manager.get_current_usage()
            print(f"   ✅ Batch {batch_idx} complete. Token usage: {usage['current_minute_tokens']:,}/{usage['effective_limit']:,}")
            self.logger.info(
                f"Batch {batch_idx} complete. "
                f"Token usage: {usage['current_minute_tokens']:,}/{usage['effective_limit']:,}"
            )

        return results

    def _analyze_patents_individually(self, patent_data: List[Tuple[str, str, str, Dict]], use_paper_prompt: bool = False) -> Dict[str, Dict]:
        """Fallback method to analyze patents individually."""
        results = {}

        for patent_id, chemical_name, smiles, patent_details in patent_data:
            try:
                result = self.analyze_patent_relevance(patent_id, chemical_name, smiles, patent_details)
                results[patent_id] = result
            except Exception as e:
                self.logger.error(f"Error processing patent {patent_id}: {str(e)}")
                error_result = {
                    "error": str(e),
                    "summaryOfClaims": "Analysis failed",
                    "reactionsInClaims": "Analysis failed",
                    "claims": {},
                    "tags": [],
                    "relevancyScore": 0
                }
                # Add metadata fields even for errors
                error_result = self._add_metadata_fields(error_result, patent_details)
                results[patent_id] = error_result

        return results

    def _process_batch(self, batch: List[Tuple[str, str, str, Dict]], batch_idx: int, use_paper_prompt: bool = False) -> Dict[str, Dict]:
        """Process a single batch of patents.

        Args:
            batch: List of (patent_id, chemical_name, smiles, patent_details) tuples
            batch_idx: Batch index for logging
            use_paper_prompt: If True, use paper-specific preprocessing prompt

        Returns:
            Dictionary mapping patent_id to analysis results
        """
        batch_results = {}
        total_tokens_used = 0

        for i, (patent_id, chemical_name, smiles, patent_details) in enumerate(batch, 1):
            try:
                # Check cache first
                if self.cache_manager:
                    cache_key = f"{patent_id}_{chemical_name}_{smiles}"
                    cached_result = self.cache_manager.get(cache_key)
                    if cached_result:
                        batch_results[patent_id] = cached_result
                        self.logger.debug(f"Batch {batch_idx}, Patent {i}/{len(batch)}: {patent_id} (cached)")
                        continue

                retry_count = 0
                max_retries = 3
                backoff_time = 5

                while retry_count < max_retries:
                    try:
                        # Estimate tokens before processing
                        estimated_tokens = self.token_manager.estimate_preprocessing_tokens(
                            chemical_name, smiles, patent_details
                        )

                        # Prepare prompt
                        if use_paper_prompt:
                            prompt = get_preprocessing_prompt(use_paper_prompt=True).format(
                                chemical_name=chemical_name,
                                parsed_text=patent_details.get('parsed_text', 'Not available')
                            )
                        else:
                            prompt = get_preprocessing_prompt(use_paper_prompt=False).format(
                                chemical_name=chemical_name,
                                smiles=smiles,
                                abstract=patent_details.get('abstract', 'Not available'),
                                description=patent_details.get('description', 'Not available'),
                                claims=patent_details.get('claims', 'Not available')
                            )

                        # Generate response
                        response_text = self._generate_with_gemini(prompt)

                        # Parse response
                        result = self._parse_response(response_text, patent_id, use_paper_prompt)
                        if result.get('summaryOfClaims')=="Analysis failed":
                            raise Exception("Analysis failed")

                        # Add metadata fields from SERP response
                        result = self._add_metadata_fields(result, patent_details)

                        batch_results[patent_id] = result

                        # Record token usage
                        self.token_manager.record_token_usage(estimated_tokens)
                        total_tokens_used += estimated_tokens

                        # Cache the result
                        if self.cache_manager:
                            cache_key = f"{patent_id}_{chemical_name}_{smiles}"
                            self.cache_manager.set(cache_key, result)

                        self.logger.debug(
                            f"Batch {batch_idx}, Patent {i}/{len(batch)}: {patent_id} "
                            f"(score: {result.get('relevancyScore', 0)}, tokens: ~{estimated_tokens:,})"
                        )
                        break  # Success, exit retry loop

                    except Exception as e:
                        retry_count += 1
                        if retry_count < max_retries:
                            self.logger.warning(f"Attempt {retry_count} failed for patent {patent_id}. Retrying in {backoff_time} seconds. Error: {str(e)}")
                            time.sleep(backoff_time)
                            backoff_time *= 2  # Exponential backoff
                        else:
                            raise  # Re-raise the exception after max retries

            except Exception as e:
                self.logger.error(f"Error processing {'paper' if use_paper_prompt else 'patent'} {patent_id} in batch {batch_idx}: {str(e)}")
                if use_paper_prompt:
                    error_result = {
                        "error": str(e),
                        "summaryOfPaper": "Analysis failed",
                        "inputCompoundRelation": "Analysis failed",
                        "relevancyScore": 0
                    }
                else:
                    error_result = {
                        "error": str(e),
                        "summaryOfClaims": "Analysis failed",
                        "reactionsInClaims": "Analysis failed",
                        "claims": {},
                        "tags": [],
                        "relevancyScore": 0
                    }
                # Add metadata fields even for errors (only for patents)
                if not use_paper_prompt:
                    error_result = self._add_metadata_fields(error_result, patent_details)
                batch_results[patent_id] = error_result

        self.logger.info(
            f"Batch {batch_idx} processed: {len(batch_results)} patents, "
            f"~{total_tokens_used:,} tokens used"
        )

        return batch_results

    def _generate_with_gemini(self, prompt: str) -> str:
        """Generate response using Gemini API.
        
        Args:
            prompt: Full prompt for analysis
            
        Returns:
            Generated response text
        """
        try:
            contents = [
                types.Content(
                    role="user",
                    parts=[types.Part.from_text(text=prompt)],
                ),
            ]
            
            generate_content_config = types.GenerateContentConfig(
                response_mime_type="text/plain",
            )
            
            # Collect the full response
            response_text = ""
            for chunk in self.gemini_client.models.generate_content_stream(
                model=self.gemini_model,
                contents=contents,
                config=generate_content_config,
            ):
                response_text += chunk.text
            
            if not response_text.strip():
                raise PatentPreprocessingError("Empty response from Gemini API")
            
            return response_text
            
        except Exception as e:
            error_msg = f"Gemini API error: {str(e)}"
            self.logger.error(error_msg)
            raise PatentPreprocessingError(error_msg)

    def _parse_response(self, response_text: str, patent_id: str, use_paper_prompt: bool = False) -> Dict:
        """Parse JSON response from Gemini with robust extraction."""
        try:
            # Clean the response
            cleaned_response = response_text.strip()

            self.logger.debug(f"Raw response for {patent_id}: {repr(response_text[:500])}")

            json_str = None

            # Approach 1: Look for ```json code blocks
            if '```json' in cleaned_response:
                start_marker = '```json'
                end_marker = '```'
                start_idx = cleaned_response.find(start_marker) + len(start_marker)
                end_idx = cleaned_response.find(end_marker, start_idx)
                if end_idx != -1:
                    json_str = cleaned_response[start_idx:end_idx].strip()

            # Approach 2: Look for regular code blocks (``` ... ```)
            elif '```' in cleaned_response and json_str is None:
                start_marker = '```'
                end_marker = '```'
                start_idx = cleaned_response.find(start_marker) + len(start_marker)
                end_idx = cleaned_response.find(end_marker, start_idx)
                if end_idx != -1:
                    potential_json = cleaned_response[start_idx:end_idx].strip()
                    # Check if it starts with { (likely JSON)
                    if potential_json.startswith('{'):
                        json_str = potential_json

            # Approach 3: Look for JSON object directly
            if json_str is None and '{' in cleaned_response:
                start_idx = cleaned_response.find('{')
                end_idx = cleaned_response.rfind('}') + 1
                if end_idx > start_idx:
                    json_str = cleaned_response[start_idx:end_idx]

            if not json_str:
                raise ValueError("No JSON found in response")

            # Parse JSON
            result = json.loads(json_str)

            # Ensure required fields exist based on prompt type
            if use_paper_prompt:
                required = ['summaryOfPaper', 'inputCompoundRelation', 'relevancyScore']
            else:
                required = ['summaryOfClaims', 'reactionsInClaims', 'claims', 'tags', 'relevancyScore']

            for field in required:
                if field not in result:
                    if field == 'relevancyScore':
                        result[field] = 0
                    elif field == 'claims':
                        result[field] = []
                    elif field == 'tags':
                        result[field] = []
                    else:
                        result[field] = "Not provided"

            # Ensure relevancyScore is a number
            if not isinstance(result.get('relevancyScore'), (int, float)):
                try:
                    result['relevancyScore'] = int(result.get('relevancyScore', 0))
                except (ValueError, TypeError):
                    result['relevancyScore'] = 0

            # Validate and fix claims structure for patents
            all_claims = []
            claim_tags = []
            if not use_paper_prompt and 'claims' in result and isinstance(result['claims'], dict):
                for claim_key , claim_data in result['claims'].items():
                    if isinstance(claim_data, dict):
                        # Ensure required claim fields exist with proper defaults
                        if 'id' not in claim_data:
                            claim_data['id'] = claim_key
                        if 'claim' not in claim_data:
                            claim_data['claim'] = "Claim text not provided"
                        if 'tag' not in claim_data:
                            claim_data['tag'] = "Process"
                        if 'dependent' not in claim_data:
                            claim_data['dependent'] = False
                        if 'depends_on' not in claim_data:
                            claim_data['depends_on'] = ""

                        # Ensure dependent is boolean
                        if not isinstance(claim_data.get('dependent'), bool):
                            claim_data['dependent'] = False

                        # Ensure depends_on is string
                        if not isinstance(claim_data.get('depends_on'), str):
                            claim_data['depends_on'] = ""
                        all_claims.append(claim_data)
                        claim_tags.append(claim_data.get('tag', 'Process'))
            
            result['claims'] = all_claims
            result['claim_tags'] = list(set(claim_tags))
            return result

        except Exception as e:
            self.logger.error(f"Parse error for {patent_id}: {str(e)}")
            if use_paper_prompt:
                return {
                    "summaryOfPaper": "Analysis failed",
                    "inputCompoundRelation": "Analysis failed",
                    "relevancyScore": 0
                }
            else:
                return {
                    "summaryOfClaims": "Analysis failed",
                    "reactionsInClaims": "Analysis failed",
                    "claims": [],
                    "tags": [],
                    "claim_tags": [],
                    "relevancyScore": 0,
                    "abstract": "",
                    "title": "",
                    "inventors": [],
                    "assignees": [],
                    "publication_date": None,
                    "filing_date": None,
                    "pdf_url": ""
                }
    
    def convert_date(self, date_str):
        """Convert date string to datetime object with error handling"""
        if not date_str:
            return None
        try:
            return dateutil.parser.parse(date_str)
        except Exception as e:
            logger.warning(f"Failed to parse date '{date_str}': {e}")
            return None

    def _add_metadata_fields(self, result: Dict, patent_details: Dict) -> Dict:
        """Add metadata fields from SERP response to the preprocessing result.

        Args:
            result: The preprocessing analysis result
            patent_details: The SERP API response containing patent metadata

        Returns:
            Updated result with metadata fields
        """
        # Get the raw SERP response which contains the correct metadata fields
        raw_serp = patent_details.get("raw_serp_response", {})

        # If raw_serp_response doesn't exist, fall back to top-level fields
        source_data = raw_serp if raw_serp else patent_details

        # Extract metadata fields from SERP response
        result["abstract"] = source_data.get("abstract", "")
        result["title"] = source_data.get("title", "")
        result["publication_date"] = self.convert_date(source_data.get("publication_date", ""))
        result["filing_date"] = self.convert_date(source_data.get("filing_date", ""))
        result["pdf_url"] = source_data.get("pdf", "")

        for idx, claim in enumerate(result.get('claims', [])):
            claims_html = patent_details.get("claims_html", [])
            if idx < len(claims_html) and claims_html[idx]:
                claim['claim'] = claims_html[idx]

        # Extract assignees as a list
        assignees = source_data.get("assignees", [])
        if isinstance(assignees, list):
            result["assignees"] = assignees
        else:
            # Fallback to singular assignee field if it exists and convert to list
            single_assignee = source_data.get("assignee", "")
            result["assignees"] = [single_assignee] if single_assignee else []

        # Extract inventor names from the inventors list
        inventors = source_data.get("inventors", [])
        if isinstance(inventors, list):
            # Extract just the names from the inventor objects
            inventor_names = []
            for inventor in inventors:
                if isinstance(inventor, dict) and "name" in inventor:
                    inventor_names.append(inventor["name"])
                elif isinstance(inventor, str):
                    inventor_names.append(inventor)
            result["inventors"] = inventor_names
        else:
            result["inventors"] = []

        return result
