"""Google Scholar synthesis search and download service using SerpAPI."""

import os
import re
import json
import time
import difflib
import requests
import urllib.parse
import urllib3
from typing import Dict, List, Optional, Tuple
from concurrent.futures import Thread<PERSON>oolExecutor, as_completed
from serpapi import GoogleSearch
from app.utils.logging import get_logger
from app.core.exceptions import PatentProcessingError
from config.settings import get_settings

# Suppress SSL warnings
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)


class ScholarSearchService:
    """Service for searching Google Scholar for compound synthesis papers using SerpAPI."""

    def __init__(
        self,
        compound_name: str,
        output_folder: str = "synthesis_papers",
        num_results: int = 10,
        n_threads: int = 3,
        min_citations: int = 0,
    ):
        """Initialize the Google Scholar synthesis search service.

        Args:
            compound_name: Name of the chemical compound to search synthesis papers for
            output_folder: Folder to save downloaded PDFs
            num_results: Number of publications to retrieve
            n_threads: Number of download threads
            min_citations: Minimum citation count filter
            debug_mode: Enable debug logging
        """
        self.compound_name = compound_name
        self.output_folder = output_folder
        self.num_results = num_results
        self.n_threads = n_threads
        self.min_citations = min_citations

        # Create synthesis-focused query like pdfDownloader.py
        self.query = f"{compound_name} AND synthesis"

        # Initialize logging
        self.logger = get_logger(__name__)

        # Initialize settings
        self.settings = get_settings()

        # Get SerpAPI key
        self.SERP_API_KEY = (
            self.settings.SERP_API_KEY or
            os.getenv('SERP_API_KEY')
        )

        if not self.SERP_API_KEY:
            raise PatentProcessingError(
                "SerpAPI key is required. Set SERP_API_KEY environment variable "
                "or configure in settings."
            )

        # File tracking
        self.no_download_link_json = 'no_download_link_titles.json'
        self.no_download_link_titles = self._load_failed_downloads()

        # Sci-Hub configuration using the exact curl pattern you provided
        self.sci_hub_url = 'https://www.sci-hub.se/'
        self._setup_sci_hub_session()

        # Publication data
        self.publications = []

    def _setup_sci_hub_session(self):
        """Setup Sci-Hub session with exact headers from working curl request."""
        self.session = requests.Session()

        # Exact headers from your working curl request
        self.headers = {
            'accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
            'accept-language': 'en-GB,en-US;q=0.9,en;q=0.8',
            'cache-control': 'max-age=0',
            'content-type': 'application/x-www-form-urlencoded',
            'dnt': '1',
            'origin': 'https://www.sci-hub.se',
            'priority': 'u=0, i',
            'referer': 'https://www.sci-hub.se/',
            'sec-ch-ua': '"Chromium";v="135", "Not-A.Brand";v="8"',
            'sec-ch-ua-mobile': '?0',
            'sec-ch-ua-platform': '"macOS"',
            'sec-fetch-dest': 'document',
            'sec-fetch-mode': 'navigate',
            'sec-fetch-site': 'same-origin',
            'upgrade-insecure-requests': '1',
            'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/135.0.0.0 Safari/537.36'
        }

        # Cookies from your working curl request
        self.cookies = {
            '__ddg1_': '5wutYaLl6GVjklrLHNho',
            'session': 'e4a5177f4946c850a4104e7c1b0ae01a',
            '__ddgid_': 'VL0yhGuXmhf0ygLw',
            '__ddg2_': 'UDPOo8YwWzCIwCFd',
            'ext_name': 'ojplmecpdpgccookcobabopnaifgidhf',
            'refresh': '1749460757.0598',
            '__ddgmark_': 'gyeNDh2Wh54AAH68',
            '__ddg9_': '14.97.5.58',
            '__ddg5_': 'JjSq4p0DIVUEorQb',
            '__ddg8_': '0DZ8LBfI3o0ZaUAJ',
            '__ddg10_': '1749722242'
        }

        self.session.headers.update(self.headers)
        self.session.cookies.update(self.cookies)

    def download_pdf_from_scholar_resources(self, title: str, pdf_resources: List[Dict]) -> bool:
        """Try to download PDF directly from Google Scholar resources."""
        if not pdf_resources:
            return False

        filename = f"{self._sanitize_filename(title)}.pdf"
        filepath = os.path.join(self.output_folder, filename)
        os.makedirs(self.output_folder, exist_ok=True)

        for i, resource in enumerate(pdf_resources):
            resource_link = resource.get('link', '')
            resource_title = resource.get('title', f'Resource {i+1}')

            if not resource_link:
                continue

            try:
                print(f"📥 Trying direct download from Scholar resource: {resource_title}")
                print(f"🔗 URL: {resource_link}")

                # Use a simple requests session for direct downloads
                response = requests.get(
                    resource_link,
                    headers={
                        'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/135.0.0.0 Safari/537.36'
                    },
                    timeout=60,
                    allow_redirects=True
                )

                if response.status_code == 200:
                    content_type = response.headers.get('Content-Type', '').lower()

                    # Check if it's a PDF
                    if ('pdf' in content_type or
                        'application/pdf' in content_type or
                        response.content.startswith(b'%PDF')):

                        with open(filepath, 'wb') as f:
                            f.write(response.content)

                        if os.path.exists(filepath) and os.path.getsize(filepath) > 0:
                            file_size = os.path.getsize(filepath)
                            print(f"✅ Successfully downloaded from Scholar resource: {filename} ({file_size} bytes)")
                            return True
                        else:
                            print(f"❌ Downloaded file is empty from resource: {resource_title}")
                    else:
                        print(f"❌ Resource is not a PDF (Content-Type: {content_type}): {resource_title}")
                else:
                    print(f"❌ Failed to download from resource {resource_title}: HTTP {response.status_code}")

            except Exception as e:
                print(f"❌ Error downloading from resource {resource_title}: {str(e)}")
                continue

        return False



    def _load_failed_downloads(self) -> List[str]:
        """Load list of titles that failed to download."""
        try:
            if os.path.exists(self.no_download_link_json):
                with open(self.no_download_link_json, 'r', encoding='utf-8') as f:
                    return json.load(f)
        except Exception as e:
            self.logger.warning(f"Error loading failed downloads list: {str(e)}")
        return []

    def _save_failed_downloads(self):
        """Save list of titles that failed to download."""
        try:
            with open(self.no_download_link_json, 'w', encoding='utf-8') as f:
                json.dump(self.no_download_link_titles, f, indent=4, ensure_ascii=False)
        except Exception as e:
            self.logger.error(f"Error saving failed downloads list: {str(e)}")

    def search_synthesis_publications(self, max_retries: int = 3) -> List[Dict]:
        """Search for synthesis publications using SerpAPI Google Scholar.

        Args:
            max_retries: Maximum number of retry attempts

        Returns:
            List of publication dictionaries
        """
        self.logger.info(f"Searching Google Scholar for synthesis of: {self.compound_name}")
        self.logger.info(f"Using query: {self.query}")

        publications = []

        try:
            # Calculate how many pages we need (SerpAPI returns max 20 per page)
            results_per_page = min(20, self.num_results)
            total_pages = (self.num_results + results_per_page - 1) // results_per_page

            for page in range(total_pages):
                start_index = page * results_per_page
                current_page_size = min(results_per_page, self.num_results - len(publications))

                if current_page_size <= 0:
                    break

                self.logger.debug(f"Fetching page {page + 1}/{total_pages} (start={start_index})")

                # SerpAPI parameters for Google Scholar synthesis search
                params = {
                    "engine": "google_scholar",
                    "q": self.query,  # Use the synthesis query
                    "start": start_index,
                    "num": current_page_size,
                    "api_key": self.SERP_API_KEY
                }

                # Add year filters if needed (optional enhancement)
                # params["as_ylo"] = "2010"  # From year
                # params["as_yhi"] = "2024"  # To year

                page_publications = self._fetch_scholar_page(params, max_retries)

                if not page_publications:
                    self.logger.warning(f"No results from page {page + 1}, stopping search")
                    break

                publications.extend(page_publications)

                # Stop if we have enough results
                if len(publications) >= self.num_results:
                    publications = publications[:self.num_results]
                    break

                # Small delay between requests to be respectful
                time.sleep(1)

        except Exception as e:
            self.logger.error(f"Error in Google Scholar search: {str(e)}")

        # Filter by citation count
        filtered_publications = [
            pub for pub in publications
            if pub.get('num_citations', 0) >= self.min_citations
        ]

        self.publications = filtered_publications
        self.logger.info(
            f"Found {len(publications)} publications for synthesis of {self.compound_name}, "
            f"{len(filtered_publications)} meet citation criteria (>= {self.min_citations})"
        )
        return filtered_publications

    def get_fallback_synthesis_titles(self, num_results: int = 10) -> List[Dict]:
        """Generate fallback synthesis titles when Google Scholar search fails.

        Similar to pdfDownloader.py's get_fallback_titles method.

        Args:
            num_results: Number of fallback titles to generate

        Returns:
            List of synthetic publication dictionaries
        """
        self.logger.info(f"Generating fallback synthesis titles for: {self.compound_name}")

        # Generic synthesis-related titles (similar to pdfDownloader.py)
        generic_titles = [
            f"Synthesis and Characterization of {self.compound_name}",
            f"Novel Synthetic Routes to {self.compound_name}",
            f"Efficient Synthesis of {self.compound_name} and Its Derivatives",
            f"A Review of Synthetic Approaches to {self.compound_name}",
            f"Green Synthesis of {self.compound_name}",
            f"One-Pot Synthesis of {self.compound_name}",
            f"Catalytic Synthesis of {self.compound_name}",
            f"Improved Method for the Preparation of {self.compound_name}",
            f"Recent Advances in the Synthesis of {self.compound_name}",
            f"Sustainable Synthesis of {self.compound_name}",
            f"Total Synthesis of {self.compound_name}",
            f"Stereoselective Synthesis of {self.compound_name}",
            f"Asymmetric Synthesis of {self.compound_name}",
            f"Microwave-Assisted Synthesis of {self.compound_name}",
            f"Solvent-Free Synthesis of {self.compound_name}"
        ]

        # Convert to publication format
        fallback_publications = []
        for i, title in enumerate(generic_titles[:num_results]):
            pub_data = {
                'title': title,
                'num_citations': 0,  # Fallback titles have no citations
                'year': '',
                'venue': 'Fallback Title',
                'authors': 'Generated',
                'abstract': f"Synthetic title for {self.compound_name} synthesis research",
                'url': '',
                'scholar_url': '',
                'result_id': f"fallback_{i}",
                'position': i,
                'is_fallback': True
            }
            fallback_publications.append(pub_data)

        return fallback_publications

    def _fetch_scholar_page(self, params: Dict, max_retries: int = 3) -> List[Dict]:
        """Fetch a single page of results from SerpAPI Google Scholar."""
        last_error = None

        for attempt in range(max_retries + 1):
            try:
                self.logger.debug(f"SerpAPI request (attempt {attempt + 1}): {params}")

                search = GoogleSearch(params)
                results = search.get_dict()

                # Check for errors
                if "error" in results:
                    error_msg = results["error"]
                    self.logger.warning(f"SerpAPI error: {error_msg}")
                    last_error = error_msg

                    if attempt < max_retries:
                        self.logger.info(f"Retrying in 2 seconds...")
                        time.sleep(2)
                        continue
                    else:
                        return []

                # Extract publications from organic results
                organic_results = results.get("organic_results", [])
                publications = []

                for result in organic_results:
                    try:
                        pub_data = self._extract_publication_data(result)
                        if pub_data:
                            publications.append(pub_data)
                    except Exception as e:
                        self.logger.warning(f"Error extracting publication data: {str(e)}")
                        continue

                self.logger.debug(f"Extracted {len(publications)} publications from page")
                return publications

            except Exception as e:
                error_msg = f"Error fetching Scholar page: {str(e)}"
                self.logger.error(error_msg)
                last_error = error_msg

                if attempt < max_retries:
                    self.logger.info(f"Retrying in 2 seconds...")
                    time.sleep(2)
                else:
                    self.logger.error(f"Failed to fetch page after {max_retries + 1} attempts")
                    return []

        return []

    def _extract_publication_data(self, result: Dict) -> Optional[Dict]:
        """Extract publication data from a SerpAPI organic result."""
        try:
            title = result.get("title", "")
            if not title:
                return None

            # Extract citation count
            num_citations = 0
            inline_links = result.get("inline_links", {})
            cited_by = inline_links.get("cited_by", {})
            if cited_by and "total" in cited_by:
                num_citations = int(cited_by["total"])

            # Extract publication info
            publication_info = result.get("publication_info", {})
            summary = publication_info.get("summary", "")

            # Parse authors and year from summary (format: "Author1, Author2 - Year - source")
            authors = ""
            year = ""
            venue = ""

            if summary:
                # Try to parse the summary format
                parts = summary.split(" - ")
                if len(parts) >= 2:
                    authors = parts[0].strip()
                    if len(parts) >= 3:
                        year = parts[1].strip()
                        venue = parts[2].strip()
                    else:
                        # Sometimes year and venue are combined
                        year_venue = parts[1].strip()
                        # Try to extract year (4 digits)
                        year_match = re.search(r'\b(19|20)\d{2}\b', year_venue)
                        if year_match:
                            year = year_match.group()
                            venue = year_venue.replace(year, "").strip(" -,")
                        else:
                            venue = year_venue

            # Get snippet as abstract
            snippet = result.get("snippet", "")

            # Get link
            link = result.get("link", "")

            # Try to extract DOI from the link or other fields
            doi = None
            if link:
                # Look for DOI in the URL
                doi_match = re.search(r'doi\.org/([^&\s]+)', link)
                if doi_match:
                    doi = doi_match.group(1)
                else:
                    # Look for DOI pattern in the URL
                    doi_match = re.search(r'10\.\d{4,}/[^\s&]+', link)
                    if doi_match:
                        doi = doi_match.group(0)

            # Extract resources (direct PDF links from Google Scholar)
            resources = result.get("resources", [])
            pdf_resources = []
            for resource in resources:
                if resource.get("file_format", "").upper() == "PDF":
                    pdf_resources.append({
                        'title': resource.get('title', ''),
                        'link': resource.get('link', ''),
                        'file_format': resource.get('file_format', '')
                    })

            pub_data = {
                'title': title,
                'num_citations': num_citations,
                'year': year,
                'venue': venue,
                'authors': authors,
                'abstract': snippet,
                'url': link,
                'scholar_url': link,
                'doi': doi,
                'pdf_resources': pdf_resources,  # Direct PDF links from Scholar
                'result_id': result.get("result_id", ""),
                'position': result.get("position", 0)
            }

            return pub_data

        except Exception as e:
            self.logger.warning(f"Error extracting publication data: {str(e)}")
            return None

    def get_existing_pdfs(self) -> List[str]:
        """Get list of existing PDF files in the output folder."""
        pdf_files = []
        try:
            if os.path.exists(self.output_folder):
                for root, dirs, files in os.walk(self.output_folder):
                    for file in files:
                        if file.endswith(".pdf"):
                            pdf_files.append(file)
        except Exception as e:
            self.logger.error(f"Error getting existing PDFs: {str(e)}")
        return pdf_files

    def _sanitize_filename(self, title: str) -> str:
        """Sanitize title for use as filename."""
        sanitized = re.sub(r'[<>:"/\\|?*]', '', title)
        sanitized = sanitized.replace('\n', ' ').replace('\r', ' ')
        if len(sanitized) > 200:
            sanitized = sanitized[:200]
        return sanitized.strip()

    def download_pdf_with_scihub(self, title: str, doi: str = None) -> bool:
        """Download PDF using exact Sci-Hub pattern from your working curl request."""
        try:
            # Create a sanitized filename
            filename = f"{self._sanitize_filename(title)}.pdf"
            filepath = os.path.join(self.output_folder, filename)

            # Ensure output directory exists
            os.makedirs(self.output_folder, exist_ok=True)

            # Try DOI first if available, then title
            search_queries = []
            if doi:
                search_queries.append(doi)
            search_queries.append(title)

            print(f"\n📝 DEBUG: FULL TITLE RECEIVED:")
            print(f"Title: '{title}'")
            print(f"Title length: {len(title)} characters")
            if doi:
                print(f"DOI: '{doi}'")

            for query in search_queries:
                print(f"\n🔍 DEBUG: About to search with query:")
                print(f"Query: '{query}'")
                print(f"Query length: {len(query)} characters")

                self.logger.info(f"Searching Sci-Hub for: {query[:60]}...")

                # Get the PDF download link using your exact curl pattern
                pdf_url = self._get_scihub_pdf_url(query)
                if pdf_url:
                    # Download the PDF
                    if self._download_pdf_from_url(pdf_url, filepath):
                        file_size = os.path.getsize(filepath)
                        self.logger.info(f"Successfully downloaded: {filename} ({file_size} bytes)")
                        return True
                else:
                    self.logger.debug(f"No PDF URL found for query: {query[:50]}...")

            # If we get here, all queries failed
            self.logger.warning(f"Failed to download: {title[:50]}...")
            self.no_download_link_titles.append(title)
            self._save_failed_downloads()
            return False

        except Exception as e:
            self.logger.error(f"Error downloading '{title[:50]}...': {str(e)}")
            return False

    def _get_scihub_pdf_url(self, query: str) -> Optional[str]:
        """Get PDF URL from Sci-Hub using exact curl pattern."""
        try:
            # Prepare the data exactly like your curl request
            data = {
                'request': query,
                'screenWidth': '1710',
                'screenHeight': '1107'
            }

            # Print the EXACT query being sent (not truncated)
            print(f"\n🔍 DEBUG: EXACT QUERY BEING SENT TO SCI-HUB:")
            print(f"Query: '{query}'")
            print(f"Query length: {len(query)} characters")
            print(f"Data being posted: {data}")

            self.logger.debug(f"Posting to Sci-Hub with query: {query[:60]}...")

            # Make the POST request exactly like your curl
            response = self.session.post(
                self.sci_hub_url,
                data=data,
                timeout=30,
                verify=False
            )

            self.logger.debug(f"Sci-Hub response status: {response.status_code}")

            # Always print response status and content for debugging
            print(f"\n🔍 DEBUG: Sci-Hub response status: {response.status_code}")
            print(f"🔍 DEBUG: Response URL: {response.url}")
            print(f"🔍 DEBUG: Query was: {query[:60]}...")

            if response.status_code == 200:

                # Look for the embed tag with PDF URL - try multiple patterns
                embed_patterns = [
                    r'<embed[^>]+src="([^"]+)"[^>]*id\s*=\s*["\']pdf["\']',
                    r'<embed[^>]+src="([^"]+)"[^>]*type="application/pdf"',
                    r'<embed[^>]*type="application/pdf"[^>]+src="([^"]+)"',
                    r'src="([^"]*\.pdf[^"]*)"'
                ]

                pdf_path = None
                for pattern in embed_patterns:
                    embed_match = re.search(pattern, response.text, re.IGNORECASE)
                    if embed_match:
                        pdf_path = embed_match.group(1)
                        self.logger.debug(f"Found PDF path with pattern: {pattern}")
                        break

                if pdf_path:
                    # Convert relative path to full URL
                    if pdf_path.startswith('//'):
                        # Protocol-relative URL like //2024.sci-hub.se/1854/...
                        pdf_url = f"https:{pdf_path}"
                    elif pdf_path.startswith('/'):
                        # Absolute path like /downloads/2019-03-18/09/...
                        pdf_url = f"https://sci-hub.se{pdf_path}"
                    elif pdf_path.startswith('http'):
                        # Already a full URL
                        pdf_url = pdf_path
                    else:
                        # Relative path
                        pdf_url = f"https://sci-hub.se/{pdf_path}"

                    # Remove any URL fragments (like #navpanes=0&view=FitH)
                    pdf_url = pdf_url.split('#')[0]

                    self.logger.info(f"Found PDF URL: {pdf_url}")
                    return pdf_url
                else:
                    self.logger.warning(f"No PDF embed tag found in response for: {query[:50]}...")
                    # Check if there's an error message
                    if 'article not found' in response.text.lower() or 'not found' in response.text.lower():
                        self.logger.debug("Article not found on Sci-Hub")
                    return None
            else:
                print(f"\n❌ DEBUG: Sci-Hub returned status {response.status_code} for: {query[:50]}...")
                print(f"❌ DEBUG: Response content: {response.text[:500]}...")
                self.logger.warning(f"Sci-Hub returned status {response.status_code} for: {query[:50]}...")
                return None

        except Exception as e:
            self.logger.error(f"Error getting PDF URL for '{query[:50]}...': {str(e)}")
            return None

    def _download_pdf_from_url(self, pdf_url: str, filepath: str) -> bool:
        """Download PDF from the extracted URL."""
        try:
            self.logger.info(f"Downloading PDF from: {pdf_url}")

            # Try the download with proper headers
            response = self.session.get(
                pdf_url,
                timeout=60,
                stream=True,
                allow_redirects=True
            )

            self.logger.debug(f"Download response status: {response.status_code}")
            self.logger.debug(f"Content-Type: {response.headers.get('Content-Type', 'Unknown')}")

            if response.status_code == 200:
                content_type = response.headers.get('Content-Type', '').lower()

                # Check if it looks like a PDF
                if 'pdf' in content_type or 'application/pdf' in content_type:
                    with open(filepath, 'wb') as f:
                        for chunk in response.iter_content(chunk_size=8192):
                            if chunk:
                                f.write(chunk)

                    # Verify it's a valid PDF
                    if os.path.exists(filepath) and os.path.getsize(filepath) > 0:
                        with open(filepath, 'rb') as f:
                            header = f.read(4)
                            if header.startswith(b'%PDF'):
                                return True
                            else:
                                self.logger.warning(f"Downloaded file is not a valid PDF (header: {header})")
                                # Log first 100 bytes for debugging
                                f.seek(0)
                                content_preview = f.read(100)
                                self.logger.debug(f"File content preview: {content_preview}")
                                os.remove(filepath)
                                return False
                    else:
                        self.logger.warning(f"Downloaded file is empty")
                        return False
                else:
                    self.logger.warning(f"Content-Type is not PDF: {content_type}")
                    # Still try to download and check content
                    with open(filepath, 'wb') as f:
                        content = response.content
                        if content.startswith(b'%PDF'):
                            f.write(content)
                            return True
                        else:
                            self.logger.debug(f"Content preview: {content[:100]}")
                            return False
            elif response.status_code == 404:
                self.logger.warning(f"PDF not found (404): {pdf_url}")
                return False
            elif response.status_code == 403:
                self.logger.warning(f"Access forbidden (403): {pdf_url}")
                return False
            else:
                self.logger.warning(f"Failed to download PDF: HTTP {response.status_code}")
                return False

        except Exception as e:
            self.logger.error(f"Error downloading PDF from URL: {str(e)}")
            return False

    def download_publication(self, publication: Dict) -> bool:
        """Download a single publication - try Scholar resources first, then Sci-Hub."""
        title = publication.get('title', '')
        if not title:
            self.logger.error("Publication has no title")
            return False

        doi = publication.get('doi')
        pdf_resources = publication.get('pdf_resources', [])

        self.logger.info(f"Attempting to download: {title[:80]}...")
        if doi:
            self.logger.debug(f"DOI available: {doi}")
        if pdf_resources:
            print(f"📚 Found {len(pdf_resources)} direct PDF resources from Google Scholar")

        # Strategy 1: Try direct download from Google Scholar resources first
        if pdf_resources:
            print(f"🎯 Trying direct download from Google Scholar resources...")
            success = self.download_pdf_from_scholar_resources(title, pdf_resources)
            if success:
                print(f"✅ Successfully downloaded from Scholar resources: {title[:80]}...")
                return True
            else:
                print(f"❌ Failed to download from Scholar resources, falling back to Sci-Hub...")

        # Strategy 2: Fall back to Sci-Hub if Scholar resources failed or unavailable
        print(f"🔬 Trying Sci-Hub download...")
        success = self.download_pdf_with_scihub(title, doi)

        if success:
            self.logger.info(f"Successfully downloaded from Sci-Hub: {title[:80]}...")
        else:
            self.logger.warning(f"Failed to download PDF for: {title[:80]}...")

        return success

    def filter_publications(self, publications: List[Dict]) -> List[Dict]:
        """Filter publications to exclude already downloaded ones."""
        # In debug mode, don't skip anything - retry everything
        # if self.debug_mode:
        #     print(f"🔧 DEBUG MODE: Bypassing all filters, will attempt all {len(publications)} publications")
        #     return [pub for pub in publications if pub.get('title', '')]

        existing_pdfs = self.get_existing_pdfs()
        filtered = []

        for pub in publications:
            title = pub.get('title', '')
            if not title:
                continue

            # Check if already downloaded
            if self.check_pdf_exists(title, existing_pdfs):
                self.logger.debug(f"Skipping already downloaded: {title}")
                continue

            # Check if previously failed
            if title in self.no_download_link_titles:
                self.logger.debug(f"Skipping previously failed: {title}")
                continue

            filtered.append(pub)

        return filtered

    def check_pdf_exists(self, target_title: str, existing_pdfs: List[str]) -> bool:
        """Check if a PDF with similar title already exists."""
        similarity_threshold = 0.9
        target_filename = self._sanitize_filename(target_title)

        for pdf_name in existing_pdfs:
            pdf_title = pdf_name.replace('.pdf', '')
            similarity = difflib.SequenceMatcher(None, target_filename, pdf_title).ratio()
            if similarity > similarity_threshold:
                return True
        return False

    def download_publications_parallel(self, publications: List[Dict]) -> Dict:
        """Download publications in parallel."""
        filtered_pubs = self.filter_publications(publications)

        if not filtered_pubs:
            self.logger.info("No new publications to download")
            return {"downloaded": 0, "failed": 0, "skipped": len(publications)}

        self.logger.info(f"Starting download of {len(filtered_pubs)} publications using {self.n_threads} threads")
        print(f"📥 Attempting to download {len(filtered_pubs)} publications...")

        downloaded = 0
        failed = 0
        completed = 0

        with ThreadPoolExecutor(max_workers=self.n_threads) as executor:
            futures = [executor.submit(self.download_publication, pub) for pub in filtered_pubs]

            for future in as_completed(futures):
                try:
                    success = future.result()
                    completed += 1
                    if success:
                        downloaded += 1
                        print(f"✅ Downloaded {downloaded}/{len(filtered_pubs)} papers")
                    else:
                        failed += 1
                        print(f"❌ Failed {failed}/{len(filtered_pubs)} papers (progress: {completed}/{len(filtered_pubs)})")
                except Exception as e:
                    failed += 1
                    completed += 1
                    self.logger.error(f"Download task failed: {str(e)}")
                    print(f"❌ Error {failed}/{len(filtered_pubs)} papers (progress: {completed}/{len(filtered_pubs)})")

        print(f"\n📊 Download Summary: ✅ {downloaded} successful, ❌ {failed} failed, ⏭️ {len(publications) - len(filtered_pubs)} skipped")

        # Provide helpful suggestions if all downloads failed
        if downloaded == 0 and failed > 0:
            print("\n💡 Troubleshooting suggestions:")
            print("   🔍 Papers might not be available on Sci-Hub")
            print("   📚 Try accessing through your institution's library")
            print("   🌐 Check the original URLs in the publication list above")
            print("   🔧 Try using a VPN if downloads are blocked")
            print("   ⏰ Wait and try again later if rate limited")

        return {
            "downloaded": downloaded,
            "failed": failed,
            "skipped": len(publications) - len(filtered_pubs)
        }

    def run_complete_search_and_download(self) -> Dict:
        """Run the complete synthesis search and download process."""
        try:
            self.logger.info(f"Starting Google Scholar synthesis search and download for: {self.compound_name}")

            # Create output directory
            os.makedirs(self.output_folder, exist_ok=True)

            # Search for synthesis publications
            publications = self.search_synthesis_publications()

            # Use fallback titles if no publications found (like pdfDownloader.py)
            if not publications:
                self.logger.warning("No publications found from Google Scholar. Using fallback titles.")
                publications = self.get_fallback_synthesis_titles(self.num_results)
                self.logger.info(f"Generated {len(publications)} fallback synthesis titles")
            else:
                self.logger.info(f"Found {len(publications)} publications from Google Scholar")

            if not publications:
                self.logger.warning("No publications available (including fallback)")
                return {
                    "status": "completed",
                    "compound": self.compound_name,
                    "query": self.query,
                    "publications_found": 0,
                    "downloaded": 0,
                    "failed": 0,
                    "skipped": 0
                }

            # Display found publications
            self._display_publications(publications)

            # Download publications
            download_results = self.download_publications_parallel(publications)

            # Summary
            result = {
                "status": "completed",
                "compound": self.compound_name,
                "query": self.query,
                "publications_found": len(publications),
                "downloaded": download_results["downloaded"],
                "failed": download_results["failed"],
                "skipped": download_results["skipped"],
                "output_folder": os.path.abspath(self.output_folder),
                "used_fallback": any(pub.get('is_fallback', False) for pub in publications)
            }

            self.logger.info(
                f"Synthesis search and download completed for {self.compound_name}: "
                f"{result['publications_found']} found, "
                f"{result['downloaded']} downloaded, "
                f"{result['failed']} failed, "
                f"{result['skipped']} skipped"
            )

            return result

        except Exception as e:
            error_msg = f"Error in synthesis search and download process: {str(e)}"
            self.logger.error(error_msg)
            return {
                "status": "error",
                "error": error_msg,
                "compound": self.compound_name,
                "query": self.query
            }

    def _display_publications(self, publications: List[Dict]):
        """Display found publications for user review."""
        if not publications:
            print("📄 No publications found")
            return

        print(f"\n📄 Found {len(publications)} publications:")
        print("-" * 80)

        for i, pub in enumerate(publications, 1):
            title = pub.get('title', 'No title')
            citations = pub.get('num_citations', 0)
            year = pub.get('year', 'Unknown')
            authors = pub.get('authors', 'Unknown')
            doi = pub.get('doi', 'N/A')
            pdf_resources = pub.get('pdf_resources', [])

            print(f"{i}. {title}")
            print(f"   Authors: <AUTHORS>
            print(f"   Year: {year} | Citations: {citations}")
            print(f"   DOI: {doi}")
            print(f"   URL: {pub.get('url', 'N/A')}")

            if pdf_resources:
                print(f"   📚 Direct PDF Resources: {len(pdf_resources)} available")
                for j, resource in enumerate(pdf_resources):
                    print(f"      {j+1}. {resource.get('title', 'Unknown')} - {resource.get('link', 'No link')}")
            else:
                print(f"   📚 Direct PDF Resources: None (will try Sci-Hub)")
            print()

        print("-" * 80)


