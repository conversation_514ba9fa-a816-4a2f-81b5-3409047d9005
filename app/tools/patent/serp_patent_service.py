"""SerpAPI patent service for fetching patent details."""

import os
import time
import json
import requests
from typing import Dict, Optional
from serpapi import GoogleSearch
from bs4 import BeautifulSoup
from app.utils.logging import get_logger
from app.core.exceptions import PatentProcessingError
from app.config.settings import get_settings
from app.utils.es_helper import *
from app.utils.azure_utils import AzureUtils
from bs4 import BeautifulSoup



class SerpPatentService(AzureUtils):
    """Service for fetching patent details using SerpAPI."""

    def __init__(self):
        """Initialize the SerpAPI patent service.
        """
        self.logger = get_logger(__name__)
        self.settings = get_settings()
        super().__init__(self.settings)

        # Get SerpAPI key
        self.SERP_API_KEY = (
            self.settings.SERP_API_KEY or
            os.getenv('SERP_API_KEY')
        )

        if not self.SERP_API_KEY:
            raise PatentProcessingError(
                "SerpAPI key is required. Set SERP_API_KEY environment variable "
                "or configure in settings."
            )
        
    
    def get_patent_data(self, patent_id: str) -> Dict:
        """Get patent data from the Azure blob with retry logic.
        
        Args:
            patent_id: Patent ID
            
        Returns:
            Dictionary with patent data
        """
        retries = 3
        blob_name = f"patents/{patent_id}/serp/serp-en.json"
        for attempt in range(1, retries + 1):
            try:
                data = json.loads(self.get_data_from_azure_blob(blob_name))
                logger.info(f"Got patent data for {patent_id}")
                return data
            except Exception as e:
                logger.error(f"Attempt {attempt} - Error getting patent data: {str(e)}")
                if attempt < retries:
                    time.sleep(2)
                else:
                    return {}

    def set_patent_data(self, patent_id: str, raw_json: Dict) -> None:
        """Set patent data in the Azure blob with retry logic.
        
        Args:
            patent_id: Patent ID
            raw_json: Raw JSON data to set
        """
        retries = 3
        blob_name = f"patents/{patent_id}/serp/serp-en.json"

        for attempt in range(1, retries + 1):
            try:
                if 'error' in raw_json:
                    if raw_json['error'] == 'Your account has run out of searches.':
                        logger.info(f"Patent not found for {patent_id}")
                        return
                
                self.upload_to_azure_blob(blob_name=blob_name, data=raw_json, content_type='application/json')
                logger.info(f"Set patent data for {patent_id}")
                return
            except Exception as e:
                logger.error(f"Attempt {attempt} - Error setting patent data: {str(e)}")
                if attempt < retries:
                    time.sleep(2)
    
    def extract_standard_claims_html(self, soup):
        '''
        Extract standard claims from the patent page for English patents
        '''
        claims_dict = {}

        claim_divs = soup.find_all('div', class_='claim')
        for div in claim_divs:
            claim_id = div.get('id', '')
            if claim_id:
                claims_dict[claim_id] = str(div)

        if claims_dict:
            return claims_dict

        candidate_ids = ['claims', 'claims-text', 'english-claims']
        for cid in candidate_ids:
            container = soup.find(id=cid)
            if container:
                divs = container.find_all(['div', 'p'], recursive=True)
                for div in divs:
                    raw_id = div.get('id') or div.get('num')
                    if raw_id:
                        claim_id = f'{cid}-{raw_id.strip()}'
                    else:
                        # fallback to text-based hash to avoid duplicate entries
                        claim_id = f'{cid}-hash-{hash(div.text.strip())}'
                    
                    if claim_id not in claims_dict:
                        claims_dict[claim_id] = str(div)
                break

        return claims_dict

    def extract_translated_claims_html(self, soup):
        '''
        Extract translated claims from the patent page for non-English patents
        '''
        claims_dict = {}
        claim_tags = soup.find_all('claim')

        for idx, claim in enumerate(claim_tags):
            claim_id = claim.get('id', f'trans-claim-{idx+1:04d}')
            html_parts = []

            for claim_text in claim.find_all('claim-text'):
                notranslate_span = claim_text.find('span', class_='notranslate')
                if notranslate_span:
                    # Remove source language span
                    src_text = notranslate_span.find('span', class_='google-src-text')
                    if src_text:
                        src_text.decompose()
                    html_parts.append(str(notranslate_span))

            if html_parts:
                claims_dict[claim_id] = ''.join(html_parts)

        return claims_dict
    
    def get_claim_html(self, patent_id, url: str) -> list:
        '''
        Get claim HTML from the patent page.
        First tries the given URL, and if that fails, retries using the fallback Google Patents URL.
        '''
        def try_extract_claims(url_to_try):
            for _ in range(3):
                try:
                    response = requests.get(url_to_try)
                    response.raise_for_status()
                    soup = BeautifulSoup(response.content, 'html.parser')

                    claims_dict = self.extract_standard_claims_html(soup)
                    if claims_dict:
                        return [claim for _, claim in sorted(claims_dict.items())]

                    claims_dict = self.extract_translated_claims_html(soup)
                    if claims_dict:
                        return [claim for _, claim in sorted(claims_dict.items())]
                except Exception as e:
                    logger.error(f"Error fetching claims from {url_to_try}: {str(e)}")
                    time.sleep(1)
            return []

        claims = try_extract_claims(url)
        if claims:
            return claims

        url_2 = f'https://patents.google.com/patent/{patent_id}/en' # Backup url
        return try_extract_claims(url_2)

    
    def get_patent_details(
        self, 
        patent_id: str, 
        max_retries: int = 3, 
        retry_delay: int = 2
    ) -> Optional[Dict]:
        """Get patent details from SerpAPI with retry logic.
        
        Args:
            patent_id: Patent ID (e.g., "US20010031432")
            max_retries: Maximum number of retry attempts
            retry_delay: Delay between retries in seconds
            
        Returns:
            Dictionary with patent details or None if failed
        """
        last_error = None
        
        for attempt in range(max_retries + 1):
            try:
                # Format patent ID for SerpAPI
                formatted_patent_id = f"patent/{patent_id}/en"
                
                results = self.get_patent_data(patent_id)
                params = {
                    "engine": "google_patents_details",
                    "patent_id": formatted_patent_id,
                    "api_key": self.SERP_API_KEY
                }
                
                self.logger.debug(
                    f"Requesting patent details for: {patent_id} "
                    f"(attempt {attempt + 1}/{max_retries + 1})"
                )
                if not results:
                    search = GoogleSearch(params)
                    results = search.get_dict()
                    self.set_patent_data(patent_id, results)
                
                # Check for errors
                if "error" in results:
                    error_msg = results["error"]
                    self.logger.warning(f"SerpAPI error for {patent_id}: {error_msg}")
                    last_error = error_msg
                    
                    if attempt < max_retries:
                        self.logger.info(f"Retrying in {retry_delay} seconds...")
                        time.sleep(retry_delay)
                        continue
                    else:
                        return None
                
                # Extract patent details
                patent_details = self._extract_patent_details(patent_id, results)

                # Fetch description if available
                if 'description_link' in results:
                    description = self._fetch_description_from_link(
                        results['description_link']
                    )
                    if description:
                        patent_details['description'] = description

                # Add the complete raw SerpAPI response for reference
                patent_details['raw_serp_response'] = results

                self.logger.info(f"Successfully fetched details for patent: {patent_id}")
                return patent_details
                
            except Exception as e:
                error_msg = f"Error fetching patent {patent_id}: {str(e)}"
                self.logger.error(error_msg)
                last_error = error_msg
                
                if attempt < max_retries:
                    self.logger.info(f"Retrying in {retry_delay} seconds...")
                    time.sleep(retry_delay)
                else:
                    self.logger.error(f"Failed to fetch patent {patent_id} after {max_retries + 1} attempts")
                    return None
        
        return None
    
    def _extract_patent_details(self, patent_id: str, results: Dict) -> Dict:
        """Extract patent details from SerpAPI results."""
        patent_details = {
            'patent_id': patent_id,
            'abstract': results.get('abstract', ''),
            'claims' : results.get('claims', ''),
            'claims_html': self.get_claim_html(patent_id,
                results.get('search_metadata', {}).get('raw_html_file', '')),
            'description': '',
            'title': results.get('title', ''),
            'inventors': results.get('inventors', []),
            'assignee': results.get('assignee', ''),
            'publication_date': results.get('publication_date', ''),
            'filing_date': results.get('filing_date', ''),
            'patent_url': results.get('search_metadata', {}).get('google_patents_details_url', '')
        }
        
        # Clean up claims if it's a list
        if isinstance(patent_details['claims'], list):
            patent_details['claims'] = '\n'.join(patent_details['claims'])
        
        # Clean up abstract
        if isinstance(patent_details['abstract'], list):
            patent_details['abstract'] = '\n'.join(patent_details['abstract'])
        
        return patent_details
    
    def _fetch_description_from_link(self, description_link: str) -> str:
        """Fetch patent description from the provided link."""
        try:
            self.logger.debug(f"Fetching description from: {description_link}")
            
            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
            }
            
            response = requests.get(description_link, headers=headers, timeout=30)
            response.raise_for_status()
            
            # Parse HTML content
            soup = BeautifulSoup(response.content, 'html.parser')
            
            # Try to find description content
            description_content = ""
            
            # Look for common patent description containers
            description_selectors = [
                'div[data-section="description"]',
                '.description',
                '#description',
                'div.patent-section-description',
                'section[data-section="description"]'
            ]
            
            for selector in description_selectors:
                elements = soup.select(selector)
                if elements:
                    description_content = '\n'.join([elem.get_text(strip=True) for elem in elements])
                    break
            
            # If no specific description found, try to get all text content
            if not description_content:
                # Remove script and style elements
                for script in soup(["script", "style"]):
                    script.decompose()
                
                # Get text content
                text = soup.get_text()
                
                # Clean up text
                lines = (line.strip() for line in text.splitlines())
                chunks = (phrase.strip() for line in lines for phrase in line.split("  "))
                description_content = '\n'.join(chunk for chunk in chunks if chunk)
            
            # No length limit on description - let the patent processor handle overall content limits
            
            self.logger.debug(f"Fetched description ({len(description_content)} characters)")
            return description_content
            
        except requests.exceptions.RequestException as e:
            error_msg = f"Request error fetching description from {description_link}: {str(e)}"
            self.logger.error(error_msg)
            return ""
        except Exception as e:
            error_msg = f"Error parsing description from {description_link}: {str(e)}"
            self.logger.error(error_msg)
            return ""
    
    def combine_patent_content(self, patent_details: Dict) -> str:
        """Combine patent content into a single string for processing."""
        content_parts = []
        
        if patent_details.get('abstract'):
            content_parts.append(f"ABSTRACT:\n{patent_details['abstract']}")
        
        if patent_details.get('description'):
            content_parts.append(f"DESCRIPTION:\n{patent_details['description']}")
        
        if patent_details.get('claims'):
            content_parts.append(f"CLAIMS:\n{patent_details['claims']}")
        
        combined_content = "\n\n".join(content_parts)
        
        self.logger.debug(f"Combined content length: {len(combined_content)} characters")
        return combined_content
