from typing import Dict, Type, Any, List # Added List
from .base import BaseTool
from ..config.settings import get_settings # Assuming settings are needed for tool instantiation

# --- Define the custom exception ---
class ToolNotFoundException(Exception):
    """Custom exception raised when a requested tool is not found in the registry."""
    pass

_tool_classes: Dict[str, Type[BaseTool]] = {}
_tool_instances: Dict[str, BaseTool] = {} # Cache for instantiated tools

def register_tool(cls: Type[BaseTool]):
    """Decorator to register a tool class."""
    if not hasattr(cls, 'name') or not cls.name or not isinstance(cls.name, str):
        raise ValueError(f"Tool class {cls.__name__} must have a non-empty string 'name' attribute for registration.")
    if cls.name in _tool_classes:
        print(f"Warning: Tool class '{cls.name}' is being re-registered. Overwriting previous definition.")
    _tool_classes[cls.name] = cls
    # print(f"Tool registered: {cls.name}") # Optional: for debugging registration
    return cls

def get_tool_instance(name: str, **kwargs) -> BaseTool:
    """
    Retrieves an instance of a registered tool.
    Instantiates the tool if it hasn't been already, passing settings and kwargs.
    """
    settings = get_settings() # Get settings for tool initialization
    
    # Check instance cache first
    if name in _tool_instances:
        # Potentially check if kwargs are compatible if re-instantiation with different kwargs is a concern
        return _tool_instances[name]

    if name in _tool_classes:
        tool_class = _tool_classes[name]
        # Ensure settings are always passed to the tool's __init__
        init_kwargs = {"settings": settings, **kwargs} 
        try:
            instance = tool_class(**init_kwargs)
            _tool_instances[name] = instance # Cache the instance
            return instance
        except TypeError as e:
            # Provide more context on the TypeError
            import inspect
            constructor_params = inspect.signature(tool_class.__init__)
            raise TypeError(
                f"Error initializing tool '{name}' ({tool_class.__name__}). "
                f"Constructor signature: {constructor_params}. "
                f"Provided kwargs (excluding self, including settings): {init_kwargs}. "
                f"Original TypeError: {e}"
            )
        except Exception as e_init: # Catch other potential init errors
             raise RuntimeError(f"Unexpected error initializing tool '{name}': {e_init}") from e_init
    else:
        raise ToolNotFoundException(f"Tool '{name}' not registered. Available tools: {list(_tool_classes.keys())}")

def get_all_tool_names() -> List[str]: # Type hint for return value
    """Returns a list of names of all registered tool classes."""
    return list(_tool_classes.keys())

def load_all_tools():
    """
    Dynamically imports all Python modules in the 'app.tools' package (and sub-packages if any)
    to ensure their @register_tool decorators are executed.
    """
    import pkgutil
    import importlib
    import os # For path manipulation

    package_name_to_scan = 'app.tools'
    
    try:
        package = importlib.import_module(package_name_to_scan)
    except ModuleNotFoundError:
        print(f"ERROR: Could not import tool package '{package_name_to_scan}'. "
              f"Ensure your project's root directory (AGENT-HUB) is in PYTHONPATH or "
              f"you are running from the AGENT-HUB directory.")
        return

    print(f"Dynamically loading tools from package: {package_name_to_scan} (Path: {package.__path__})")
    
    loaded_modules_count = 0
    for finder, module_name_suffix, is_pkg in pkgutil.walk_packages(package.__path__, package.__name__ + '.'):
        # Construct the full module name
        full_module_name = module_name_suffix # walk_packages already gives full name with prefix

        # Get the short name relative to the tools package for exclusion logic
        # e.g., if full_module_name is "app.tools.some_tool", short_name_for_check is "some_tool"
        # if full_module_name is "app.tools.subdir.another_tool", short_name_for_check is "subdir.another_tool"
        if full_module_name.startswith(package_name_to_scan + '.'):
            relative_module_name = full_module_name[len(package_name_to_scan)+1:]
        else: # Should not happen if prefix is correct
            relative_module_name = full_module_name

        # Exclude utility modules like base, registry, __init__
        # Also exclude any __pycache__ or dotfiles if walk_packages somehow picks them up (it shouldn't)
        if relative_module_name in ['base', 'registry', '__init__'] or \
           relative_module_name.startswith('.') or \
           '__pycache__' in relative_module_name:
            # print(f"  Skipping utility/special module: {full_module_name}")
            continue
            
        try:
            # print(f"  Attempting to import: {full_module_name}")
            importlib.import_module(full_module_name)
            loaded_modules_count +=1
            # print(f"    Successfully imported and registered tools from: {full_module_name}")
        except Exception as e:
            print(f"  WARNING: Error importing tool module '{full_module_name}': {e}")
            # import traceback
            # traceback.print_exc() # For more detailed debugging if needed

    print(f"  Dynamic tool loading complete. Triggered import for {loaded_modules_count} potential tool modules.")
    # print(f"  Tools registered after load_all_tools: {get_all_tool_names()}") # For debugging