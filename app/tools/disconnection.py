import os
from typing import Dict, Any, List, Optional
# Conditional import for OpenAI
try:
    import openai
except ImportError:
    openai = None

from .base import BaseTool
from .registry import register_tool
from ..config.settings import Settings

@register_tool
class DisconnectionSuggesterTool(BaseTool):
    name = "DisconnectionSuggester"
    description = ("Suggests retrosynthetic disconnections for a molecule based on its "
                   "SMILES string and identified functional groups, using an LLM.")

    _openai_client: Optional[Any] = None

    def __init__(self, settings: Settings, **kwargs):
        super().__init__(settings=settings)
        self._openai_client = None # Default to None
        if (settings.OPENAI_API_KEY and 
            "your_openai_api_key_here" not in settings.OPENAI_API_KEY and
            "YOUR_OPENAI_KEY_MISSING_IN_ENV" not in settings.OPENAI_API_KEY):
            if openai: # Check if module was imported
                try:
                    self._openai_client = openai.AzureOpenAI()
                    # print(f"[{self.name}] OpenAI client initialized.")
                except Exception as e_init_openai:
                    print(f"[{self.name}] Warning: Could not initialize OpenAI client: {e_init_openai}. Disconnections might not use LLM.")
            else:
                print(f"[{self.name}] Warning: OpenAI library not imported. LLM-based disconnections will be unavailable.")
        else:
            print(f"[{self.name}] Warning: OPENAI_API_KEY not set or contains placeholder value. LLM-based disconnections will be unavailable.")

    def _get_disconnections_from_llm(self, smiles: str, functional_groups: List[str]) -> str:
        if not self._openai_client:
            return "LLM client not available for suggesting disconnections."
        if not smiles: # Added check for SMILES
            return "Cannot suggest disconnections without a SMILES string."
        
        # If FGs are empty, LLM might still try with SMILES, or we can adjust prompt.
        fg_string = ", ".join(functional_groups) if functional_groups else "None specifically provided (rely on SMILES structure)"
        
        prompt_messages = [
            {
                "role": "system",
                "content": (
                    "You are an expert organic chemist specializing in retrosynthesis. "
                    "Your task is to suggest logical retrosynthetic disconnections for a given molecule based on its identified functional groups (if provided) and its SMILES structure. "
                    "For each major functional group present or discernible from SMILES, suggest one or two common and strategic C-X or C-C disconnections. "
                    "Clearly state the bond being disconnected (e.g., C-O in an alcohol, C-N in an amide, C=O C-alpha bond for ketones). "
                    "Show the synthon or fragments that would result. "
                    "Then, briefly suggest a common type of forward reaction that could form that bond. "
                    "Focus on 1-2 key disconnections per relevant functional group. Be concise and clear. "
                    "Structure your output clearly, perhaps using bullet points."
                )
            },
            {
                "role": "user",
                "content": (
                    f"For the molecule with SMILES: {smiles}\n"
                    f"Identified functional groups are: {fg_string}\n\n"
                    "Please suggest key retrosynthetic disconnections."
                )
            }
        ]

        try:
            response = self._openai_client.chat.completions.create(
                model="gpt-4o", # Or settings.LLM_MODEL_DISCONNECTION
                messages=prompt_messages,
                temperature=0.5, 
                max_tokens=1000,
                n=1,
                stop=None,
            )
            disconnection_suggestions = response.choices[0].message.content.strip()
            return disconnection_suggestions
        except Exception as api_e:
            if openai and hasattr(openai, 'APIError') and isinstance(api_e, openai.APIError):
                print(f"[{self.name}] OpenAI API error: {api_e}")
                return f"OpenAI API error during disconnection suggestion: {str(api_e)[:100]}..."
            print(f"[{self.name}] LLM error: {api_e}")
            return f"Error suggesting disconnections via LLM: {str(api_e)[:100]}..."


    def _run(self, smiles: str, functional_groups: List[str]) -> Dict[str, Any]:
        print(f"[{self.name} Tool] _run for SMILES: {smiles} with FGs: {functional_groups}")
        if not smiles:
            return {"error": "Input SMILES string is required."}
        
        # Functional groups are passed in. If empty, the LLM method handles it.
        llm_suggestions_str = self._get_disconnections_from_llm(smiles, functional_groups)

        return {
            "smiles": smiles,
            "functional_groups_identified": functional_groups, # Echo back for context
            "disconnection_suggestions": llm_suggestions_str # This is the string from LLM
        }