import os
import re
import time
import pandas as pd
from typing import Optional
from rdkit import Chem
from rdkit.Chem import AllChem
from rdkit.Chem.Draw import ReactionToImage, MolToImage # Assuming this was a typo fix for rdMolDraw2D.MolTo изображаемое

# --- Import tools using the AGENT-HUB registry ---
# This assumes that load_all_tools() has been called or tools are imported to register them.
# For a standalone script, you might need to ensure this happens.
from app.tools.registry import get_tool_instance, load_all_tools
from app.config.settings import get_settings # To initialize settings for tools

# --- Initialize AGENT-HUB components (needed if running this standalone) ---
# This is a simplified setup for a standalone script using these functions.
# In your main AGENT-HUB app, this loading happens at startup.
_settings = get_settings()
load_all_tools() # Ensure all tools are registered and can be fetched

# --- Global Path for Visualizations (using AGENT-HUB settings) ---
AUTOGEN_VISUALIZATION_DIR = _settings.VISUALIZATION_DIR_ABS # Use path from settings
# os.makedirs(AUTOGEN_VISUALIZATION_DIR, exist_ok=True) # Settings property should handle this

# --- Utility for sanitizing filenames ---
def sanitize_filename_ag(name: str) -> str:
    if not isinstance(name, str): name = str(name)
    name = re.sub(r'[^\w\.\-]+', '_', name); return name[:100]

# --- AutoGen Callable Functions (using AGENT-HUB tool instances) ---

def get_functional_groups(smiles_or_reaction_smiles: str) -> str:
    print(f"[AutoGenTool] get_functional_groups for: {smiles_or_reaction_smiles}")
    try:
        tool = get_tool_instance("FuncGroups")
        # The tool's execute method handles the _run call and error packaging
        result = tool.execute(query_smiles=smiles_or_reaction_smiles)

        if isinstance(result, dict) and "error" in result:
            return f"Error from FuncGroups tool: {result['error']}"
        # FuncGroupAgent formats the reply nicely; here we just stringify the raw tool output.
        # You might want to replicate the agent's formatting if this output is user-facing.
        return f"Functional Group Analysis: {str(result)}"
    except Exception as e:
        return f"Error in get_functional_groups tool wrapper: {str(e)}"

def convert_name_to_smiles(chemical_name: str) -> str:
    print(f"[AutoGenTool] convert_name_to_smiles for: {chemical_name}")
    try:
        tool = get_tool_instance("NameToSMILES")
        # NameToSMILES tool's execute method directly returns a string or error string
        return tool.execute(chemical_name=chemical_name)
    except Exception as e:
        return f"Error in convert_name_to_smiles tool wrapper: {str(e)}"

def convert_smiles_to_name(smiles_string: str) -> str:
    print(f"[AutoGenTool] convert_smiles_to_name for: {smiles_string}")
    try:
        tool = get_tool_instance("SMILES2Name")
        return tool.execute(smiles_string=smiles_string)
    except Exception as e:
        return f"Error in convert_smiles_to_name tool wrapper: {str(e)}"

def analyze_reaction_bond_changes(reaction_smiles: str) -> str:
    print(f"[AutoGenTool] analyze_reaction_bond_changes for: {reaction_smiles}")
    try:
        tool = get_tool_instance("BondAnalyzer")
        result = tool.execute(reaction_smiles=reaction_smiles)
        if isinstance(result, dict) and "error" in result:
            return f"Error from BondAnalyzer tool: {result['error']}"
        return f"Bond Change Analysis: {str(result)}"
    except Exception as e:
        return f"Error in analyze_reaction_bond_changes tool wrapper: {str(e)}"

def visualize_chemical_structure(smiles_or_reaction_smiles: str) -> str:
    print(f"[AutoGenTool] visualize_chemical_structure for: {smiles_or_reaction_smiles}")
    try:
        visualizer_tool = get_tool_instance("ChemVisualizer")
        # ChemVisualizerTool needs detect_input_type, but the execute method takes output_file
        # This function needs to prepare the output_file path.

        input_type = visualizer_tool.detect_input_type(smiles_or_reaction_smiles)
        timestamp = time.strftime("%Y%m%d_%H%M%S")
        sanitized_smiles_for_filename = sanitize_filename_ag(smiles_or_reaction_smiles)
        
        # Use paths from settings
        relative_filename = f"{input_type}_{sanitized_smiles_for_filename}_{timestamp}.png"
        absolute_output_filepath = os.path.join(_settings.VISUALIZATION_DIR_ABS, relative_filename)
        web_path = f"{_settings.VISUALIZATION_WEB_BASE_PATH}/{relative_filename}" # Path for web access

        # The ChemVisualizerTool's execute method returns the absolute_output_filepath on success
        # or an error string.
        tool_result = visualizer_tool.execute(
            smiles_or_reaction_smiles=smiles_or_reaction_smiles,
            output_file=absolute_output_filepath
        )

        if tool_result == absolute_output_filepath:
            return f"Visualization generated. Web path: {web_path} (Absolute: {absolute_output_filepath})"
        else: # It's an error string from the tool or its BaseTool.execute wrapper
            return tool_result
    except Exception as e:
        import traceback
        return f"Error in visualize_chemical_structure tool wrapper: {str(e)}\n{traceback.format_exc()}"


def classify_reaction_and_get_details(reaction_smiles: str, property_to_query: Optional[str] = None) -> str:
    # Combined for simplicity, matching ReactionClassifierAgent
    action = "classify_reaction_and_get_details" if not property_to_query else f"query_specific_property ({property_to_query})"
    print(f"[AutoGenTool] {action} for: {reaction_smiles}")
    try:
        tool = get_tool_instance("ReactionClassifier")
        # ReactionClassifierTool's execute directly returns the report string or error string
        return tool.execute(reaction_smiles=reaction_smiles, query=property_to_query)
    except Exception as e:
        return f"Error during {action} tool execution: {str(e)}"

# This function was called query_specific_property_for_reaction before, merging it.
# def query_specific_property_for_reaction(reaction_smiles: str, property_to_query: str) -> str:
#     return classify_reaction_and_get_details(reaction_smiles, property_to_query)
    
def suggest_disconnections(smiles: str) -> str:
    print(f"[AutoGenTool] suggest_disconnections for: {smiles}")
    if ">>" in smiles:
        return "Error: This tool is for single molecules, not reactions. Please provide a molecule SMILES."

    try:
        fg_tool = get_tool_instance("FuncGroups")
        disconnection_tool = get_tool_instance("DisconnectionSuggester")

        fg_result = fg_tool.execute(query_smiles=smiles)
        functional_groups = []
        if isinstance(fg_result, dict) and "functional_groups" in fg_result:
            functional_groups = fg_result["functional_groups"]
        elif isinstance(fg_result, dict) and "error" in fg_result:
            # Proceed without FGs, or return error. Let's proceed.
            print(f"[suggest_disconnections] Could not fetch FGs: {fg_result['error']}")
        
        disconnection_result = disconnection_tool.execute(smiles=smiles, functional_groups=functional_groups)
        
        if isinstance(disconnection_result, dict) and "error" in disconnection_result:
            return f"Error from DisconnectionSuggester tool: {disconnection_result['error']}"
        
        # Format output similar to DisconnectionSuggesterAgent
        reply_parts = [
            f"Disconnection Suggestions for SMILES: {disconnection_result.get('smiles', smiles)}",
            f"Functional Groups Considered: {', '.join(disconnection_result.get('functional_groups_identified', ['N/A'])) or 'N/A'}",
            "\nSuggestions from LLM:",
            disconnection_result.get('disconnection_suggestions', "No suggestions available or LLM error.")
        ]
        return "\n".join(reply_parts)

    except Exception as e:
        return f"Error in suggest_disconnections tool wrapper: {str(e)}"


def get_full_chemical_report(chemical_identifier: str) -> str:
    print(f"[AutoGenTool] get_full_chemical_report for: {chemical_identifier}")
    try:
        # ChemicalPropsTool is the AGENT-HUB tool name for ChemicalAnalysisAgent's logic
        tool = get_tool_instance("ChemicalProperties")
        # ChemicalPropsTool.execute returns the full report string or an error string
        report_or_error_str = tool.execute(chemical_identifier=chemical_identifier)
        return report_or_error_str
    except Exception as e:
        import traceback
        print(f"Error within get_full_chemical_report for '{chemical_identifier}': {str(e)}\n{traceback.format_exc()}")
        return f"Error generating full chemical report for '{chemical_identifier}': {str(e)}"