# File: AGENT-HUB/app/tools/__init__.py

# Core components from the registry and base
from .registry import register_tool, get_tool_instance, get_all_tool_names, load_all_tools, ToolNotFoundException # This line will now work
from .base import BaseTool

# --- Import tool modules to ensure their @register_tool decorator runs ---
from . import asckos
from . import chemical_property
from . import reaction_info
from . import bond
from . import disconnection
from . import funcgroups
from . import name2smiles
from . import smiles2name
from . import visualizer
# ... any other tool modules ...

__all__ = [
    "register_tool",
    "get_tool_instance",
    "get_all_tool_names",
    "load_all_tools",
    "ToolNotFoundException",
    "BaseTool",
]