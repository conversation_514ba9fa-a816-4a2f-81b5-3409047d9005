from typing import Dict, Any, Optional # Keep only necessary imports

from .base import BaseTool
from .registry import register_tool
from ..config.settings import Settings
from ..core.chemical_analysis import ChemicalAnalysisLogic, ChemicalPropertiesData # Import core components

@register_tool
class ChemicalPropsTool(BaseTool):
    name = "ChemicalProperties"
    description = ("Provides a comprehensive analysis report for a chemical compound (name, SMILES, or CAS). "
                   "Includes properties, safety, pricing, and environmental impact using PubChem and LLMs.")

    def __init__(self, settings: Settings, **kwargs):
        super().__init__(settings=settings)
        self.analysis_logic = ChemicalAnalysisLogic(settings=settings)

    def _run(self, chemical_identifier: str) -> str: # Returns string report
        print(f"[{self.name} Tool] _run called for: {chemical_identifier}")
        if not chemical_identifier or not isinstance(chemical_identifier, str) or not chemical_identifier.strip():
            # Let the BaseTool.execute handle this by raising ValueError
            raise ValueError(f"[{self.name} Error] Invalid or empty chemical_identifier provided.")
        
        # The main analysis and report generation is handled by ChemicalAnalysisLogic
        # The execute method in BaseTool will catch exceptions from here.
        properties_data_obj: ChemicalPropertiesData = self.analysis_logic.analyze_chemical(chemical_identifier)
        
        # analyze_chemical should ideally always return a valid ChemicalPropertiesData object,
        # even if it's mostly filled with "Unknown" or "N/A" for error cases internally.
        # So, we can directly proceed to generate a report.
        report_string = self.analysis_logic.generate_report(properties_data_obj)
        return report_string