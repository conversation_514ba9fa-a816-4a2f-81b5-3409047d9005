import os 
import sys
import json
from typing import Optional, Dict, Any
import requests
import urllib.parse
try:
    import openai
except ImportError:
    openai = None

# Ensure these imports match your project structure
from .base import BaseTool
from .registry import register_tool
from ..config.settings import Settings

@register_tool
class SmilesToNameTool(BaseTool):
    name = "SMILES2Name"
    description = ( # Updated description
        "Converts a SMILES string to its IUPAC chemical name using CACTUS, PubChem, "
        "and an LLM as a final fallback for the IUPAC name."
    )
    
    _openai_client: Optional[Any] = None

    def __init__(self, settings: Settings, **kwargs):
        super().__init__(settings=settings)
        # Initialize OpenAI client if key is available and module is imported
        if (self.settings.OPENAI_API_KEY and 
            "your_openai_api_key_here" not in self.settings.OPENAI_API_KEY.lower() and # case-insensitive
            "your_openai_key_missing_in_env" not in self.settings.OPENAI_API_KEY.lower()): # case-insensitive
            if openai:
                try:
                    self._openai_client = openai.AzureOpenAI()
                    # print(f"[{self.name}] OpenAI client initialized for IUPAC name fallback.")
                except Exception as e_init:
                     print(f"[{self.name}] Warning: Could not initialize OpenAI client: {e_init}.")
            else:
                print(f"[{self.name}] Warning: OpenAI library not imported. LLM fallback for IUPAC name will be unavailable.")
        else:
            # Updated warning message
            print(f"[{self.name}] Warning: OPENAI_API_KEY not set or contains placeholder. LLM fallback for IUPAC name will be unavailable.")
        
        self.cactus_url_template = getattr(settings, 'CACTUS_SMILES_TO_NAME_URL_TEMPLATE', "https://cactus.nci.nih.gov/chemical/structure/{smiles}/iupac_name")
        self.pubchem_smiles_prop_url_template = getattr(settings, 'PUBCHEM_SMILES_PROP_URL_TEMPLATE', "https://pubchem.ncbi.nlm.nih.gov/rest/pug/compound/smiles/{smiles}/property/IUPACName/JSON")

    # _get_common_name_from_llm method is REMOVED

    def _get_iupac_name_from_llm(self, smiles: str) -> Optional[str]: # New method
        if not self._openai_client:
            # print(f"[{self.name}] LLM for IUPAC name not available (client not initialized).")
            return None
        if not smiles: # Should ideally be caught by RDKit validation earlier
            return None

        try:
            prompt_messages = [
                {"role": "system", "content": "You are a helpful chemistry assistant. Your task is to provide the IUPAC name for a given chemical compound represented by its SMILES string. Respond concisely with only the IUPAC name. If you cannot determine the IUPAC name, state 'Could not determine IUPAC name from SMILES'."},
                {"role": "user", "content": f"What is the IUPAC name for the chemical with SMILES string: '{smiles}'?"}
            ]
            
            model_to_use = getattr(self.settings, 'OPENAI_DEFAULT_MODEL', "gpt-4o") 
            # print(f"[{self.name}] Querying LLM ({model_to_use}) for IUPAC name of SMILES: {smiles}")

            response = self._openai_client.chat.completions.create(
                model=model_to_use,
                messages=prompt_messages,
                temperature=0, 
                max_tokens=200, # IUPAC names can be long
                n=1, 
                stop=None,
            )
            llm_output = response.choices[0].message.content.strip()

            if "could not determine iupac name" in llm_output.lower() or \
               not llm_output or \
               len(llm_output) > 250: # Arbitrary upper limit for a valid name from LLM
                # print(f"[{self.name}] LLM could not determine IUPAC name or output invalid for '{smiles}': {llm_output}")
                return None
            return llm_output
        except Exception as api_e:
            error_msg_detail = f"Error during LLM IUPAC name lookup for '{smiles}' ({str(api_e)[:100]}...)."
            if openai and hasattr(openai, 'APIError') and isinstance(api_e, openai.APIError):
                status_code = api_e.status_code if hasattr(api_e, 'status_code') else 'N/A'
                error_message_from_api = api_e.message if hasattr(api_e, 'message') else str(api_e)
                error_msg_detail = f"OpenAI API error ({status_code}) for IUPAC name of '{smiles}': {error_message_from_api[:100]}..."
            print(f"[{self.name}] {error_msg_detail}") # Log this error
            return None

    def _run(self, smiles_string: str) -> str:
        # print(f"[{self.name} Tool] _run called with SMILES: {smiles_string}")
        
        # 1. Basic RDKit validation (optional but good practice)
        try:
            from rdkit import Chem
            mol = Chem.MolFromSmiles(smiles_string)
            if not mol:
                return f"Error: Invalid SMILES string provided: '{smiles_string}'"
            # Optional: Canonicalize SMILES for potentially better results with web services
            # smiles_string_canonical = Chem.MolToSmiles(mol, isomericSmiles=True, canonical=True)
            # Use smiles_string_canonical below if you enable this
        except ImportError:
            # print(f"[{self.name} Tool] RDKit not available. Proceeding without SMILES validation/canonicalization.")
            pass # RDKit not installed, proceed, web services might still handle it or fail
        except Exception as e_rdkit:
            # print(f"[{self.name} Tool] RDKit processing error for '{smiles_string}': {e_rdkit}. Proceeding with original SMILES.")
            # Depending on desired strictness, could return an error here:
            # return f"Error: RDKit could not process SMILES '{smiles_string}': {e_rdkit}"
            pass


        iupac_name_val: Optional[str] = None
        source: Optional[str] = None

        # 2. Try CACTUS
        # print(f"[{self.name} Tool] Attempting CACTUS for SMILES: {smiles_string}")
        iupac_name_val = self._try_cactus(smiles_string)
        if iupac_name_val:
            source = "CACTUS"
        
        # 3. Try PubChem if CACTUS failed
        if not iupac_name_val:
            # print(f"[{self.name} Tool] CACTUS failed. Attempting PubChem for SMILES: {smiles_string}")
            iupac_name_val = self._try_pubchem(smiles_string)
            if iupac_name_val:
                source = "PubChem"

        # 4. Try LLM if both CACTUS and PubChem failed
        if not iupac_name_val:
            # print(f"[{self.name} Tool] CACTUS & PubChem failed. Attempting LLM for IUPAC name of SMILES: {smiles_string}")
            iupac_name_val = self._get_iupac_name_from_llm(smiles_string)
            if iupac_name_val:
                source = "LLM (OpenAI)"
        
        if iupac_name_val:
            return f"IUPAC Name: {iupac_name_val} (Source: {source})"
        else:
            return f"Error: Could not resolve IUPAC name for SMILES '{smiles_string}' from CACTUS, PubChem, or LLM."

    def _try_cactus(self, smiles: str) -> Optional[str]:
        try:
            encoded_smiles = urllib.parse.quote(smiles) 
            url = self.cactus_url_template.format(smiles=encoded_smiles)
            # print(f"[{self.name} Tool] Querying CACTUS: {url}")
            res = requests.get(url, timeout=7, headers={'Accept': 'text/plain'}) # Specify accept for plain text
            res.raise_for_status() # Raises HTTPError for bad responses (4XX or 5XX)
            name_text = res.text.strip()
            
            if name_text and \
               "Page not found" not in name_text and \
               "<html" not in name_text.lower() and \
               "could not be resolved" not in name_text.lower() and \
               "error" not in name_text.lower() and \
               len(name_text) > 0 and len(name_text) < 300: # Max reasonable length for an IUPAC name
                return name_text
        except requests.exceptions.Timeout:
            # print(f"[{self.name} Tool] CACTUS request timed out for SMILES: {smiles}")
            pass
        except requests.exceptions.RequestException as e: 
            # print(f"[{self.name} Tool] CACTUS request failed for SMILES '{smiles}': {e}")
            pass
        return None

    def _try_pubchem(self, smiles: str) -> Optional[str]:
        try:
            encoded_smiles = urllib.parse.quote_plus(smiles) 
            url = self.pubchem_smiles_prop_url_template.format(smiles=encoded_smiles)
            # print(f"[{self.name} Tool] Querying PubChem: {url}")
            res = requests.get(url, timeout=7, headers={'Accept': 'application/json'})
            res.raise_for_status()
            data = res.json()
            
            properties = data.get('PropertyTable', {}).get('Properties', [])
            if properties and isinstance(properties, list) and len(properties) > 0:
                iupac_name = properties[0].get('IUPACName')
                if iupac_name and isinstance(iupac_name, str) and 0 < len(iupac_name) < 300: # Max reasonable length
                    return iupac_name
        except requests.exceptions.Timeout:
            # print(f"[{self.name} Tool] PubChem request timed out for SMILES: {smiles}")
            pass
        except requests.exceptions.RequestException as e: 
            # print(f"[{self.name} Tool] PubChem request failed for SMILES '{smiles}': {e}")
            pass
        except json.JSONDecodeError as e: 
            # print(f"[{self.name} Tool] PubChem JSON decode error for SMILES '{smiles}': {e}")
            pass
        return None