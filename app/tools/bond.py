from rdkit import Chem
from rdkit.Chem import AllChem
from typing import Any, Dict, Optional
import re
from rxnmapper import R<PERSON><PERSON><PERSON><PERSON>per # Ensure this is in requirements.txt

from .base import BaseTool
from .registry import register_tool
from ..config.settings import Settings

@register_tool
class BondAnalyzerTool(BaseTool):
    name = "BondAnalyzer"
    description = ("Identifies bonds broken, formed, and changed in a chemical reaction. "
                   "Works with mapped or unmapped reaction SMILES (will attempt to map unmapped ones).")
    
    _mapper: Optional[RXNMapper] = None # Type hint, make it Optional

    def __init__(self, settings: Settings, **kwargs):
        super().__init__(settings=settings)
        try:
            self._mapper = RXNMapper()
            print(f"[{self.name}] RXNMapper initialized.")
        except Exception as e:
            print(f"[{self.name}] Critical Error: Failed to initialize RXNMapper: {e}. This tool might fallback or fail.")
            self._mapper = None

    def _map_reaction(self, rxn_smiles: str) -> str:
        if not self._mapper:
            print(f"[{self.name}] RXNMapper not available in _map_reaction. Falling back to RDKit mapping.")
            return self._rdkit_map_reaction(rxn_smiles)

        try:
            atom_map_pattern = r':[0-9]+'
            if re.search(atom_map_pattern, rxn_smiles):
                return rxn_smiles

            original_rxn_for_log = rxn_smiles
            # Minimal cleaning, RXNMapper might handle some of this.
            # A more robust SMILES parser/normalizer could be used here if needed.
            processed_rxn_smiles = rxn_smiles.replace(" ", "") # Remove all spaces as a basic step
            
            # Handle '+' for multiple molecules if not within brackets (simplistic)
            if ">>" in processed_rxn_smiles:
                parts = processed_rxn_smiles.split(">>", 1)
                if len(parts) == 2:
                    reactants, products = parts
                    # Only replace '+' with '.' if '+' is likely a molecule separator
                    if "+" in reactants and not re.search(r'\[[^\]]*\+[^\]]*\]', reactants):
                         reactants = reactants.replace("+", ".")
                    if "+" in products and not re.search(r'\[[^\]]*\+[^\]]*\]', products):
                         products = products.replace("+", ".")
                    processed_rxn_smiles = f"{reactants}>>{products}"
            
            # print(f"[{self.name}._map_reaction] Attempting to map (RXNMapper): {original_rxn_for_log} -> {processed_rxn_smiles}")
            results_list = self._mapper.get_attention_guided_atom_maps([processed_rxn_smiles])
            
            if results_list and isinstance(results_list, list) and len(results_list) > 0:
                first_result = results_list[0]
                if isinstance(first_result, dict) and 'mapped_rxn' in first_result and first_result['mapped_rxn']:
                    # print(f"[{self.name}._map_reaction] RXNMapper success: {first_result['mapped_rxn']}")
                    return first_result['mapped_rxn']
            
            # print(f"[{self.name}._map_reaction] RXNMapper did not return a valid mapped reaction for: {processed_rxn_smiles}. Results: {results_list}. Falling back.")
            return self._rdkit_map_reaction(original_rxn_for_log) # Fallback with original for RDKit

        except Exception as e:
            # print(f"[{self.name}._map_reaction] RXNMapper error for '{rxn_smiles}': {str(e)}. Trying RDKit fallback with original SMILES.")
            return self._rdkit_map_reaction(rxn_smiles) # Fallback with original if processing error

    def _rdkit_map_reaction(self, rxn_smiles: str) -> str:
        try:
            # print(f"[{self.name}._rdkit_map_reaction] Attempting RDKit mapping for: {rxn_smiles}")
            # RDKit's ReactionFromSmarts can be sensitive. Basic cleaning might help.
            # No further cleaning here, assuming rxn_smiles is as good as it gets for RDKit.
            rxn = AllChem.ReactionFromSmarts(rxn_smiles, useSmiles=True)
            if not rxn:
                # print(f"[{self.name}._rdkit_map_reaction] RDKit ReactionFromSmarts failed to parse: {rxn_smiles}")
                return "" 
            
            # ReactionMapAtoms modifies rxn in place.
            # The return code indicates success/failure type but isn't strictly needed if we check the result.
            AllChem.ReactionMapAtoms(rxn) 
            
            mapped_smiles_check = AllChem.ReactionToSmiles(rxn)
            if not re.search(r':[0-9]+', mapped_smiles_check): 
                # print(f"[{self.name}._rdkit_map_reaction] RDKit ReactionMapAtoms did not produce mapped atoms.")
                return ""
            # print(f"[{self.name}._rdkit_map_reaction] RDKit mapping successful.")
            return mapped_smiles_check
        except Exception as inner_e:
            # print(f"[{self.name}._rdkit_map_reaction] RDKit mapping error for '{rxn_smiles}': {str(inner_e)}")
            return ""

    def _get_bond_changes(self, mapped_rxn: str) -> Dict[str, Any]:
        try:
            parts = mapped_rxn.split(">>")
            if len(parts) != 2:
                return {"error": f"Invalid mapped reaction SMILES format: {mapped_rxn}", "bonds_broken": [], "bonds_formed": [], "bonds_changed": []}
            mapped_reactants_smi, mapped_products_smi = parts

            reactant_mol = Chem.MolFromSmiles(mapped_reactants_smi)
            product_mol = Chem.MolFromSmiles(mapped_products_smi)
            
            if not reactant_mol: return {"error": f"Could not parse mapped reactants: {mapped_reactants_smi}", "bonds_broken": [], "bonds_formed": [], "bonds_changed": []}
            if not product_mol: return {"error": f"Could not parse mapped products: {mapped_products_smi}", "bonds_broken": [], "bonds_formed": [], "bonds_changed": []}

            # Atom map number to index dictionaries
            reactant_atoms_map_to_idx = {atom.GetAtomMapNum(): atom.GetIdx() for atom in reactant_mol.GetAtoms() if atom.GetAtomMapNum() > 0}
            product_atoms_map_to_idx = {atom.GetAtomMapNum(): atom.GetIdx() for atom in product_mol.GetAtoms() if atom.GetAtomMapNum() > 0}

            # Store bonds using sorted atom map numbers as keys
            reactant_bonds = {} # (map_num1, map_num2) -> bond_type_str
            for bond in reactant_mol.GetBonds():
                map_nums = sorted([bond.GetBeginAtom().GetAtomMapNum(), bond.GetEndAtom().GetAtomMapNum()])
                if map_nums[0] > 0 and map_nums[1] > 0: # Both atoms must be mapped
                    reactant_bonds[tuple(map_nums)] = str(bond.GetBondType())
            
            product_bonds = {}
            for bond in product_mol.GetBonds():
                map_nums = sorted([bond.GetBeginAtom().GetAtomMapNum(), bond.GetEndAtom().GetAtomMapNum()])
                if map_nums[0] > 0 and map_nums[1] > 0:
                    product_bonds[tuple(map_nums)] = str(bond.GetBondType())
            
            bonds_broken_list = []
            bonds_changed_list = []
            
            # Compare reactant bonds to product bonds
            for bond_key_maps, r_bond_type_str in reactant_bonds.items():
                map1, map2 = bond_key_maps
                # Get atom symbols (ensure atoms exist in map_to_idx before accessing RDKit mol)
                atom1_r_symbol = reactant_mol.GetAtomWithIdx(reactant_atoms_map_to_idx[map1]).GetSymbol() if map1 in reactant_atoms_map_to_idx else '?'
                atom2_r_symbol = reactant_mol.GetAtomWithIdx(reactant_atoms_map_to_idx[map2]).GetSymbol() if map2 in reactant_atoms_map_to_idx else '?'

                if bond_key_maps not in product_bonds:
                    bonds_broken_list.append(f"{atom1_r_symbol}-{atom2_r_symbol} ({r_bond_type_str}) between atoms mapped {map1}-{map2}")
                elif product_bonds[bond_key_maps] != r_bond_type_str:
                    p_bond_type_str = product_bonds[bond_key_maps]
                    bonds_changed_list.append(f"{atom1_r_symbol}-{atom2_r_symbol}: {r_bond_type_str} to {p_bond_type_str} between atoms mapped {map1}-{map2}")
            
            bonds_formed_list = []
            for bond_key_maps, p_bond_type_str in product_bonds.items():
                if bond_key_maps not in reactant_bonds:
                    map1, map2 = bond_key_maps
                    atom1_p_symbol = product_mol.GetAtomWithIdx(product_atoms_map_to_idx[map1]).GetSymbol() if map1 in product_atoms_map_to_idx else '?'
                    atom2_p_symbol = product_mol.GetAtomWithIdx(product_atoms_map_to_idx[map2]).GetSymbol() if map2 in product_atoms_map_to_idx else '?'
                    bonds_formed_list.append(f"{atom1_p_symbol}-{atom2_p_symbol} ({p_bond_type_str}) between atoms mapped {map1}-{map2}")
            
            return {
                "bonds_broken": bonds_broken_list,
                "bonds_formed": bonds_formed_list,
                "bonds_changed": bonds_changed_list
            }
        except Exception as e:
            return {"error": f"Error extracting bond changes from '{mapped_rxn}': {str(e)}", "bonds_broken": [], "bonds_formed": [], "bonds_changed": []}

    def _run(self, reaction_smiles: str) -> Dict[str, Any]:
        print(f"[{self.name} Tool] _run called with: {reaction_smiles}")
        
        # Re-check _mapper initialization, as it can fail in __init__
        if not self._mapper:
            # Attempt a one-time re-initialization if it failed during class init
            print(f"[{self.name}] RXNMapper was not initialized. Attempting now...")
            try:
                self._mapper = RXNMapper()
                print(f"[{self.name}] RXNMapper successfully re-initialized in _run.")
            except Exception as e_reinit:
                print(f"[{self.name}] Critical Error: Failed to re-initialize RXNMapper in _run: {e_reinit}. Cannot proceed.")
                return {"error": "BondAnalyzer's RXNMapper component failed to initialize."}
        
        try:
            if ">>" not in reaction_smiles:
                return {"error": "Input is not a valid reaction SMILES (missing '>>')."}
            
            # Basic cleaning: remove spaces, as RXNMapper might be sensitive
            # A more robust SMILES normalization/parsing could be added if needed
            cleaned_rxn_smiles = reaction_smiles.replace(" ", "")
            
            mapped_rxn = self._map_reaction(cleaned_rxn_smiles) 
            if not mapped_rxn: # _map_reaction returns empty string on failure
                return {"error": "Failed to map the reaction. Please check the reaction SMILES format and validity."}
            
            bond_changes_result = self._get_bond_changes(mapped_rxn) 
            if "error" in bond_changes_result: 
                return bond_changes_result 
                
            result = {
                "mapped_reaction": mapped_rxn,
                "bonds_broken": bond_changes_result["bonds_broken"],
                "bonds_formed": bond_changes_result["bonds_formed"],
                "bonds_changed": bond_changes_result["bonds_changed"]
            }
            
            # Add a note if the original input was different from the cleaned one processed for mapping
            # and if the original input was not already mapped.
            if mapped_rxn != cleaned_rxn_smiles and not re.search(r':[0-9]+', reaction_smiles):
                result["note"] = "The reaction was automatically mapped for analysis."
                if reaction_smiles != cleaned_rxn_smiles: # If cleaning also happened
                     result["original_input_for_mapping"] = reaction_smiles
                     result["processed_for_mapping"] = cleaned_rxn_smiles
                
            return result
        except Exception as e:
            raise # Re-raise for BaseTool to handle