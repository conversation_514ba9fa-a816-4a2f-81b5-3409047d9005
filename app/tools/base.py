from abc import ABC, abstractmethod
from typing import Any, Dict
from ..config.settings import Settings # For type hinting

class BaseTool(ABC):
    name: str
    description: str

    def __init__(self, settings: Settings, **kwargs):
        self.settings = settings
        if not hasattr(self, 'name') or not self.name:
            raise ValueError(f"Tool {self.__class__.__name__} must have a 'name' attribute defined.")
        if not hasattr(self, 'description') or not self.description:
            raise ValueError(f"Tool {self.__class__.__name__} must have a 'description' attribute defined.")
        # print(f"Tool '{self.name}' initialized with settings.")

    @abstractmethod
    def _run(self, *args, **kwargs) -> Any:
        """
        The core logic of the tool.
        Input arguments should be clearly defined by subclasses.
        Should return the direct result of the tool's operation or raise an exception.
        """
        pass

    def execute(self, *args, **kwargs) -> Any:
        """
        Public method to run the tool. Includes basic error handling and logging.
        """
        # print(f"Executing tool: {self.name} with args: {args}, kwargs: {kwargs}") # Basic logging
        try:
            return self._run(*args, **kwargs)
        except Exception as e:
            import traceback
            print(f"Error in tool {self.name}: {str(e)}\n{traceback.format_exc()}")
            # Return a dictionary indicating an error, which agents can then process.
            return {"error": f"Tool Error ({self.name}): {str(e)}"}
