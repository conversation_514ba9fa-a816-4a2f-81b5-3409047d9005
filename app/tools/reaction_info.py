import os
import re
import json
import time
import pandas as pd
from rdkit import Chem, <PERSON><PERSON><PERSON><PERSON><PERSON>
from functools import lru_cache
from typing import Dict, Any, Optional, List, Tuple
import logging
from concurrent.futures import ThreadPoolExecutor, as_completed

import requests
# import pubchempy as pcp

from .base import BaseTool
from .registry import register_tool, get_tool_instance
from ..config.settings import Settings
from ..core.chemical_analysis import (
    ChemicalAnalysisLogic,
    ChemicalPropertiesData,
    HazardLevel,
    SolubilityType,
    COMMON_AUXILIARIES_BY_NAME_LOWER,
    COMMON_AUXILIARIES_BY_SMILES
)

logger = logging.getLogger(__name__)
if not logging.getLogger().hasHandlers():
    logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')

RDLogger.DisableLog('rdApp.*')

@register_tool
class ReactionRecipeGeneratorTool(BaseTool):
    name = "ReactionRecipeGenerator"
    description = ("Generates a detailed JSON recipe card for a chemical reaction, including "
                   "components, conditions, safety, pricing, and visualization.")

    def __init__(self, settings: Settings, **kwargs):
        super().__init__(settings=settings)
        # Note the .format() call to handle dynamic hostnames from settings
        self.askcos_api_url = self.settings.REACTION_CLASSIFIER_API_URL.format(AZURE_HOSTNAME=self.settings.AZURE_ASKOS_HOSTNAME)
        self.reaction_class = ''
        self.chemical_analysis_logic = ChemicalAnalysisLogic(settings=settings)

        self.dataset1: Optional[pd.DataFrame] = None
        self.dataset2: Optional[pd.DataFrame] = None
        self._try_load_reaction_datasets()
        
        self.visualization_tool: Optional[BaseTool] = None
        try:
            self.visualization_tool = get_tool_instance("ChemVisualizer")
            logger.info(f"[{self.name}] ChemVisualizer tool instance obtained successfully.")
        except Exception as e:
            logger.warning(f"[{self.name}] Could not get ChemVisualizer tool instance: {e}. Visualizations will be unavailable.")

    def _try_load_reaction_datasets(self):
        try:
            if self.settings.REACTION_DATASET_PATH1 and os.path.exists(self.settings.REACTION_DATASET_PATH1):
                self.dataset1 = pd.read_parquet(self.settings.REACTION_DATASET_PATH1)
                logger.info(f"[{self.name}] Successfully loaded reaction dataset 1: {self.settings.REACTION_DATASET_PATH1}")
            elif self.settings.REACTION_DATASET_PATH1:
                 logger.warning(f"[{self.name}] Warning: Reaction Dataset 1 path provided but file not found: {self.settings.REACTION_DATASET_PATH1}")
            
            if self.settings.REACTION_DATASET_PATH2 and os.path.exists(self.settings.REACTION_DATASET_PATH2):
                self.dataset2 = pd.read_parquet(self.settings.REACTION_DATASET_PATH2)
                logger.info(f"[{self.name}] Successfully loaded reaction dataset 2: {self.settings.REACTION_DATASET_PATH2}")
            elif self.settings.REACTION_DATASET_PATH2:
                 logger.warning(f"[{self.name}] Warning: Reaction Dataset 2 path provided but file not found: {self.settings.REACTION_DATASET_PATH2}")
        except Exception as e:
            logger.error(f"[{self.name}] Error loading reaction datasets: {str(e)}", exc_info=True)

    @staticmethod
    def _get_price_source_confidence_for_reaction_component(supplier_info_list: List[Dict[str, Any]], llm_provider_for_chem_agent: Optional[str]) -> Tuple[str, str, Optional[str]]:
        if not supplier_info_list: return "N/A", "N/A", None
        first_source_info = supplier_info_list[0]
        source_type = first_source_info.get("source_type", "Unknown")
        source_name_detail = first_source_info.get("name", "Unknown Source")
        raw_llm_confidence = None
        if source_type == "Local JSON":
            display_name_match = re.search(r"Local DB:\s*(.*?)(?:\s*\(.*?\))?(?:\s*-\s*Key:.*)?$", source_name_detail)
            confidence_str = "High"
            source_str = "Local Database"
            if display_name_match:
                db_name_part = display_name_match.group(1).strip()
                if "Sigma" in db_name_part: source_str = "Sigma-Aldrich Catalog (Local Cache)"
                elif "Primary" in db_name_part: source_str = "Primary Local Data (Local Cache)"
                elif "Secondary" in db_name_part: source_str = "Secondary Local Data (Local Cache)"
                else: source_str = f"{db_name_part} (Local Cache)"
            elif "Local DB:" in source_name_detail: source_str = source_name_detail.replace("Local DB:", "").split('(')[0].strip() + " (Local Cache)"
            return source_str, confidence_str, None
        elif source_type.startswith("LLM"):
            availability_str = first_source_info.get("availability", "")
            conf_match = re.search(r"Conf(?:idence)?:\s*([\w-]+)", availability_str, re.IGNORECASE)
            confidence_str_from_llm = "medium"
            if conf_match:
                raw_llm_confidence = conf_match.group(1).strip()
                confidence_str_from_llm = raw_llm_confidence.lower()
            if confidence_str_from_llm in ["high", "very_high", "very-high"]: final_confidence_display = "High"
            elif confidence_str_from_llm == "medium": final_confidence_display = "Medium"
            else: final_confidence_display = "Low"
            llm_provider_name_part = llm_provider_for_chem_agent or 'LLM'
            if llm_provider_name_part.lower() not in source_name_detail.lower(): source_str = f"{source_name_detail} ({llm_provider_name_part})"
            else: source_str = source_name_detail
            return source_str, final_confidence_display, raw_llm_confidence
        return source_name_detail, "Unknown", None
    
    @lru_cache(maxsize=50)
    def _call_askcos_api(self, reaction_smiles: str) -> Optional[List[Dict[str, Any]]]:
        if not reaction_smiles: return None
        headers = {"Content-Type": "application/json"}
        payload = {"smiles": [reaction_smiles]}
        try:
            # Using self.askcos_api_url which is formatted in __init__
            response = requests.post(self.askcos_api_url, headers=headers, json=payload, timeout=self.settings.HTTP_REQUEST_TIMEOUT)
            response.raise_for_status()
            data = response.json()
            if data.get("status") == "SUCCESS" and data.get("results"):
                results_content = data["results"]
                if isinstance(results_content, list) and len(results_content) > 0:
                    if isinstance(results_content[0], list) and all(isinstance(item, dict) for item in results_content[0]): return results_content[0]
                    elif isinstance(results_content[0], dict) and "rank" in results_content[0]: return results_content
                logger.warning(f"[{self.name} ASKCOS Info] Unexpected 'results' format for {reaction_smiles}: {results_content}")
            else: logger.warning(f"[{self.name} ASKCOS Warning] API call for {reaction_smiles} did not return SUCCESS or no results. Status: {data.get('status')}, Error: {data.get('error')}")
        except requests.exceptions.RequestException as e: logger.error(f"[{self.name} ASKCOS Error] Request failed for {reaction_smiles}: {e}", exc_info=True)
        except json.JSONDecodeError as e: logger.error(f"[{self.name} ASKCOS Error] Failed to decode JSON response for {reaction_smiles}: {e}", exc_info=True)
        except Exception as e: logger.error(f"[{self.name} ASKCOS Error] Unexpected error for {reaction_smiles}: {e}", exc_info=True)
        return None

    @staticmethod
    def _extract_orderly_data_from_row(orderly_match_row: pd.Series) -> Optional[Dict[str, Any]]:
        if orderly_match_row is None or orderly_match_row.empty: return None
        orderly_data = {}
        if 'procedure_details' in orderly_match_row.index and pd.notna(orderly_match_row['procedure_details']): orderly_data['procedure_details'] = str(orderly_match_row['procedure_details'])
        if 'rxn_time' in orderly_match_row.index and pd.notna(orderly_match_row['rxn_time']): orderly_data['rxn_time'] = str(orderly_match_row['rxn_time'])
        if 'temperature' in orderly_match_row.index and pd.notna(orderly_match_row['temperature']): orderly_data['temperature'] = str(orderly_match_row['temperature'])
        if 'yield_000' in orderly_match_row.index and pd.notna(orderly_match_row['yield_000']): orderly_data['yield_000'] = str(orderly_match_row['yield_000'])
        if 'atmosphere' in orderly_match_row.index and pd.notna(orderly_match_row['atmosphere']): orderly_data['atmosphere'] = str(orderly_match_row['atmosphere'])
        agents = [str(orderly_match_row[f'agent_{i:03d}']) for i in range(17) if f'agent_{i:03d}' in orderly_match_row.index and pd.notna(orderly_match_row[f'agent_{i:03d}'])]
        if agents: orderly_data['agents_list'] = agents
        solvents = [str(orderly_match_row[f'solvent_{i:03d}']) for i in range(11) if f'solvent_{i:03d}' in orderly_match_row.index and pd.notna(orderly_match_row[f'solvent_{i:03d}'])]
        if solvents: orderly_data['solvents_list'] = solvents
        return orderly_data if orderly_data else None

    @lru_cache(maxsize=100)
    def _query_reaction_dataset(self, reaction_smiles: str) -> Dict[str, Any]:
        results: Dict[str, Any] = {"orderly_data": None, "classification_data": None, "orderly_source_found": False, "classification_source_found": False}
        if not reaction_smiles: return results
        if self.dataset1 is not None and not self.dataset1.empty:
            df1 = self.dataset1
            for col_name in ['rxn_str', 'reaction_smiles', 'smiles']:
                if col_name in df1.columns:
                    try:
                        match_df = df1[df1[col_name].astype(str) == reaction_smiles]
                        if not match_df.empty:
                            results["orderly_data"] = self._extract_orderly_data_from_row(match_df.iloc[0])
                            results["orderly_source_found"] = True
                            break
                    except Exception as e: logger.warning(f"[{self.name} Dataset Query Warn] Error matching in Orderly column '{col_name}': {e}")
        if self.dataset2 is not None and not self.dataset2.empty:
            df2 = self.dataset2
            for col_name in ['rxn_str', 'reaction_smiles', 'smiles', 'canonical_rxn_smiles']:
                if col_name in df2.columns:
                    try:
                        match_df = df2[df2[col_name].astype(str) == reaction_smiles]
                        if not match_df.empty:
                            class_match_row = match_df.iloc[0]
                            for name_col in ['reaction_name', 'NAME', 'CLASS', 'class_label', 'template_name', 'name']:
                                if name_col in class_match_row.index and pd.notna(class_match_row[name_col]):
                                    if "classification_data" not in results or results["classification_data"] is None: results["classification_data"] = {}
                                    results["classification_data"]['reaction_name'] = str(class_match_row[name_col])
                                    results["classification_source_found"] = True
                                    break
                            if results["classification_source_found"]: break
                    except Exception as e: logger.warning(f"[{self.name} Dataset Query Warn] Error matching in Classification column '{col_name}': {e}")
        return results

    def _get_llm_predicted_reaction_details(self, reaction_smiles: str, reactant_names: List[str], product_names: List[str], askcos_top_classification_info: Optional[Dict[str, Any]]) -> Dict[str, Any]:
        askcos_context_str = "ASKCOS Top Classification: Not available."
        if askcos_top_classification_info:
            name = askcos_top_classification_info.get("reaction_name", askcos_top_classification_info.get("reaction_classname", "Unknown"))
            certainty = askcos_top_classification_info.get('prediction_certainty', 'N/A')
            askcos_context_str = f"ASKCOS Top Classification (Rank 1): {name} (Certainty: {certainty:.2f})" if isinstance(certainty, float) else f"ASKCOS Top Classification (Rank 1): {name} (Certainty: {certainty})"
        system_prompt = "You are an expert chemist. Based on the reaction and classification, predict typical experimental details. Return JSON."
        user_prompt = f"""Reaction SMILES: {reaction_smiles}
Identified Reactants: {', '.join(reactant_names) if reactant_names else 'N/A'}
Identified Products: {', '.join(product_names) if product_names else 'N/A'}
{askcos_context_str}
Provide typical experimental details for this reaction class. Return a JSON object with keys:
"predicted_reagents_catalysts", "predicted_solvents", "predicted_temperature", "predicted_time",
"predicted_atmosphere", "predicted_yield_range", "predicted_procedure_outline".
Example procedure: ["Combine reactants in solvent.", "Add catalyst and stir.", "Monitor reaction.", "Work-up and purify."]."""
        llm_response = self.chemical_analysis_logic._get_llm_completion_with_fallback(system_prompt, user_prompt, max_tokens=1500)
        return self.chemical_analysis_logic._parse_llm_json_response(llm_response, default_on_error={
            "predicted_reagents_catalysts": [], "predicted_solvents": [], "predicted_temperature": "Not specified by LLM",
            "predicted_time": "Not specified by LLM", "predicted_atmosphere": "Not specified by LLM",
            "predicted_yield_range": "Not specified by LLM", "predicted_procedure_outline": ["LLM could not predict procedure steps."]})

    def _generate_visualization(self, reaction_smiles: str) -> Optional[str]:
        if not self.visualization_tool:
            logger.info(f"[{self.name}] Visualization tool not available. Skipping visualization.")
            return None
        try:
            timestamp = time.strftime("%Y%m%d%H%M%S")
            sanitized_smiles_part = re.sub(r'[^\w\-\.]', '_', reaction_smiles)[:50]
            relative_filename = f"reaction_{sanitized_smiles_part}_{timestamp}.png"
            absolute_output_filepath = os.path.join(self.settings.VISUALIZATION_DIR_ABS, relative_filename)
            web_path = os.path.join(self.settings.VISUALIZATION_WEB_BASE_PATH, relative_filename).replace("\\", "/")
            viz_tool_output = self.visualization_tool.execute(
                smiles_or_reaction_smiles=reaction_smiles,
                output_file=absolute_output_filepath
            )
            if isinstance(viz_tool_output, str) and viz_tool_output == absolute_output_filepath:
                logger.info(f"[{self.name}] Visualization successful. Web path: {web_path}")
                return web_path
            logger.warning(f"[{self.name}] Visualization tool did not return the expected path. Output: {viz_tool_output}")
            return None
        except Exception as e_viz:
            logger.error(f"[{self.name}] Exception during visualization call: {e_viz}", exc_info=True)
            return None

    def _get_experimental_data(self, reaction_smiles: str, reactants_smi: List[str], products_smi: List[str]) -> Dict[str, Any]:
        askcos_raw_data = self.reaction_class
        askcos_rank_1 = askcos_raw_data
        orderly_data, source_note = self._search_datasets_for_procedure(reaction_smiles, askcos_rank_1)
        output = {}
        initial_reagents = []
        if orderly_data:
            source_note = source_note or "Orderly Dataset (direct SMILES match)"
            output["reaction_conditions"] = {
                "yield_from_dataset": f"{orderly_data.get('yield_000')}%" if orderly_data.get('yield_000', 'nan').lower() != 'nan' else "N/A",
                "temperature_from_dataset": orderly_data.get('temperature', "N/A"),
                "time_from_dataset": orderly_data.get('rxn_time', "N/A"),
                "atmosphere_llm_or_dataset": orderly_data.get('atmosphere', "N/A"),
            }
            raw_proc = orderly_data.get('procedure_details')
            if raw_proc:
                 llm_proc_data = self.chemical_analysis_logic._parse_llm_json_response(self.chemical_analysis_logic._get_llm_completion_with_fallback("Reformat this chemical procedure into a list of steps. Return JSON: {\"steps\": []}", f"Procedure: {raw_proc[:2000]}"), default_on_error={"steps": [f"Raw from dataset: {raw_proc[:300]}..."] })
                 output["procedure_steps"] = llm_proc_data.get("steps", [f"Raw: {raw_proc[:300]}..."])
            else: output["procedure_steps"] = ["Procedure details not available from dataset."]
            for name in orderly_data.get('agents_list', []): initial_reagents.append({"name": name, "role": "Reagent (from Orderly Dataset)"})
            for name in orderly_data.get('solvents_list', []): initial_reagents.append({"name": name, "role": "Solvent (from Orderly Dataset)"})
        else:
            source_note = "LLM Prediction"
            reactant_names = [self.chemical_analysis_logic.analyze_chemical(r).name or r for r in reactants_smi]
            product_names = [self.chemical_analysis_logic.analyze_chemical(p).name or p for p in products_smi]
            llm_details = self._get_llm_predicted_reaction_details(reaction_smiles, reactant_names, product_names, askcos_rank_1)
            output["reaction_conditions"] = {
                "temperature_from_dataset": llm_details.get("predicted_temperature", "N/A"),
                "time_from_dataset": llm_details.get("predicted_time", "N/A"),
                "atmosphere_llm_or_dataset": llm_details.get("predicted_atmosphere", "N/A"),
                "yield_from_dataset": llm_details.get("predicted_yield_range", "N/A"),
            }
            output["procedure_steps"] = llm_details.get("predicted_procedure_outline", ["LLM could not predict procedure."])
            for name in llm_details.get("predicted_reagents_catalysts", []): initial_reagents.append({"name": name, "role": "Reagent/Catalyst (LLM Predicted)"})
            for name in llm_details.get("predicted_solvents", []): initial_reagents.append({"name": name, "role": "Solvent (LLM Predicted)"})
        output["experimental_data_source_note"] = source_note
        output["initial_reagents_solvents"] = initial_reagents
        output["reaction_name"] = askcos_rank_1.get("reaction_name") if askcos_rank_1 else "N/A"
        if output["reaction_name"] == "N/A":
            local_class_data = self._query_reaction_dataset(reaction_smiles).get("classification_data")
            if local_class_data and local_class_data.get("reaction_name"):
                output["reaction_name"] = local_class_data["reaction_name"]
        return output

    def _search_datasets_for_procedure(self, reaction_smiles: str, askcos_rank_1: Optional[Dict]) -> Tuple[Optional[Dict], Optional[str]]:
        min_certainty_for_guidance = 0.9
        if askcos_rank_1 and askcos_rank_1.get('prediction_certainty', 0.0) >= min_certainty_for_guidance and self.dataset1 is not None and self.dataset2 is not None:
            try:
                # Placeholder for complex guided search logic
                pass
            except Exception as e:
                logger.warning(f"[{self.name} Warn] Error in ASKCOS-guided Orderly search: {e}")
        direct_query_result = self._query_reaction_dataset(reaction_smiles)
        if direct_query_result.get("orderly_data"):
            return direct_query_result["orderly_data"], "Orderly Dataset (direct SMILES match)"
        return None, None

    def _analyze_reagent_properties(self, reagent_info: Dict) -> Dict:
        # Input name can be SMILES or a common name
        input_identifier = reagent_info["name"]
        props: ChemicalPropertiesData = self.chemical_analysis_logic.analyze_chemical(input_identifier)

        final_smiles = props.smiles
        if not final_smiles and ('c' in input_identifier.lower() or '(' in input_identifier or '@' in input_identifier):
            final_smiles = input_identifier
        
        price_source_str, price_confidence_str, _ = self._get_price_source_confidence_for_reaction_component(
            props.supplier_info, self.chemical_analysis_logic.llm_provider
        )
        
        return {
            "name": props.name or input_identifier, # Keep the identified name, or fall back to the input SMILES
            "smiles": final_smiles, # Use our corrected SMILES value
            "role": reagent_info["role"],
            "price_per_unit": props.estimated_price_per_kg,
            "currency": props.price_currency,
            "unit_basis": "kg" if props.estimated_price_per_kg is not None else None,
            "price_source": price_source_str,
            "price_confidence": price_confidence_str
        }

    def _get_safety_analysis(self, context: Dict) -> Dict:
        system_prompt = "You are a chemical safety expert. Provide concise reaction safety assessment and operational notes in JSON."
        user_prompt = f"""For the reaction context:\n{json.dumps(context, indent=2)}\nProvide:\n1. Overall safety assessment (1-2 sentences).\n2. Key operational notes.\nReturn JSON: {{"safety": "...", "notes": "..."}}"""
        llm_response = self.chemical_analysis_logic._get_llm_completion_with_fallback(system_prompt, user_prompt, max_tokens=600)
        return self.chemical_analysis_logic._parse_llm_json_response(llm_response, default_on_error={"safety": "N/A", "notes": "N/A"})

    def _run(self, reaction_smiles: str, original_name: Optional[str] = None) -> Dict[str, Any]:
        logger.info(f"[{self.name} Tool] Starting recipe generation for raw input: {reaction_smiles}")

        # --- 1. PARSING LOGIC (remains the same) ---
        reactants_smi_list: List[str] = []
        reagents_smi_list: List[str] = []
        products_smi_list: List[str] = []
        standard_reaction_smiles_for_helpers: str = ""

        if reaction_smiles.count('>') == 2 and '>>' not in reaction_smiles:
            logger.info(f"[{self.name}] Detected custom 'Reactants>Reagents>Products' format.")
            try:
                reactants_str, reagents_str, products_str = reaction_smiles.split('>', 2)
                reactants_smi_list = [s.strip() for s in reactants_str.split('.') if s.strip()]
                reagents_smi_list = [s.strip() for s in reagents_str.split('.') if s.strip()]
                products_smi_list = [s.strip() for s in products_str.split('.') if s.strip()]
                
                lhs_parts = []
                if reactants_str: lhs_parts.append(reactants_str)
                if reagents_str: lhs_parts.append(reagents_str)
                all_lhs_str = ".".join(lhs_parts)
                standard_reaction_smiles_for_helpers = f"{all_lhs_str}>>{products_str}"
                logger.info(f"[{self.name}] Correctly standardized to: {standard_reaction_smiles_for_helpers}")
            except ValueError as e:
                raise ValueError(f"Invalid custom R>A>P format. Input: '{reaction_smiles}'. Error: {e}")
        elif ">>" in reaction_smiles:
            logger.info(f"[{self.name}] Detected standard 'Reactants.Agents>>Products' format.")
            standard_reaction_smiles_for_helpers = reaction_smiles
            # ... (standard parsing logic remains the same) ...
            main_parts = reaction_smiles.split(">>", 1)
            lhs_str, products_str = main_parts[0].strip(), main_parts[1].strip()
            reactants_smi_list = [s.strip() for s in lhs_str.split('.') if s.strip()]
            reagents_smi_list = []
            products_smi_list = [s.strip() for s in products_str.split('.') if s.strip()]
        else:
            raise ValueError("Invalid reaction SMILES format.")

        if not reactants_smi_list or not products_smi_list:
            raise ValueError("Parsed SMILES resulted in empty reactants or products.")

        # --- 2. COMPONENT IDENTIFICATION (remains the same) ---
        logger.info(f"[{self.name}] Identifying components...")
        reactants_identified = [self.chemical_analysis_logic.analyze_chemical(r).name or r for r in reactants_smi_list]
        reagents_identified = [self.chemical_analysis_logic.analyze_chemical(r).name or r for r in reagents_smi_list]
        products_identified = [self.chemical_analysis_logic.analyze_chemical(p).name or p for p in products_smi_list]
        
        with ThreadPoolExecutor(max_workers=15) as executor:
            # --- 3. PARALLEL TASK EXECUTION ---
            
            # Task 1: Get general experimental data (conditions, procedure) from LLM or datasets.
            # This task no longer influences the final reagent list.
            future_exp_data = executor.submit(self._get_experimental_data, standard_reaction_smiles_for_helpers, reactants_smi_list, products_smi_list)
            
            # Task 2: Get safety analysis.
            safety_context = {
                "reactants": reactants_identified,
                "reagents": reagents_identified, # Use identified names for better LLM context
                "products": products_identified,
            }
            future_safety = executor.submit(self._get_safety_analysis, safety_context)

            # Task 3: Analyze and price ONLY the reagents provided in the input string.
            future_input_reagents = []
            if reagents_smi_list:
                logger.info(f"[{self.name}] Submitting {len(reagents_smi_list)} pricing/analysis tasks for INPUT reagents ONLY.")
                for smi in reagents_smi_list:
                    # The role is explicitly 'Reagent (from input)' to be clear.
                    future = executor.submit(self._analyze_reagent_properties, {"name": smi, "role": "Reagent (from input)"})
                    future_input_reagents.append(future)

            # --- 4. COLLECT RESULTS ---
            
            # Collect priced reagents from input. This is our definitive list.
            final_reagents_and_solvents = []
            for future in as_completed(future_input_reagents):
                try:
                    final_reagents_and_solvents.append(future.result())
                except Exception as e:
                    logger.error(f"[{self.name}] A reagent analysis task failed: {e}", exc_info=True)
            
            # Collect experimental data
            try:
                experimental_data = future_exp_data.result(timeout=300)
                # We will use conditions and procedure from here, but IGNORE its reagent suggestions.
            except Exception as e:
                logger.error(f"[{self.name}] Experimental data task failed: {e}", exc_info=True)
                experimental_data = {}

            # Collect safety data
            try:
                safety_data = future_safety.result(timeout=300)
            except Exception as e:
                logger.error(f"[{self.name}] Safety analysis task failed: {e}", exc_info=True)
                safety_data = {}
            
            logger.info(f"[{self.name}] All parallel tasks completed.")
            
            # --- 5. CONSTRUCT FINAL JSON OUTPUT ---
            final_json_output = {
                "reaction_smiles_original_input": reaction_smiles,
                "reaction_smiles_standardized_for_processing": standard_reaction_smiles_for_helpers,
                "original_name_context": original_name,
                "experimental_data_source_note": experimental_data.get("experimental_data_source_note", "Not Found"),
                "reaction_details": {
                    "reactants_identified": reactants_identified,
                    "reagents_identified": reagents_identified,
                    "products_identified": products_identified,
                    "reaction_name": experimental_data.get("reaction_name", "N/A"),
                },
                "reaction_conditions": experimental_data.get("reaction_conditions", {}),
                "procedure_steps": experimental_data.get("procedure_steps", []),
                # This list is now built EXCLUSIVELY from the input reagents.
                "reagents_and_solvents": final_reagents_and_solvents,
                "safety_and_notes": safety_data
            }
            
            logger.info(f"[{self.name}] Recipe generation complete.")
            return final_json_output