from rdkit import Chem
import re
import os
from typing import Dict, Any, List # Added List for return type hint

from .base import BaseTool
from .registry import register_tool
from ..config.settings import Settings

@register_tool
class FuncGroupTool(BaseTool):
    name = "FuncGroups"
    description = "Identifies functional groups in a molecule or reaction SMILES using SMARTS patterns."

    def __init__(self, settings: Settings, **kwargs):
        super().__init__(settings=settings)
        smarts_file_path = self.settings.OPEN_BABEL_SMARTS_FILE
        self.smarts_patterns: Dict[str, Chem.Mol] = {} # Ensure type
        if not os.path.exists(smarts_file_path):
            print(f"[{self.name}] CRITICAL ERROR: SMARTS file not found at {smarts_file_path}. This tool will not function correctly.")
        else:
            self.smarts_patterns = self._load_functional_groups_from_file(smarts_file_path)
            # print(f"[{self.name}] Loaded {len(self.smarts_patterns)} SMARTS patterns from {smarts_file_path}")

    def _load_functional_groups_from_file(self, smarts_filepath: str) -> Dict[str, Chem.Mol]:
        func_groups: Dict[str, Chem.Mol] = {}
        try:
            with open(smarts_filepath, 'r', encoding='utf-8') as f:
                for line_num, line in enumerate(f, 1):
                    line = line.strip()
                    if not line or line.startswith("#"):
                        continue
                    try:
                        name, smarts = line.split(":", 1)
                        name = name.strip()
                        smarts = smarts.strip()
                        mol_from_smarts = Chem.MolFromSmarts(smarts)
                        if mol_from_smarts:
                            func_groups[name] = mol_from_smarts
                        else:
                            print(f"[{self.name} SMARTS Load Warning] Line {line_num}: Could not parse SMARTS for '{name}': '{smarts}'")
                    except ValueError:
                        print(f"[{self.name} SMARTS Load Warning] Line {line_num}: Malformed (expected 'name:smarts'): {line}")
                    except Exception as e_parse:
                        print(f"[{self.name} SMARTS Load Error] Line {line_num}: Error parsing '{line}': {e_parse}")
        except FileNotFoundError:
            print(f"[{self.name} SMARTS Load Error] SMARTS file not found: {smarts_filepath}")
        except Exception as e_file:
            print(f"[{self.name} SMARTS Load Error] Could not read SMARTS file {smarts_filepath}: {e_file}")
        return func_groups
    
    def _identify_functional_groups(self, mol: Chem.Mol) -> List[str]:
        if not mol: return []
        groups_found = []
        for name, pattern_mol in self.smarts_patterns.items():
            if pattern_mol is None: continue # Skip if pattern failed to load
            if mol.HasSubstructMatch(pattern_mol):
                groups_found.append(name)
        return groups_found
    
    def _analyze_molecule(self, smiles: str) -> Dict[str, Any]:
        try:
            mol = Chem.MolFromSmiles(smiles)
            if not mol:
                return {"error": f"Invalid SMILES for molecule: '{smiles}'"}
            groups = self._identify_functional_groups(mol)
            return {"smiles": smiles, "functional_groups": groups, "type": "molecule_analysis"}
        except Exception as e:
            return {"error": f"Error analyzing molecule '{smiles}': {str(e)}"}

    def _analyze_reaction(self, reaction_smiles: str) -> Dict[str, Any]:
        try:
            if ">>" not in reaction_smiles:
                return {"error": "Invalid reaction SMILES. Must contain '>>'."}
            reactants_str, products_str = reaction_smiles.split(">>", 1)
            
            reactants_smiles_list = [r.strip() for r in reactants_str.split('.') if r.strip()]
            products_smiles_list = [p.strip() for p in products_str.split('.') if p.strip()]

            reactant_analyses = []
            for r_smi in reactants_smiles_list:
                mol_analysis_result = self._analyze_molecule(r_smi)
                if "error" in mol_analysis_result:
                    return {"error": f"Invalid reactant SMILES '{r_smi}' in reaction: {mol_analysis_result['error']}"}
                reactant_analyses.append(mol_analysis_result)
            
            product_analyses = []
            for p_smi in products_smiles_list:
                mol_analysis_result = self._analyze_molecule(p_smi)
                if "error" in mol_analysis_result:
                    return {"error": f"Invalid product SMILES '{p_smi}' in reaction: {mol_analysis_result['error']}"}
                product_analyses.append(mol_analysis_result)
            
            return {
                "reaction_smiles": reaction_smiles,
                "reactants_analysis": reactant_analyses,
                "products_analysis": product_analyses,
                "transformation_summary": self._summarize_transformation_details(reactant_analyses, product_analyses),
                "type": "reaction_analysis"
            }
        except Exception as e:
            return {"error": f"Error analyzing reaction '{reaction_smiles}': {str(e)}"}

    def _summarize_transformation_details(self, reactant_analyses_list: List[Dict], product_analyses_list: List[Dict]) -> Dict[str, List[str]]:
        # Changed name for clarity
        all_reactant_fgs = set()
        for analysis_item in reactant_analyses_list:
            if "functional_groups" in analysis_item:
                all_reactant_fgs.update(analysis_item["functional_groups"])
        
        all_product_fgs = set()
        for analysis_item in product_analyses_list:
            if "functional_groups" in analysis_item:
                all_product_fgs.update(analysis_item["functional_groups"])
        
        return {
            "groups_lost": list(all_reactant_fgs - all_product_fgs),
            "groups_gained": list(all_product_fgs - all_reactant_fgs),
            "groups_retained": list(all_reactant_fgs.intersection(all_product_fgs))
        }
    
    def _run(self, query_smiles: str) -> Dict[str, Any]:
        print(f"[{self.name} Tool] _run called with query: {query_smiles}")
        if not self.smarts_patterns:
            return {"error": "SMARTS patterns not loaded for functional group identification."}

        if ">>" in query_smiles:
            return self._analyze_reaction(query_smiles)
        else:
            return self._analyze_molecule(query_smiles)