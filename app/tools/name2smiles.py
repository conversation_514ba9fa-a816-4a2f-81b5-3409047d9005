import re
import logging
import urllib.parse
from typing import Optional, Tuple, List
import requests
from rdkit import Chem
import openai

from .base import BaseTool
from .registry import register_tool
from ..config.settings import Settings

class IupacToSmilesConverter:
    _openai_client: Optional[openai.AzureOpenAI] = None
    # --- Constants for NameToSMILES preprocessing ---
    SALT_SUFFIXES = {
        'hydrochloride', 'hcl', 'hydrobromide', 'hbr', 'sulfate', 'sulphate',
        'phosphate', 'acetate', 'citrate', 'tartrate', 'maleate', 'fumarate',
        'succinate', 'oxalate', 'lactate', 'benzoate', 'salicylate', 'tosylate',
        'mesylate', 'besylate', 'triflate', 'sodium salt', 'potassium salt',
        'calcium salt', 'magnesium salt', 'zinc salt', 'lithium salt',
        'ammonium salt', 'trifluoroacetate', 'formate', 'propionate'
    }
    HYDRATE_SUFFIXES = {
        'monohydrate', 'dihydrate', 'trihydrate', 'tetrahydrate', 'pentahydrate',
        'hexahydrate', 'heptahydrate', 'octahydrate', 'nonahydrate', 'decahydrate',
        'hydrate', 'anhydrous', 'hemihydrate', 'sesquihydrate'
    }
    SOLVATE_SUFFIXES = {
        'solvate', 'ethanolate', 'methanolate', 'isopropanolate', 'dmso solvate',
        'acetone solvate', 'ethyl acetate solvate', 'toluene solvate'
    }
    STEREOCHEMISTRY_PATTERNS = [
        r'\([RS]\)-', r'\([EZ]\)-', r'cis-', r'trans-', r'α-', r'β-', r'alpha-', r'beta-',
        r'\(±\)-', r'\(\+\)-', r'\(-\)-', r'd-', r'l-', r'dl-', r'rac-', r'rel-',
        r'\([αβ]\)-', r'\(alpha\)-', r'\(beta\)-'
    ]
    COMMON_ABBREVIATIONS = {
        'me': 'methyl', 'et': 'ethyl', 'pr': 'propyl', 'bu': 'butyl', 'ph': 'phenyl',
        'ac': 'acetyl', 'boc': 'tert-butoxycarbonyl', 'cbz': 'benzyloxycarbonyl',
        'ts': 'tosyl', 'ms': 'mesyl', 'tbs': 'tert-butyldimethylsilyl'
    }

    def convert(self, query: str) -> Tuple[bool, str]:
        try:
            if not query or not isinstance(query, str):
                return False, "Input query must be a non-empty string."

            if self._is_likely_smiles(query):
                return True, query

            name_variants = self._generate_name_variants(query)

            apis = [
                ('OPSIN', self._try_opsin),
                ('CIR (Cactus)', self._try_cir),
                ('PubChem REST', self._try_pubchem),
            ]

            for variant in name_variants:
                for api_name, api_func in apis:
                    try:
                        success, result = api_func(variant)
                        if success:
                            return True, result
                    except:
                        continue
            
            # --- LLM Fallback ---
            # Try LLM with the original query first, then potentially the most "canonical" variant
            queries_for_llm = [query] + ([name_variants[0]] if name_variants and name_variants[0] != query else [])
            
            for llm_query_variant in queries_for_llm:
                llm_success, llm_result = self._try_llm_for_smiles(llm_query_variant)
                if llm_success:
                    return True, llm_result
                
            return False, "Could not convert name to canonical smiles"

        except Exception as e:
            return False, "Could not convert name to canonical smiles"

    def _is_likely_smiles(self, query: str) -> bool:
        """Check if the input looks like a SMILES string"""
        
        if not query or not isinstance(query, str) or not query.strip():
            return False

        if query.startswith("InChI="):
            return False
            
        try:
            mol = Chem.MolFromSmiles(query, sanitize=True)
            is_smiles = mol is not None and mol.GetNumAtoms() > 0
            return is_smiles
        except:
            pass

        # --- Non-RDKit Fallback Logic ---
        # Early exclusion: This was the specific rule for NonExistentCompoundXYZ123ABC
        # Check this BEFORE the strong indicators to prevent false positives
        is_long_mixed_alnum_no_strong_indicators = (
            len(query) > 7 and 
            any(c.islower() for c in query) and 
            any(c.isupper() for c in query) and 
            query.isalnum() and 
            not any(char in "()[]=#@\\/" for char in query) and  # No obvious SMILES chars
            not re.search(r'^[A-Za-z]+\d+$', query) and  # Not simple letter+number pattern
            not re.search(r'^\d+[A-Za-z]+', query)  # Not number+letter pattern
        )
        if is_long_mixed_alnum_no_strong_indicators:
            return False

        # 1. Check for strong, unambiguous SMILES features.
        # Fixed the problematic [a-zA-Z]\d pattern to be more specific
        strong_smiles_indicators_regex = re.compile(
            r"(@{1,2}|#|=|"
            r"\[[A-Z][a-z]?[@H\d\+\-\:\.\(\)]+\]|"
            r"(?<![A-Za-z])[A-Za-z]\d(?![A-Za-z])|"  # Ring closures: letter+digit not surrounded by letters
            r"\/(?![=\s\/])|"
            r"\\(?![=\s\\])"
            r")"
        )
        if strong_smiles_indicators_regex.search(query):
            return True

        # 2. Handle double bonds (=) carefully
        if '=' in query:
            if re.fullmatch(r"[a-zA-Z0-9\s\-]+\s*=\s*[a-zA-Z0-9\s\-]+", query):
                return False
            if re.fullmatch(r"[CHNOPSFClBrIcnops\d\.\(\)=%@#\+\-\[\]\\/\:]+", query):
                if not (' ' in query and any(w.isalpha() and len(w)>3 for w in query.split('='))):
                    return True

        # 3. Simple aliphatic chains or single lowercase aromatic rings
        if ' ' not in query:
            if query.islower() and query.isalnum() and any(c.isalpha() for c in query):
                if query not in ['dna', 'rna', 'atp', 'gmp', 'nad', 'fad', 'coa', 'water', 'acid', 'salt', 'form', 'type', 'base', 'aspirin', 'caffeine'] and len(query) > 1 :
                    return True
            if query.isalpha() and query.isupper() and 2 <= len(query) <= 6:
                if all(atom_char in "CNOPS" for atom_char in query):
                    if query not in ["CO", "NO", "HF", "HCL", "SO2", "CO2", "NO2", "CH4", "ATP", "DNA", "RNA"]:
                        return True

        # 4. Digit + Letter without Ring Closure or Bracket Atom
        if any(c.isdigit() for c in query) and any(c.isalpha() for c in query):
            if not re.search(r"(?<![A-Za-z])[A-Za-z]\d(?![A-Za-z])", query):  # Fixed pattern here too
                if not re.search(r"\[[A-Z][a-z]?[@H\d\+\-\:\.\(\)]*\]", query): 
                    if query.upper() in ["H2O", "CH4", "NH3", "SO2", "CO2"]:
                        return False
                    if query[0].isalpha():
                        if query.isalnum() and not any(c in "()[]=#@\\/" for c in query) : 
                            return False

        # 5. Default to NOT being a SMILES if it looks more like a typical chemical name/identifier.
        if ' ' not in query and query.istitle() and len(query) > 2 and query.isalpha():
            return False
        if ' ' in query and any(word.lower() in ['acid', 'salt', 'hydrate', 'compound', 'oxide', 'chloride', 'solution', 'mixture'] for word in query.split()):
            return False

        return False

    def _generate_name_variants(self, name: str) -> List[str]:
        variants = set()
        original_name = name.strip()
        if not original_name: return []
        
        variants.add(original_name)
        variants.add(original_name.lower())

        cleaned_name = self._remove_salts_hydrates(original_name)
        if cleaned_name != original_name:
            variants.add(cleaned_name)
            variants.add(cleaned_name.lower())

        no_stereo = self._strip_stereochemistry(original_name)
        if no_stereo != original_name:
            variants.add(no_stereo)
            variants.add(no_stereo.lower())
        
        if cleaned_name != original_name:
            cleaned_no_stereo = self._strip_stereochemistry(cleaned_name)
            if cleaned_no_stereo not in variants:
                variants.add(cleaned_no_stereo)
                variants.add(cleaned_no_stereo.lower())
        
        expanded_original = self._expand_abbreviations(original_name)
        if expanded_original != original_name:
            variants.add(expanded_original)
            variants.add(expanded_original.lower())
        
        if cleaned_name != original_name:
            expanded_cleaned = self._expand_abbreviations(cleaned_name)
            if expanded_cleaned != cleaned_name and expanded_cleaned not in variants:
                 variants.add(expanded_cleaned)
                 variants.add(expanded_cleaned.lower())

        return sorted([v for v in variants if v.strip()], key=len, reverse=True)

    def _remove_salts_hydrates(self, name: str) -> str:
        """Remove salt, hydrate, and solvate suffixes from chemical names."""
        name_lower = name.lower()

        # Combine all suffixes to remove
        all_suffixes = self.SALT_SUFFIXES | self.HYDRATE_SUFFIXES | self.SOLVATE_SUFFIXES

        for suffix in sorted(all_suffixes, key=len, reverse=True):  # Try longer suffixes first
            escaped_suffix = re.escape(suffix)
            # Patterns to match suffixes, allowing for flexible spacing around commas
            patterns_config = [
                # (regex_pattern_string, is_end_of_line_suffix)
                (rf"\s+{escaped_suffix}$", True),                     # e.g., " hydrochloride"
                (rf",\s*{escaped_suffix}$", True),                    # e.g., ", hydrochloride" or ",hydrochloride"
                (rf"[·•]\s*{escaped_suffix}$", True),                 # e.g., "·hydrochloride" or "• hydrochloride"
                # For middle removal, ensure suffix is a whole word/unit
                (rf"\s+{escaped_suffix}(\s+|,|\.|·|•|$)", False), # Matches " suffix " or " suffix," etc. The lookahead helps define the end.
            ]
            
            # The original patterns were simpler and also effective for common cases:
            # f" {suffix}$", f", {suffix}$", f" {suffix} ", f", {suffix} ", f"·{suffix}$", f"•{suffix}$"
            # We'll use slightly more robust regexes based on the above config:

            # Simplified patterns for clarity, similar to original but with re.escape and some \s*
            # This list order can matter if a suffix could be matched by multiple patterns.
            # However, the sorted suffixes list (longest first) is the primary guard against wrong suffix removal.
            current_patterns = [
                (rf"\s+{escaped_suffix}$", True),                      # " hydrochloride" at end
                (rf",\s*{escaped_suffix}$", True),                     # ",hydrochloride" or ", hydrochloride" at end
                (rf"[·•]\s*{escaped_suffix}$", True),                  # "·hydrate" or "• hydrate" at end
                (rf"\s+{escaped_suffix}\s+", False),                   # " hydrochloride " in middle
                (rf",\s*{escaped_suffix}\s+", False)                   # ",hydrochloride " or ", hydrochloride " in middle
            ]


            for pattern_str, is_end_suffix in current_patterns:
                # Search in the lowercased name
                match = re.search(pattern_str, name_lower)
                if match:
                    # Use match.start() and match.end() on the original name string
                    # to remove the suffix while preserving case.
                    original_match_start = match.start()
                    original_match_end = match.end() # This is the end of the full pattern match in name_lower

                    if is_end_suffix:
                        # If it's an end suffix, take everything before the matched part (which includes leading space/comma)
                        # name[:original_match_start] is the part before " suffix" or ",suffix"
                        return name[:original_match_start].strip() 
                    else:
                        # For middle suffixes like " name SUFFIX rest"
                        # The pattern matched " SUFFIX " (including surrounding spaces/commas)
                        # We want to replace " SUFFIX " with a single space.
                        name_part_before = name[:original_match_start]
                        name_part_after = name[original_match_end:]
                        return (name_part_before.rstrip(' ,') + " " + name_part_after.lstrip(' ,')).strip()
        return name

    def _strip_stereochemistry(self, name: str) -> str:
        # (Implementation from previous simplified version)
        result = name
        for pattern in self.STEREOCHEMISTRY_PATTERNS:
            result = re.sub(pattern, '', result, flags=re.IGNORECASE)
        result = re.sub(r"^\s*[-–—]\s*|\s*[-–—]\s*$", "", result.strip())
        return re.sub(r"\s\s+", " ", result).strip()

    def _expand_abbreviations(self, name: str) -> str:
        # (Implementation from previous simplified version)
        result = name
        for abbr, expansion in self.COMMON_ABBREVIATIONS.items():
            pattern = re.compile(r"\b" + re.escape(abbr) + r"\b", re.IGNORECASE)
            result = pattern.sub(expansion, result)
        return result.strip()

    # --- API Call Implementations (OPSIN, CIR, PubChem) ---
    # (Implementations from previous simplified version - keeping them concise)
    @staticmethod
    def _try_opsin(query: str, timeout: int = 5) -> Tuple[bool, str]:
        try:
            url = f"https://opsin.ch.cam.ac.uk/opsin/{urllib.parse.quote(query)}.json"
            response = requests.get(url, timeout=timeout)
            if response.status_code == 404: return False, f"OPSIN: Name not resolvable (404)."
            response.raise_for_status(); data = response.json()
            if data.get("status") == "SUCCESS" and data.get("smiles"): return True, data["smiles"]
            return False, f"OPSIN: {data.get('message', 'Conversion failed')}"
        except requests.exceptions.Timeout: return False, "OPSIN: Request timed out."
        except Exception: return False, "OPSIN: Request error or invalid JSON."

    @staticmethod
    def _try_cir(query: str, timeout: int = 5) -> Tuple[bool, str]:
        try:
            url = f"https://cactus.nci.nih.gov/chemical/structure/{urllib.parse.quote(query)}/smiles"
            response = requests.get(url, timeout=timeout, allow_redirects=True)
            if response.status_code == 404: return False, f"CIR: Name not resolvable (404)."
            response.raise_for_status(); smiles = response.text.strip()
            if smiles and "Page not found" not in smiles and "<html" not in smiles.lower(): return True, smiles
            return False, "CIR: No valid SMILES returned."
        except requests.exceptions.Timeout: return False, "CIR: Request timed out."
        except Exception: return False, "CIR: Request error."

    @staticmethod
    def _try_pubchem(query: str, timeout: int = 5) -> Tuple[bool, str]:
        try:
            encoded_query = urllib.parse.quote(query)
            url = f"https://pubchem.ncbi.nlm.nih.gov/rest/pug/compound/name/{encoded_query}/property/IsomericSMILES,CanonicalSMILES/JSON"
            response = requests.get(url, timeout=timeout)
            if response.status_code == 404:
                search_url = f"https://pubchem.ncbi.nlm.nih.gov/rest/pug/compound/name/{encoded_query}/cids/JSON"
                search_resp = requests.get(search_url, timeout=timeout)
                if search_resp.status_code == 404: return False, f"PubChem: Name not found (404)."
                search_resp.raise_for_status(); search_data = search_resp.json()
                cid = search_data.get("IdentifierList", {}).get("CID", [None])[0]
                if not cid: return False, "PubChem: No CID found."
                prop_url = f"https://pubchem.ncbi.nlm.nih.gov/rest/pug/compound/cid/{cid}/property/IsomericSMILES,CanonicalSMILES/JSON"
                response = requests.get(prop_url, timeout=timeout)
            response.raise_for_status(); prop_data = response.json()
            properties = prop_data.get("PropertyTable", {}).get("Properties", [{}])[0]
            smiles = properties.get("IsomericSMILES") or properties.get("CanonicalSMILES")
            if smiles: return True, smiles
            return False, "PubChem: No SMILES in properties."
        except requests.exceptions.Timeout: return False, "PubChem: Request timed out."
        except Exception: return False, "PubChem: Request error or invalid JSON/structure."

    def _try_llm_for_smiles(self, query: str, timeout: int = 15) -> Tuple[bool, str]:
        try:
            prompt = (
                f"What is the Canonical SMILES string for the chemical compound named '{query}'? "
                "Respond with only the SMILES string. If you cannot determine it or are unsure, "
                "respond with the exact phrase 'UNKNOWN'."
            )
            response = self._openai_client.chat.completions.create(
                model="gpt-4o", # Or "gpt-3.5-turbo" for faster/cheaper, potentially less accurate
                messages=[{"role": "user", "content": prompt}],
                temperature=0.0, # For deterministic output
                max_tokens=150, # SMILES are usually not extremely long
                timeout=timeout,
            )
            llm_smiles = response.choices[0].message.content.strip()

            if not llm_smiles or llm_smiles == "UNKNOWN" or "unknown" in llm_smiles.lower():
                return False, "LLM could not determine SMILES or response was UNKNOWN."
            
            # Basic check if it looks like SMILES. More robust validation (e.g. RDKit) could be added
            # but adds complexity and dependency if RDKit is not strictly for this.
            if self._is_likely_smiles(llm_smiles):
                return True, llm_smiles
            else:
                # Attempt to clean up common LLM verbiage if any (e.g. "The SMILES string is: CCC")
                # This is a heuristic and might need refinement
                match = re.search(r'([\[\]\(\)=#@\\/\.\+\-\w\d]+)', llm_smiles) # A more permissive SMILES-like pattern
                if match and self._is_likely_smiles(match.group(1)):
                    return True, match.group(1)
                return False, f"LLM response '{llm_smiles[:50]}...' does not appear to be a valid SMILES string."

        except openai.APIError as e:
            logging.error(f"OpenAI API error for NameToSMILES: {e}")
            return False, f"OpenAI API error: {type(e).__name__}"
        except Exception as e:
            logging.error(f"Error during LLM SMILES conversion for '{query}': {e}")
            return False, f"LLM conversion error: {type(e).__name__}"


@register_tool
class NameToSmilesTool(BaseTool):
    name = "NameToSMILES"
    description = "Converts chemical names to SMILES using multiple services and fallback options"

    def __init__(self, settings: Settings, **kwargs):
        super().__init__(settings=settings)
        self.converter = IupacToSmilesConverter()
        self.converter._openai_client = openai.AzureOpenAI(
        )

    def _run(self, chemical_name: str) -> str:
        try:
            if not chemical_name or not isinstance(chemical_name, str):
                return "Error: Invalid input. Chemical name must be a non-empty string."
                
            logging.info(f"[{self.name} Tool] _run called with: {chemical_name}")
            
            # Try conversion using the enhanced converter
            success, result = self.converter.convert(chemical_name)
            logging.info(f"[{self.name} Tool] Conversion result: {result} (Success: {success})")
            if success:
                return f"SMILES: {result}"
            else:
                # If conversion failed, return the error message
                return f"Error: {result}"

        except Exception as e:
            logging.error(f"Unexpected error in NameToSMILES tool: {str(e)}")
            return f"Error: An unexpected error occurred while processing '{chemical_name}'"