from pydantic import BaseModel, Field # Import Field for descriptions
from typing import Dict, Optional, Any, Union, List # Changed analysis back to Any for flexibility

class GeneralQueryRequest(BaseModel):
    query: str = Field(..., description="The user's query for the ChemCopilot system.")
    smiles: str = Field(..., description="smiles")
    reaction_class: Dict[str, Dict[str, Any]] = Field(..., description="Classification of the reaction with nested structure")
    session_id: Optional[str] = Field(None, description="Optional session ID for maintaining context or MOI.")
    original_name_for_saving: Optional[str] = Field(None, description="Original name if query used SMILES, for filename context.")
    clear_moi_before_query: bool = Field(False, description="Flag to clear MOI before processing this query.")

class AnalysisResponse(BaseModel):
    analysis: Union[str, Dict[str, Any], List[Dict[str, Any]], None] = Field(None, description="The main analysis result, can be text, JSON object, or list of objects.")# Changed to Any
    visualization_path: Optional[str] = Field(None, description="Web path to any generated visualization image.")
    processed_smiles: Optional[str] = Field(None, description="The SMILES string that was primarily processed or identified.")
    error: Optional[str] = Field(None, description="Error message if the query processing failed.")
    query_context_for_filename: Optional[str] = Field(None, description="Contextual tag used for naming saved analysis files.")
    current_moi_name: Optional[str] = Field(None, description="Name of the current Molecule of Interest after processing.")
    current_moi_smiles: Optional[str] = Field(None, description="SMILES of the current Molecule of Interest after processing.")
    session_id: Optional[str] = Field(None, description="The session ID used for this interaction.") # Made optional to match typical response behavior