from fastapi import FastAPI, HTTPException, Depends, Query # Added Query
from fastapi.staticfiles import StaticFiles
from typing import Dict, Any, Optional
import os
import traceback
import logging
import sys
import time # Added for ping endpoint

api_logger = logging.getLogger("app.api.main")
api_logger.setLevel(logging.INFO)
# Ensure logs are outputting if not already configured by root_agent's basicConfig
if not api_logger.hasHandlers() and not logging.getLogger().hasHandlers():
    # Configure a basic handler if no handlers are found anywhere
    handler = logging.StreamHandler(sys.stdout)
    formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
    handler.setFormatter(formatter)
    api_logger.addHandler(handler)
    api_logger.propagate = False # Avoid double logging if root logger also gets a handler later
elif not api_logger.hasHandlers() and logging.getLogger().hasHandlers():
    # If root logger has handlers, but api_logger doesn't, let it propagate to root.
    api_logger.propagate = True


from app.config.settings import get_settings, Settings
from app.agents.registry import load_all_agents, get_all_agent_names, get_agent_instance # get_agent_instance is imported but not used in this file
from app.tools.registry import load_all_tools, get_all_tool_names
from app.orchestration.root_agent import RootAgentOrchestrator
from app.api.schemas.general_models import GeneralQueryRequest, AnalysisResponse # Ensure this path is correct

app = FastAPI(
    title="AGENT-HUB ChemCopilot",
    description="A multi-agent system for chemical analysis and operations.",
    version="1.0.0"
)


orchestrator: Optional[RootAgentOrchestrator] = None


@app.on_event("startup")
async def startup_event():
    global orchestrator
    api_logger.info("AGENT-HUB ChemCopilot application startup...")

    try:
        current_settings = get_settings()
    except RuntimeError as rte_settings:
        api_logger.error(f"CRITICAL STARTUP ERROR: Failed to load settings. Error: {rte_settings}")
        api_logger.error(traceback.format_exc())
        # Potentially exit or prevent app from fully starting if settings are critical
        # For now, we'll let it continue to see if other parts can initialize,
        # but many subsequent operations will likely fail.
        # Consider `raise` here if settings are absolutely mandatory for any operation.
        # Fallback to a default Settings object or None to signal failure
        current_settings = None # Or some default/empty Settings if that makes sense
        # Ensure orchestrator remains None if settings fail
        orchestrator = None
        # Skip further initializations that depend on settings
        api_logger.warning("Proceeding with startup despite settings load failure. Functionality will be limited.")
        # return # Optionally, stop further startup processing

    if current_settings: # Only proceed if settings loaded
        api_logger.info(f"OpenAI API Key: {'SET' if current_settings.OPENAI_API_KEY and 'YOUR_OPENAI_KEY' not in current_settings.OPENAI_API_KEY else 'NOT SET or Placeholder'}")
        api_logger.info(f"Perplexity API Key: {'SET' if current_settings.PERPLEXITY_API_KEY and 'your_perplexity_api_key_here' not in current_settings.PERPLEXITY_API_KEY else 'NOT SET or Placeholder'}")
        api_logger.info(f"Default LLM Provider: {current_settings.DEFAULT_LLM_PROVIDER}")

        api_logger.info("Loading all tools...")
        try:
            load_all_tools()
            api_logger.info(f"Registered Tools: {get_all_tool_names()}")
        except Exception as e_tools:
            api_logger.error(f"STARTUP ERROR: Failed to load tools. Error: {e_tools}")
            api_logger.error(traceback.format_exc())

        api_logger.info("Loading all agents...")
        try:
            load_all_agents()
            api_logger.info(f"Registered Agents: {get_all_agent_names()}")
        except Exception as e_agents:
            api_logger.error(f"STARTUP ERROR: Failed to load agents. Error: {e_agents}")
            api_logger.error(traceback.format_exc())

        api_logger.info("Attempting to initialize RootAgentOrchestrator...")
        try:
            orchestrator = RootAgentOrchestrator() # This is where it might fail
            api_logger.info("RootAgentOrchestrator initialized successfully.")
        except AttributeError as ae:
            api_logger.error(f"STARTUP ERROR (AttributeError): A required setting for RootAgentOrchestrator might be missing or misnamed. Error: {ae}")
            api_logger.error(traceback.format_exc())
            orchestrator = None
        except ImportError as ie:
            api_logger.error(f"STARTUP ERROR (ImportError): A module required by the orchestrator or its components could not be imported. Error: {ie}")
            api_logger.error(traceback.format_exc())
            orchestrator = None
        except RuntimeError as rte:
            api_logger.error(f"STARTUP ERROR (RuntimeError): Critical settings initialization failed within RootAgentOrchestrator. Error: {rte}")
            api_logger.error(traceback.format_exc())
            orchestrator = None
        except Exception as e:
            api_logger.error(f"FATAL ERROR during RootAgentOrchestrator initialization in startup: {e}")
            api_logger.error(traceback.format_exc())
            orchestrator = None # Ensure orchestrator is None on generic exception

        # Static files mounting (only if current_settings are available)
        static_dir_for_visualizations = current_settings.VISUALIZATION_DIR_ABS
        if not os.path.exists(static_dir_for_visualizations):
            try:
                os.makedirs(static_dir_for_visualizations, exist_ok=True)
                api_logger.info(f"Created static directory for visualizations: {static_dir_for_visualizations}")
            except Exception as e_dir:
                api_logger.error(f"Error creating visualization directory {static_dir_for_visualizations}: {e_dir}")
        
        # Check again if dir exists before mounting, in case creation failed silently or permissions issue
        if os.path.exists(static_dir_for_visualizations) and os.path.isdir(static_dir_for_visualizations):
            try:
                app.mount(
                    current_settings.VISUALIZATION_WEB_BASE_PATH,
                    StaticFiles(directory=current_settings.VISUALIZATION_DIR_ABS),
                    name="visualizations"
                )
                api_logger.info(f"Serving visualizations from: {current_settings.VISUALIZATION_DIR_ABS} at {current_settings.VISUALIZATION_WEB_BASE_PATH}")
            except Exception as e_mount: # More specific: RuntimeError if path/name conflict
                api_logger.error(f"Error mounting static files: {e_mount}")
                api_logger.error(f"  Attempted to mount dir: {current_settings.VISUALIZATION_DIR_ABS} at path {current_settings.VISUALIZATION_WEB_BASE_PATH}")
                api_logger.error(f"  VISUALIZATION_DIR_ABS exists: {os.path.exists(current_settings.VISUALIZATION_DIR_ABS)}")
        else:
            api_logger.error(f"Static directory for visualizations not found or not a directory: {static_dir_for_visualizations}. Cannot mount.")

    else: # If current_settings failed to load
        api_logger.error("Skipping tool, agent, orchestrator, and static file initialization due to settings load failure.")

    api_logger.info("Application startup sequence complete.")


@app.get("/ping")
async def ping():
    """A simple endpoint to check if the API is responsive."""
    api_logger.info("PING endpoint was hit!")
    return {"message": "pong", "timestamp": time.time()}


@app.post("/query", response_model=AnalysisResponse, response_model_exclude_none=True)
async def handle_query(request_data: GeneralQueryRequest, settings: Settings = Depends(get_settings)):
    """
    Receives a chemical query and routes it through the RootAgentOrchestrator
    to determine the appropriate action and generate an analysis.
    """
    global orchestrator

    api_logger.info(f"HANDLE_QUERY: Received request. Query: '{request_data.query}', Session: {request_data.session_id}")
    api_logger.info(f"HANDLE_QUERY: Orchestrator object ID: {id(orchestrator)}")

    if orchestrator is None:
        api_logger.error("HANDLE_QUERY: Orchestrator not initialized. Cannot process query.")
        raise HTTPException(status_code=503, detail="Service temporarily unavailable: Orchestrator not initialized.")

    session_id_to_use = request_data.session_id or "default_session"

    try:
        api_logger.info(f"HANDLE_QUERY: About to call orchestrator.route_query for query: '{request_data.query}'")

        orchestrator_response = await orchestrator.route_query(
            query=request_data.query,
            original_name_for_saving=request_data.original_name_for_saving,
            clear_moi_before_query=request_data.clear_moi_before_query
        )
        api_logger.info(f"HANDLE_QUERY: orchestrator.route_query completed. Analysis from orchestrator_response: '{str(orchestrator_response.get('analysis'))[:100]}...'")

        response_data = AnalysisResponse(
            analysis=orchestrator_response.get("analysis"),
            visualization_path=orchestrator_response.get("visualization_path"),
            processed_smiles=orchestrator_response.get("processed_smiles"),
            error=orchestrator_response.get("error"),
            query_context_for_filename=orchestrator_response.get("analysis_context"),
            current_moi_name=orchestrator_response.get("current_moi_name"),
            current_moi_smiles=orchestrator_response.get("current_moi_smiles"),
            session_id=session_id_to_use
        )
        api_logger.info(f"HANDLE_QUERY: Responding. Viz: {response_data.visualization_path}, Error: {response_data.error}")

        api_logger.info(f"HANDLE_QUERY: Value of response_data.analysis before returning: '{response_data.analysis}' (Type: {type(response_data.analysis)})")
        api_logger.info(f"HANDLE_QUERY: Full response_data object before returning: {response_data.model_dump_json(indent=2, exclude_none=True)}")

        return response_data

    except HTTPException:
        raise
    except Exception as e:
        api_logger.error(f"HANDLE_QUERY: Unhandled exception for query '{request_data.query}': {str(e)}")
        api_logger.error(traceback.format_exc())
        raise HTTPException(status_code=500, detail=f"An internal server error occurred: {str(e)}")


@app.post("/test_orchestrator_query",
          response_model=AnalysisResponse,
          response_model_exclude_none=True,
          summary="Test the orchestrator with a specific query")
async def test_orchestrator_query_endpoint(
    test_query: str = Query(..., description="The test query string."), # Changed to Query param
    session_id: Optional[str] = Query(None, description="Optional session ID for the test."), # Changed to Query param
    original_name: Optional[str] = Query(None, description="Optional original name for saving context."), # Changed to Query param
    clear_moi: bool = Query(False, description="Whether to clear MOI before this test query.") # Changed to Query param
):
    """
    Allows direct testing of the orchestrator's query routing logic.
    Pass the query and other optional parameters as query parameters.
    """
    global orchestrator
    api_logger.info(f"TEST_ORCHESTRATOR_QUERY: Received test query: '{test_query}', Session: {session_id}, ClearMOI: {clear_moi}, OriginalName: {original_name}")

    if orchestrator is None:
        api_logger.error("TEST_ORCHESTRATOR_QUERY: Orchestrator not initialized.")
        raise HTTPException(status_code=503, detail="Service unavailable: Orchestrator not initialized.")

    try:
        orchestrator_response = await orchestrator.route_query(
            query=test_query,
            original_name_for_saving=original_name,
            clear_moi_before_query=clear_moi
        )

        response_data = AnalysisResponse(
            analysis=orchestrator_response.get("analysis"),
            visualization_path=orchestrator_response.get("visualization_path"),
            processed_smiles=orchestrator_response.get("processed_smiles"),
            error=orchestrator_response.get("error"),
            query_context_for_filename=orchestrator_response.get("analysis_context"),
            current_moi_name=orchestrator_response.get("current_moi_name"),
            current_moi_smiles=orchestrator_response.get("current_moi_smiles"),
            session_id=session_id
        )
        api_logger.info(f"TEST_ORCHESTRATOR_QUERY: Responding. Analysis preview: '{str(response_data.analysis)[:100]}...'")
        return response_data

    except HTTPException:
        raise
    except Exception as e:
        api_logger.error(f"TEST_ORCHESTRATOR_QUERY: Exception for test query '{test_query}': {str(e)}")
        api_logger.error(traceback.format_exc())
        raise HTTPException(status_code=500, detail=f"Internal server error processing your request.")


@app.get("/health")
async def health_check():
    """Provides the operational status of the application, orchestrator, and registered components."""
    global orchestrator
    orchestrator_status = "Initialized and Active" if orchestrator is not None else "Not Initialized"

    _registered_agents = ["N/A"]
    try:
        # get_all_agent_names might return None or raise if not initialized
        names = get_all_agent_names()
        _registered_agents = names if names is not None else ["Registry not ready or empty"]
    except Exception:
        _registered_agents = ["Error retrieving agent names"]
        api_logger.exception("Health Check: Error retrieving agent names.")

    _registered_tools = ["N/A"]
    try:
        # get_all_tool_names might return None or raise
        names = get_all_tool_names()
        _registered_tools = names if names is not None else ["Registry not ready or empty"]
    except Exception:
        _registered_tools = ["Error retrieving tool names"]
        api_logger.exception("Health Check: Error retrieving tool names.")

    return {
        "application_status": "AGENT-HUB ChemCopilot API is running.",
        "orchestrator_status": orchestrator_status,
        "registered_agents": _registered_agents, # Used the try-except protected variable
        "registered_tools": _registered_tools    # Used the try-except protected variable
    }