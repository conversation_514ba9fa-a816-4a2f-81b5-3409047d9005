"""Workflow execution endpoints."""

from fastapi import APIRouter, HTTPException
from pydantic import BaseModel
from typing import Dict, Any

from ...orchestration.registry import WorkflowRegistry
from ...orchestration.workflows.patent_workflow import PatentWorkflow
from ...models.patent_models import ProcessingRequest

router = APIRouter(prefix="/workflows", tags=["workflows"])

class WorkflowRequest(BaseModel):
    """Workflow execution request."""
    workflow_name: str
    parameters: Dict[str, Any]

@router.post("/execute")
async def execute_workflow(request: WorkflowRequest):
    """Execute a registered workflow."""
    registry = WorkflowRegistry()
    
    workflow_class = registry.get_workflow(request.workflow_name)
    if not workflow_class:
        raise HTTPException(
            status_code=404,
            detail=f"Workflow '{request.workflow_name}' not found"
        )
    
    try:
        workflow = workflow_class()
        result = await workflow.execute(**request.parameters)
        return result
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Workflow execution failed: {str(e)}"
        )

@router.get("/list")
async def list_workflows():
    """List all available workflows."""
    registry = WorkflowRegistry()
    return {
        "workflows": list(registry.available_workflows.keys())
    }

"""Patent processing endpoints."""


router = APIRouter(prefix="/patents", tags=["patents"])
