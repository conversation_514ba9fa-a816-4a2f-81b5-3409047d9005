from fastapi import APIRouter, HTTPException, Depends, Query, Request # Added Request
from typing import List, Dict, Any, Optional
import time, re
import traceback
from pydantic import BaseModel

# DO NOT import orchestrator directly from chemcopilot_service for request handling
# from app.api.chemcopilot_service import orchestrator, api_logger
from app.api.chemcopilot_service import api_logger # api_logger is fine to import directly

from app.config.settings import get_settings, Settings
from app.api.schemas.general_models import GeneralQueryRequest, AnalysisResponse
from app.agents.registry import get_all_agent_names, get_agent_instance
from app.tools.registry import get_all_tool_names
from app.db.db_ops import DbOps
from app.orchestration.root_agent import RootAgentOrchestrator # Import the type for hinting


router = APIRouter()

db_ops = DbOps()

def _extract_chemical_name(tool_reply: str) -> Optional[str]:
    if tool_reply.startswith("IUPAC Name: "):
        # Remove "IUPAC Name: " prefix
        name_part = tool_reply[len("IUPAC Name: "):]
        # Find " (Source:" and take the part before it
        match = re.search(r"\s+\(Source:", name_part)
        if match:
            name_part = name_part[:match.start()]
        return name_part.strip()
    return None
# Dependency to get the orchestrator from app.state
def get_orchestrator_dependency(request: Request) -> RootAgentOrchestrator:
    # Check if the attribute exists first to avoid AttributeError if state itself is not fully set up (less likely)
    if not hasattr(request.app.state, 'chemcopilot_orchestrator') or \
       request.app.state.chemcopilot_orchestrator is None:
        api_logger.error("Orchestrator accessed via dependency but not found or not initialized on app.state.")
        # Log the current state for debugging
        # api_logger.debug(f"Current app.state attributes: {dir(request.app.state)}")
        raise HTTPException(status_code=503, detail="Service (Orchestrator component) is not available at the moment.")
    return request.app.state.chemcopilot_orchestrator


# --- ChemCopilot Orchestrator Endpoints ---

@router.get("/ping_chemcopilot", summary="Ping ChemCopilot Service")
async def ping_chemcopilot_service(request: Request): # Inject Request
    """A simple endpoint to check if the ChemCopilot service part of the API is responsive."""
    api_logger.info("CHEMCOPILOT PING endpoint was hit!")
    orch_status = "Initialized"
    if not hasattr(request.app.state, 'chemcopilot_orchestrator') or \
       request.app.state.chemcopilot_orchestrator is None:
        orch_status = "Not Initialized"
    return {"message": "ChemCopilot pong", "timestamp": time.time(), "orchestrator_status": orch_status}

@router.post("/query", response_model=AnalysisResponse, response_model_exclude_none=True)
async def handle_query(
    request_data: GeneralQueryRequest,
    # settings: Settings = Depends(get_settings), # Keep if your orchestrator or other logic needs settings directly here
    orchestrator: RootAgentOrchestrator = Depends(get_orchestrator_dependency) # Use the dependency
):
    """
    Receives a chemical query and routes it through the RootAgentOrchestrator
    to determine the appropriate action and generate an analysis.
    """
    api_logger.info(f"HANDLE_QUERY: Received request. Query: '{request_data.query}', Session: {request_data.session_id}")
    api_logger.info(f"HANDLE_QUERY: Orchestrator object ID from dependency: {id(orchestrator)}")
    orchestrator.reaction_class = request_data.reaction_class

    # The dependency 'get_orchestrator_dependency' already raises HTTPException if orchestrator is None

    session_id_to_use = request_data.session_id or "default_session"
    result = db_ops.query_smiles_metadata(request_data.smiles)
    if result and isinstance(result, dict):
        return AnalysisResponse(analysis=result)

    try:
        api_logger.info(f"HANDLE_QUERY: About to call orchestrator.route_query for query: '{request_data.query}'")
        # Use 'orchestrator' from dependency directly
        orchestrator_response = await orchestrator.route_query(
            query=request_data.query,
            original_name_for_saving=request_data.original_name_for_saving,
            clear_moi_before_query=request_data.clear_moi_before_query
        )
        api_logger.info(f"HANDLE_QUERY: orchestrator.route_query completed. Analysis preview: '{str(orchestrator_response.get('analysis'))[:100]}...'")

        response_data = AnalysisResponse(
            analysis=orchestrator_response.get("analysis"),
            visualization_path=orchestrator_response.get("visualization_path"),
            processed_smiles=orchestrator_response.get("processed_smiles"),
            error=orchestrator_response.get("error"),
            query_context_for_filename=orchestrator_response.get("analysis_context"),
            current_moi_name=orchestrator_response.get("current_moi_name"),
            current_moi_smiles=orchestrator_response.get("current_moi_smiles"),
            session_id=session_id_to_use
        )
        api_logger.info(f"HANDLE_QUERY: Responding. Viz: {response_data.visualization_path}, Error: {response_data.error}")
        if 'reaction_smiles_interpreted' in orchestrator_response.get("analysis"):
            db_ops.insert_smiles_data(request_data.smiles, '', orchestrator_response.get("analysis") )
        return response_data
    except HTTPException: # Re-raise HTTPExceptions explicitly
        raise
    except Exception as e:
        api_logger.error(f"HANDLE_QUERY: Unhandled exception for query '{request_data.query}': {str(e)}")
        api_logger.error(traceback.format_exc())
        raise HTTPException(status_code=500, detail=f"An internal server error occurred: {str(e)}")

@router.post("/test_orchestrator_query",
             response_model=AnalysisResponse,
             response_model_exclude_none=True,
             summary="Test the orchestrator with a specific query")
async def test_orchestrator_query_endpoint(
    test_query: str = Query(..., description="The test query string."),
    session_id: Optional[str] = Query(None, description="Optional session ID for the test."),
    original_name: Optional[str] = Query(None, description="Optional original name for saving context."),
    clear_moi: bool = Query(False, description="Whether to clear MOI before this test query."),
    orchestrator: RootAgentOrchestrator = Depends(get_orchestrator_dependency) # Use the dependency
):
    """
    Allows direct testing of the orchestrator's query routing logic.
    Pass the query and other optional parameters as query parameters.
    """
    api_logger.info(f"TEST_ORCHESTRATOR_QUERY: Received test query: '{test_query}', Session: {session_id}, ClearMOI: {clear_moi}, OriginalName: {original_name}")

    # Dependency handles orchestrator being None

    try:
        orchestrator_response = await orchestrator.route_query( # Use 'orchestrator' from dependency
            query=test_query,
            original_name_for_saving=original_name,
            clear_moi_before_query=clear_moi
        )
        response_data = AnalysisResponse(
            # ... (same as in handle_query)
            analysis=orchestrator_response.get("analysis"),
            visualization_path=orchestrator_response.get("visualization_path"),
            processed_smiles=orchestrator_response.get("processed_smiles"),
            error=orchestrator_response.get("error"),
            query_context_for_filename=orchestrator_response.get("analysis_context"),
            current_moi_name=orchestrator_response.get("current_moi_name"),
            current_moi_smiles=orchestrator_response.get("current_moi_smiles"),
            session_id=session_id
        )
        api_logger.info(f"TEST_ORCHESTRATOR_QUERY: Responding. Analysis preview: '{str(response_data.analysis)[:100]}...'")
        return response_data
    except HTTPException:
        raise
    except Exception as e:
        api_logger.error(f"TEST_ORCHESTRATOR_QUERY: Exception for test query '{test_query}': {str(e)}")
        api_logger.error(traceback.format_exc())
        raise HTTPException(status_code=500, detail=f"Internal server error processing your request.")

@router.get("/chemcopilot_status", summary="ChemCopilot Operational Status")
async def chemcopilot_health_check(request: Request): # Inject Request
    """Provides the operational status of the ChemCopilot orchestrator and its registered components."""
    orchestrator_status = "Initialized and Active"
    if not hasattr(request.app.state, 'chemcopilot_orchestrator') or \
       request.app.state.chemcopilot_orchestrator is None:
        orchestrator_status = "Not Initialized"
        
    _registered_agents = ["N/A"]
    try:
        names = get_all_agent_names()
        _registered_agents = names if names is not None else ["Registry not ready or empty"]
    except Exception:
        _registered_agents = ["Error retrieving agent names"]
        api_logger.exception("ChemCopilot Status: Error retrieving agent names.")

    _registered_tools = ["N/A"]
    try:
        names = get_all_tool_names()
        _registered_tools = names if names is not None else ["Registry not ready or empty"]
    except Exception:
        _registered_tools = ["Error retrieving tool names"]
        api_logger.exception("ChemCopilot Status: Error retrieving tool names.")

    return {
        "chemcopilot_service_status": "AGENT-HUB ChemCopilot Subsystem Reporting",
        "orchestrator_status": orchestrator_status,
        "registered_agents": _registered_agents,
        "registered_tools": _registered_tools
    }

class NameConversionRequest(BaseModel):
    identifier: str

class NameConversionResponse(BaseModel):
    iupac_name: Optional[str] = None
    smiles: Optional[str] = None
    error: Optional[str] = None

@router.post("/chemical_converter", 
    summary="Convert between chemical names and SMILES",
    description="Converts chemical names to SMILES or SMILES to chemical names")
async def convert_chemical_name(request: NameConversionRequest):
    """
    Convert between chemical names and SMILES notation.
    The service automatically detects if the input is likely a SMILES string or chemical name.
    
    - If input looks like SMILES: converts to chemical name
    - If input looks like a name: converts to SMILES
    """
    api_logger.info(f"NAME_CONVERSION: Processing identifier: '{request.identifier}'")
    
    try:
        agent = get_agent_instance("NameConversionAgent")
        is_smiles = agent._is_likely_smiles_for_conversion(request.identifier)
        if is_smiles:
            result = db_ops.query_smiles_data(request.identifier) # Check if it's already in the 
            if result:
                api_logger.info(f"NAME_CONVERSION: Found existing SMILES data for '{request.identifier}'")
                return NameConversionResponse(iupac_name=result, smiles=request.identifier, error=None)
        else:
            result = db_ops.query_iupac_data(request.identifier) # Check if it's already in the DB
            if result:
                api_logger.info(f"NAME_CONVERSION: Found existing IUPAC data for '{request.identifier}'")
                return NameConversionResponse(iupac_name=request.identifier, smiles=result, error=None)

        result = await agent.process_message({
            "identifier": request.identifier
        })
        
        if result.get("status") == "success":
            if is_smiles:
                api_logger.info(f"NAME_CONVERSION: Successfully converted SMILES '{request.identifier}' to IUPAC name: {result.get('reply')}")
                if result.get('reply').startswith("IUPAC Name:"):
                    extracted_name = _extract_chemical_name(result.get('reply'))
                    if extracted_name:
                        db_ops.insert_iupac_data(request.identifier, extracted_name) # Save to DB
                        return NameConversionResponse(
                            iupac_name=extracted_name,
                            smiles=request.identifier,
                            error=None
                        )
            else:
                api_logger.info(f"NAME_CONVERSION: Successfully converted IUPAC name '{request.identifier}' to SMILES: {result.get('reply')}")
                if result.get('reply').startswith("SMILES:"):
                    smiles_string = result.get('reply')[len("SMILES:"):].strip()
                    db_ops.insert_smiles_data(smiles_string, request.identifier ) # Save to DB
                    return NameConversionResponse(
                        iupac_name=request.identifier,
                        smiles=smiles_string,
                        error=None
                    )
        else:
            error_msg = result.get("error_message") or result.get("reply") or "Unknown conversion error"
            api_logger.warning(f"NAME_CONVERSION: Error converting '{request.identifier}': {error_msg}")
            return NameConversionResponse(
                iupac_name=None,
                smiles=None,
                error=error_msg
            )
            
    except Exception as e:
        api_logger.error(f"NAME_CONVERSION: Exception processing '{request.identifier}': {str(e)}")
        raise HTTPException(
            status_code=500, 
            detail=f"Error processing conversion request: {str(e)}"
        )

api_logger.info("AGENT_HUB.app.api.routes.agents module loaded, using app.state for orchestrator.")


# @router.get("/", response_model=List[str])
# async def list_available_agents():
#     """Lists all registered agent names."""
#     return get_all_agent_names()

# @router.post("/{agent_name}/invoke")
# async def invoke_specific_agent(agent_name: str, payload: Dict[str, Any]):
#     """Directly invoke a specific agent (for testing or advanced use)."""
#     try:
#         agent = get_agent_instance(agent_name)
#         # Ensure payload matches what agent's process_message expects
#         response = await agent.process_message(payload) 
#         return response
#     except ValueError as e:
#         raise HTTPException(status_code=404, detail=str(e))
#     except Exception as e:
#         raise HTTPException(status_code=500, detail=f"Error invoking agent {agent_name}: {str(e)}")

# print("AGENT_HUB.app.api.routes.agents module loaded")
