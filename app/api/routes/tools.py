# AGENT-HUB/app/api/routes/tools.py
from fastapi import APIRouter, HTTPException, Query
from pydantic import BaseModel, constr
from typing import Any, Optional, Dict, List
import re # Import re for parsing
import logging

# Adjust relative imports based on your actual project structure.
from ...tools.registry import get_tool_instance
from ...config.settings import Settings

router = APIRouter()

# Type alias for constrained SMILES string
SMILESStringConstrained = constr(strip_whitespace=True, min_length=1)

# New response model for successful name conversion
class IUPACNameResponse(BaseModel):
    name: str
    smiles: Optional[str] = None  # Optional SMILES string if available



# The commented-out general tool routes are kept for context if needed later.
# @router.get("/", response_model=List[str])
# async def list_available_tools():
#     """Lists all registered tool names."""
#     # return get_all_tool_names() # Requires get_all_tool_names from registry
#     return ["SMILES2Name", "NameToSMILES"] # Placeholder

# @router.post("/{tool_name}/execute")
# async def execute_specific_tool(tool_name: str, payload: Dict[str, Any]):
#     """Directly execute a specific tool (for testing). Payload keys should match tool's _run args."""
#     try:
#         current_settings = Settings()
#         tool = get_tool_instance(tool_name, settings=current_settings)
#         response = tool.execute(**payload)
#         return response
#     except ValueError as e: # Tool not found or issue with payload
#         raise HTTPException(status_code=404, detail=str(e)) # Or 422 for bad payload
#     except Exception as e:
#         raise HTTPException(status_code=500, detail=f"Error executing tool {tool_name}: {str(e)}")

# print("AGENT_HUB.app.api.routes.tools module loaded with GET endpoint.")