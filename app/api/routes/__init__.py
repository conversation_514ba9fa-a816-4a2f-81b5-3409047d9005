# AGENT-HUB/app/api/routes/__init__.py
from fastapi import APIRouter

# Example: You might create routers for different concerns
# from . import agents_routes, sessions_routes, tools_routes # Assuming you create these files

# main_api_router = APIRouter()
# main_api_router.include_router(agents_routes.router, prefix="/agents", tags=["Agent Management"])
# main_api_router.include_router(sessions_routes.router, prefix="/sessions", tags=["Session Management"])
# main_api_router.include_router(tools_routes.router, prefix="/tools", tags=["Tool Interactions"])

# print("AGENT_HUB.app.api.routes package initialized")

# For now, can be empty if all routes are in app/api/main.py
