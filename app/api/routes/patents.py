"""Patent processing endpoints."""

from fastapi import APIRouter, HTTPException
from pydantic import BaseModel
from typing import Dict, Any

from ...models.patent_models import ProcessingRequest
from ...orchestration.workflows.patent_workflow import PatentWorkflow
from ...utils.logging import get_logger

router = APIRouter(prefix="/patents", tags=["patents"])
logger = get_logger(__name__)

@router.post("/process")
async def process_patent(request: ProcessingRequest) -> Dict[str, Any]:
    """Process patent data."""
    try:
        workflow = PatentWorkflow()
        result = await workflow.execute(**request.dict())
        
        if result.get("status") == "error":
            raise HTTPException(
                status_code=400,
                detail=result.get("error", "Workflow execution failed")
            )
            
        return result
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Patent processing failed: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Patent processing failed: {str(e)}"
        )