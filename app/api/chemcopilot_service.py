import logging
import os
import sys
import traceback
from typing import Optional

from fastapi import FastAPI # Crucial for app.state

from app.config.settings import get_settings, Settings
from app.agents.registry import load_all_agents, get_all_agent_names
from app.tools.registry import load_all_tools, get_all_tool_names
from app.orchestration.root_agent import RootAgentOrchestrator

# Logger setup
api_logger = logging.getLogger("app.api.chemcopilot")
api_logger.setLevel(logging.INFO)
if not api_logger.hasHandlers() and not logging.getLogger().hasHandlers():
    handler = logging.StreamHandler(sys.stdout)
    formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
    handler.setFormatter(formatter)
    api_logger.addHandler(handler)
    api_logger.propagate = False
elif not api_logger.hasHandlers() and logging.getLogger().hasHandlers():
    api_logger.propagate = True

# We no longer primarily rely on this global for request handling by other modules
# It can still be here if this module itself needs a direct reference after initialization.
# _module_global_orchestrator: Optional[RootAgentOrchestrator] = None

async def initialize_chemcopilot_services(app_instance: FastAPI): # Add app_instance parameter
    # global _module_global_orchestrator # if you need to set it for this module's internal use
    api_logger.info("ChemCopilot services initialization starting...")

    current_settings: Optional[Settings] = None
    try:
        current_settings = get_settings()
    except RuntimeError as rte_settings:
        api_logger.error(f"CRITICAL INITIALIZATION ERROR: Failed to load settings. Error: {rte_settings}")
        api_logger.error(traceback.format_exc())
        current_settings = None
        app_instance.state.chemcopilot_orchestrator = None # Ensure it's None on app.state too
        # _module_global_orchestrator = None
        api_logger.warning("Proceeding with initialization despite settings load failure. Functionality will be limited.")

    if current_settings:
        api_logger.info(f"OpenAI API Key: {'SET' if current_settings.OPENAI_API_KEY and 'YOUR_OPENAI_KEY' not in current_settings.OPENAI_API_KEY else 'NOT SET or Placeholder'}")
        api_logger.info(f"Perplexity API Key: {'SET' if current_settings.PERPLEXITY_API_KEY and 'your_perplexity_api_key_here' not in current_settings.PERPLEXITY_API_KEY else 'NOT SET or Placeholder'}")
        api_logger.info(f"Default LLM Provider: {current_settings.DEFAULT_LLM_PROVIDER}")

        api_logger.info("Loading all tools...")
        try:
            load_all_tools()
            api_logger.info(f"Registered Tools: {get_all_tool_names()}")
        except Exception as e_tools:
            api_logger.error(f"INITIALIZATION ERROR: Failed to load tools. Error: {e_tools}")
            api_logger.error(traceback.format_exc())

        api_logger.info("Loading all agents...")
        try:
            load_all_agents()
            api_logger.info(f"Registered Agents: {get_all_agent_names()}")
        except Exception as e_agents:
            api_logger.error(f"INITIALIZATION ERROR: Failed to load agents. Error: {e_agents}")
            api_logger.error(traceback.format_exc())

        api_logger.info("Attempting to initialize RootAgentOrchestrator...")
        local_orchestrator_instance = None
        try:
            local_orchestrator_instance = RootAgentOrchestrator()
            api_logger.info("RootAgentOrchestrator instance created successfully.")
        except AttributeError as ae:
            api_logger.error(f"INITIALIZATION ERROR (AttributeError): A required setting for RootAgentOrchestrator might be missing. Error: {ae}")
            api_logger.error(traceback.format_exc())
        except ImportError as ie:
            api_logger.error(f"INITIALIZATION ERROR (ImportError): A module required by orchestrator could not be imported. Error: {ie}")
            api_logger.error(traceback.format_exc())
        except RuntimeError as rte:
            api_logger.error(f"INITIALIZATION ERROR (RuntimeError): Critical settings issue in RootAgentOrchestrator. Error: {rte}")
            api_logger.error(traceback.format_exc())
        except Exception as e:
            api_logger.error(f"FATAL ERROR during RootAgentOrchestrator initialization: {e}")
            api_logger.error(traceback.format_exc())
        
        app_instance.state.chemcopilot_orchestrator = local_orchestrator_instance
        # _module_global_orchestrator = local_orchestrator_instance # if needed by this module
        if local_orchestrator_instance:
            api_logger.info("RootAgentOrchestrator initialized and set on app.state.")
        else:
            api_logger.error("RootAgentOrchestrator FAILED to initialize. app.state.chemcopilot_orchestrator is None.")

    else:
        api_logger.error("Skipping tool, agent, and orchestrator initialization due to settings load failure.")
        app_instance.state.chemcopilot_orchestrator = None
        # _module_global_orchestrator = None

    orchestrator_on_state_status = 'Initialized' if hasattr(app_instance.state, 'chemcopilot_orchestrator') and app_instance.state.chemcopilot_orchestrator else 'NOT Initialized'
    api_logger.info(f"ChemCopilot services initialization complete. Orchestrator on app.state: {orchestrator_on_state_status}")
    return current_settings

def mount_visualization_statics(app: FastAPI, settings: Optional[Settings]):
    # ... (this function remains the same) ...
    if not settings:
        api_logger.error("Cannot mount visualization statics: settings not available.")
        return

    from fastapi.staticfiles import StaticFiles # Local import

    static_dir_for_visualizations = settings.VISUALIZATION_DIR_ABS
    if not os.path.exists(static_dir_for_visualizations):
        try:
            os.makedirs(static_dir_for_visualizations, exist_ok=True)
            api_logger.info(f"Created static directory for visualizations: {static_dir_for_visualizations}")
        except Exception as e_dir:
            api_logger.error(f"Error creating visualization directory {static_dir_for_visualizations}: {e_dir}")

    if os.path.exists(static_dir_for_visualizations) and os.path.isdir(static_dir_for_visualizations):
        try:
            app.mount(
                settings.VISUALIZATION_WEB_BASE_PATH,
                StaticFiles(directory=settings.VISUALIZATION_DIR_ABS),
                name="visualizations"
            )
            api_logger.info(f"Serving visualizations from: {settings.VISUALIZATION_DIR_ABS} at {settings.VISUALIZATION_WEB_BASE_PATH}")
        except Exception as e_mount: # More specific: RuntimeError if path/name conflict
            api_logger.error(f"Error mounting static files: {e_mount}")
            api_logger.error(f"  Attempted to mount dir: {settings.VISUALIZATION_DIR_ABS} at path {settings.VISUALIZATION_WEB_BASE_PATH}")
            api_logger.error(f"  VISUALIZATION_DIR_ABS exists: {os.path.exists(settings.VISUALIZATION_DIR_ABS)}")
    else:
        api_logger.error(f"Static directory for visualizations not found or not a directory: {static_dir_for_visualizations}. Cannot mount.")