"""Core data models for Patent2Reaction."""

from typing import Optional, Dict, List, Any
from pydantic import BaseModel, Field
from enum import Enum

class ModelType(str, Enum):
    """Supported AI models."""
    GEMINI = "gemini"
    GPT = "gpt"
    BOTH = "both"

class ProcessingRequest(BaseModel):
    material: Optional[str] = None
    patent_id: Optional[str] = None
    num_results: int = Field(default=5, le=50)
    model_type: ModelType = ModelType.GPT
    enable_preprocessing: bool = True
    enable_alignment: bool = True

class ProcessingStatus(str, Enum):
    """Processing status values."""
    PENDING = "pending"
    PROCESSING = "processing"
    COMPLETED = "completed"
    FAILED = "failed"


class ProcessingResult(BaseModel):
    """Result model for patent processing."""
    status: ProcessingStatus
    material: Optional[str] = None
    patent_id: Optional[str] = None
    model_type: ModelType
    patents_processed: List[str] = Field(default_factory=list)
    reactions: Dict[str, Any] = Field(default_factory=dict)
    preprocessing_results: Dict[str, Any] = Field(default_factory=dict)
    alignment_results: Dict[str, Any] = Field(default_factory=dict)
    error_message: Optional[str] = None
    processing_time: Optional[float] = None
    cache_hits: int = 0
    total_patents_found: int = 0

    class Config:
        """Pydantic configuration."""
        use_enum_values = True


class PatentDetails(BaseModel):
    """Patent details model."""
    patent_id: str
    title: Optional[str] = None
    abstract: Optional[str] = None
    description: Optional[str] = None
    claims: Optional[str] = None
    patent_url: Optional[str] = None
    publication_date: Optional[str] = None


class ReactionData(BaseModel):
    """Chemical reaction data model."""
    reaction_id: str
    reactants: List[str]
    products: List[str]
    conditions: Dict[str, Any]
    reaction_smiles: Optional[str] = None
    source_patent: str

