
from enum import Enum
import logging
import uuid
import hashlib
from pymongo import MongoClient
from datetime import datetime
from app.config.settings import get_settings
import os

app_config = get_settings()


logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
class PatentStatus(Enum):
    """
    Enum to represent the status of the retro synthesis process.
    """
    PENDING = "PENDING"
    RUNNING = "RUNNING"
    COMPLETED = "COMPLETED"
    FAILED = "FAILED"

class DbOps:
    """
    A class to handle MongoDB operations for logging retro synthesis requests.
    """
    def __init__(self):
        self.client = MongoClient(app_config.MONGO_URL, uuidRepresentation='standard')
        self.db = self.client['agent_hub_db']
        self.requests_collection = self.db['agent_requests']
        self.smiles_metadata = self.db['smiles_metadata']
        # self._create_indexes()

    def _create_indexes(self):
        """
        Create necessary indexes for the collections.
        """
        self.requests_collection.create_index('request_id', unique=True)

    def insert_log(self, request_id, input_type, input_value, status, user_id='', tenant_id='', patent_id='', error_message=None):
        """
        Insert or update a log entry and track status history.
        """
        current_time = datetime.now()
        
        # Convert request_id to string if it's a UUID
        request_id_str = str(request_id) if isinstance(request_id, uuid.UUID) else request_id
        
        # Prepare the document for new entries
        log_doc = {
            'request_id': request_id_str,
            'input_type': input_type,
            'input_value': input_value,
            'status': status.value,
            'patent_id': patent_id,
            'type': 'PATENT_SEARCH',
            'user_id': user_id,
            'tenant_id': tenant_id,
            'error_message': error_message,
            'created_at': current_time,
            'updated_at': current_time
        }

        # Update status and updated_at for existing documents, or insert new document
        self.requests_collection.update_one(
            {'request_id': request_id_str},
            {
            '$set': {
                'status': status.value,
                'updated_at': current_time
            },
            '$setOnInsert': {k: v for k, v in log_doc.items() if k not in ['status', 'updated_at']}
            },
            upsert=True
        )

        logging.info(f"Updated log for request_id: {request_id_str}, status: {status.value}")
        
    def insert_smiles_data(self, smiles: str, name: str, other_information: dict = None):
        """
        Insert or update SMILES data, using smiles as unique key.
        Updates only other_information if record exists.
        """
        current_time = datetime.now()
        data_doc = {
            'smiles': smiles,
            'hashed_smiles': hashlib.md5(smiles.encode('utf-8')).hexdigest(),
            'iupac_name': name,
            'other_information': other_information,
            'created_at': current_time,
            'updated_at': current_time
        }
        if isinstance(other_information, dict):
            self.smiles_metadata.update_one(
                {'smiles': smiles},
                {
                    '$set': {
                        'other_information': other_information,
                        'updated_at': current_time
                    },
                    '$setOnInsert': {k: v for k, v in data_doc.items() if k not in ['other_information', 'updated_at']}
                },
                upsert=True
            )
        else:
            self.smiles_metadata.insert_one(data_doc)
        logging.info(f"Inserted/Updated SMILES data for: {smiles}")
    
    def insert_iupac_data(self, smiles: str, iupac_name: str):
        """
        Insert or update IUPAC name and SMILES data into the database.
        
        Args:
            smiles (str): The SMILES string to insert.
            iupac_name (str): The IUPAC name to insert.
        
        """
        current_time = datetime.now()
        data_doc = {
            'smiles': smiles,
            'iupac_name': iupac_name,
            'hashed_smiles': hashlib.md5(smiles.encode('utf-8')).hexdigest(),
            'created_at': current_time,
            'updated_at': current_time
        }
        
        # Update if SMILES exists, otherwise insert new document
        self.smiles_metadata.update_one(
            {'smiles': smiles},
            {
                '$set': data_doc
            },
            upsert=True
        )
    
    def query_smiles_data(self, smiles: str):
        result = self.smiles_metadata.find_one({'smiles': smiles})
        if result:
            return result.get('iupac_name')
        return None
    
    def query_iupac_data(self, iupac_name: str):
        """
        Query the database for a given IUPAC name and return the corresponding SMILES.
        
        Args:
            iupac_name (str): The IUPAC name to query.
        
        Returns:
            str: The SMILES string if found, otherwise None.
        """
        result = self.smiles_metadata.find_one({'iupac_name': iupac_name})
        if result:
            return result.get('smiles')
        return None
    
    def query_smiles_metadata(self, smiles: str):
        result = self.smiles_metadata.find_one({'smiles' : smiles})
        if result:
            return result.get('other_information')
        return None
    
    def insert_retro_data(self, request_id, unique_id, route_id, num_steps, total_route_score, target_smiles, total_cost ,raw_smiles, data):
        """
        Insert retro synthesis data into the database.
        """
        current_time = datetime.now()
        data_doc = {
            'request_id': request_id,
            'route_id': route_id,
            'unique_id': unique_id,
            'target_smiles': target_smiles,
            'raw_smiles': raw_smiles,
            'num_steps': num_steps,
            'total_route_score': total_route_score,
            'total_cost': total_cost,
            'data': data,
            'created_at': current_time,
            'updated_at': current_time
        }
        try:
            # Update if unique_id exists, otherwise insert new document
            result = self.db['retro_data'].update_one(
                {'unique_id': unique_id},
                {
                    '$set': {
                        'updated_at': current_time
                    },
                    '$setOnInsert': {k: v for k, v in data_doc.items() if k != 'updated_at'}
                },
                upsert=True
            )
            if result.upserted_id:
                logging.info(f"Inserted new retro data for unique_id: {unique_id}")
            else:
                logging.info(f"Updated existing retro data for unique_id: {unique_id}")
        except Exception as e:
            logging.error(f"Failed to upsert retro data for unique_id {unique_id}: {str(e)}")

    def fetch_latest_request_id_by_target_smiles_and_patent_id(self, patent_id, target_smiles):
        try:
            # Build the query filter
            query = {"input_value": target_smiles, "input_type": "MOLECULE_NAME", "type": "PATENT_SEARCH", "status": "COMPLETED"}
            if patent_id is not None and patent_id.strip() != "":
                query["patent_id"] = patent_id

            # Execute the query
            cursor = (
                self.db["agent_requests"]
                .find(query)
                .sort("updated_at", -1)  # sort descending
                .limit(1)
            )

            doc = next(cursor, None)
            return doc.get("request_id") if doc else None

        except Exception as e:
            # Log, re-raise, or handle as you wish
            print(f"Error fetching latest request id: {e}")
            return None

    def close(self):
        """
        Close the MongoDB connection.
        
        """
        self.client.close()
