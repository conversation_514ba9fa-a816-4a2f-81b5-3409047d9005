import os
import json
import logging
import redis
import traceback
import contextvars
import datetime
from celery import Celery
from celery.signals import after_setup_logger
from app.utils.es_helper import *
from app.db.db_ops import DbOps, PatentStatus
from app.config.settings import get_settings
from app.utils.context_helpers import request_id_var

from app.orchestration.workflows.patent_workflow import PatentWorkflow

# Configure logging
logger = logging.getLogger(__name__)
logger.setLevel(logging.INFO)
formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')

# File handler
os.makedirs('logs', exist_ok=True)
fh = logging.FileHandler('logs/tasks.log')
fh.setFormatter(formatter)
logger.addHandler(fh)


# Stream handler (for console)
sh = logging.StreamHandler()
sh.setFormatter(formatter)
logger.addHandler(sh)

app_config = get_settings()


# Celery config
celery_app = Celery(__name__, broker=app_config.REDIS_URL, backend=app_config.REDIS_URL)
celery_app.conf.update(
    worker_prefetch_multiplier=1,
    task_acks_late=True,
    worker_disable_rate_limits=True,
    task_reject_on_worker_lost=True,
    task_ignore_result=False,
    worker_max_tasks_per_child=1,
    task_default_queue=app_config.PATENT_INPUT_REDIS_QUEUE,  # Add this line
    task_routes={
        'process_patent_message': {'queue': app_config.PATENT_INPUT_REDIS_QUEUE}
    }
)



@after_setup_logger.connect
def setup_celery_logger(logger, *args, **kwargs):
    # Ensure file + console logging in worker
    logger.setLevel(logging.INFO)

    fh = logging.FileHandler('logs/tasks.log')
    fh.setFormatter(formatter)
    logger.addHandler(fh)

    sh = logging.StreamHandler()
    sh.setFormatter(formatter)
    logger.addHandler(sh)


class RedisClient:
    def __init__(self, redis_url):
        self.redis = redis.Redis.from_url(redis_url, decode_responses=True)
        self.redis.ping()

    def insert_into_output_queue(self, data):
        self.redis.rpush(app_config.PATENT_OUTPUT_REDIS_QUEUE, json.dumps(data))

    def get_queue_length(self, queue_name):
        return self.redis.llen(queue_name)

redis_client = RedisClient(app_config.REDIS_URL)

@celery_app.task(name='process_patent_message', bind=True, queue=app_config.PATENT_INPUT_REDIS_QUEUE)
def process_patent_message(self, payload):
    """
    Celery task to process patent messages
    """
    logger.info(f"Received payload: {payload}")
    
    azure_config = {
        "provider": app_config.SEARCH_PROVIDER,
        "endpoint": f"https://{app_config.AZURE_SEARCH_SERVICE_NAME}.search.windows.net",
        "index_name": app_config.AZURE_PATENT_DATA_INDEX_NAME,
        "api_key": app_config.AZURE_SEARCH_API_KEY
    }

    es_client = get_search_client(azure_config)

    db_ops = DbOps()
    request_id = str(payload.get('request_id', ''))
    molecule_name = payload.get('molecule_name', '')
    patent_id = payload.get('patent_id', '')
    model_type = payload.get('model_type', 'gemini')
    max_results = 10000
    enable_preprocessing = payload.get('enable_preprocessing', True)
    enable_alignment = payload.get('enable_alignment', True)
    target_smiles = payload.get('target_smiles', '')

    # os.environ['REQUEST_ID'] = str(request_id)

    request_id_var.set(request_id)
    
    logger.info(f"Processing message for request_id: {request_id}, molecule_name: {molecule_name}, patent_id: {patent_id}")

    try:
        workflow = PatentWorkflow()
        
        db_ops.insert_log(
            request_id=request_id,
            input_type="MOLECULE_NAME",
            input_value=target_smiles,
            status=PatentStatus.RUNNING,
            user_id=payload.get("user_id", ""),
            tenant_id=payload.get("tenant_id", ""),
            patent_id=patent_id
        )
        if molecule_name or target_smiles:
            logger.info(f"Processing molecule_name: {molecule_name} for request_id: {request_id} and for target_smiles: {target_smiles}")
            params = {
                "material": molecule_name,
                "model_type": model_type,
                "max_results": max_results,
                "enable_preprocessing": enable_preprocessing,
                "enable_alignment": enable_alignment,
                "chemical_name": molecule_name,
                'patent_id': patent_id if patent_id else '',
                'target_smiles': target_smiles,
                'request_id': request_id
            }
        else:
            logger.error("Neither patent_id nor molecule_name provided")
            return

        old_request_id = db_ops.fetch_latest_request_id_by_target_smiles_and_patent_id(patent_id, target_smiles)
        old_data_processed = False
        if old_request_id:
            azure_config_patent_summary = {
                **azure_config,
                "index_name": app_config.AZURE_PATENT_SUMMARY_INDEX_NAME
            }
            es_client_for_patent_summary = get_search_client(azure_config_patent_summary)
            patents_data = es_client_for_patent_summary.search_documents(query={"key": "request_id", "value": old_request_id})
            logger.info(f"Found {len(patents_data)} patents for old request_id: {old_request_id}")
            if patents_data:
                for patent_data in patents_data:
                    patent_data.update(
                        {
                            "id": str(uuid.uuid4()),
                            "request_id": request_id,
                            "created_at": datetime.datetime.now().isoformat(),
                        }
                    )
                    es_client_for_patent_summary.insert_document(document=patent_data)
                logger.info(f"Inserted patent data with new request_id: {request_id}")
                old_data_processed = True

        # Execute workflow
        result = None
        if not old_data_processed:
            result = workflow.execute(params)

        if result:
            result['request_id'] = str(request_id)
            result['processed_at'] = datetime.datetime.now().isoformat()

            # Store in ES
            es_client.insert_document(
                document={
                    "patent_id": result.get("patent_id", ""),
                    "request_id": request_id,
                    "chemical_name": params["chemical_name"],
                    "created_at" : datetime.datetime.now().isoformat(),
                    'data' : json.dumps(result) }
            )
        if result or old_data_processed:
            db_ops.insert_log(
                request_id=request_id,
                input_type="MOLECULE_NAME",
                input_value=target_smiles,
                status=PatentStatus.COMPLETED,
                user_id=payload.get("user_id", ""),
                tenant_id=payload.get("tenant_id", ""),
                patent_id=payload.get("patent_id", ""),
                error_message=None
            )

            logger.info(f"Successfully processed request_id: {request_id}")
            redis_result = celery_app.send_task(
                'patent_result_handler',
                args=[{
                'status': "SUCCESS",
                'request_id': request_id,
            }],
                queue=app_config.PATENT_OUTPUT_REDIS_QUEUE
            )
        else:
            logger.error(f"Workflow execution failed for request_id: {request_id}")

    except Exception as e:
        logger.exception(f"Error processing request_id {request_id}: {str(e)}")
        db_ops.insert_log(
            request_id=request_id,
            input_type="MOLECULE_NAME",
            input_value=target_smiles,
            status=PatentStatus.FAILED,
            user_id=payload.get("user_id", ""),
            tenant_id=payload.get("tenant_id", ""),
            patent_id=patent_id,
            error_message= str(traceback.format_exc())
        )
        redis_result = celery_app.send_task(
                'patent_result_handler',
                args=[{
                    'status': "FAILED",
                    'request_id': request_id,
                    'error_message': str(e)
                }],
                queue=app_config.PATENT_OUTPUT_REDIS_QUEUE
            )
        raise


if __name__ == "__main__":
    # Run the Celery worker
    celery_app.start(
        argv=[ 'worker', '--loglevel=info', '--concurrency=1']
    )