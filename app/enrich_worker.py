import os
import json, time
import logging
import redis
import traceback
import contextvars
import datetime
from celery import Celery
from celery.signals import after_setup_logger
from app.utils.es_helper import *
from app.db.db_ops import DbOps
from app.config.settings import get_settings
from app.agents.registry import load_all_agents, get_all_agent_names
from app.tools.registry import load_all_tools, get_all_tool_names
import asyncio
from app.orchestration.root_agent import RootAgentOrchestrator # Import the type for hinting

logger = logging.getLogger(__name__)
db_ops = DbOps()
app_config = get_settings()
celery_inflow_app = Celery(__name__, broker=app_config.REDIS_URL, backend=app_config.REDIS_URL)
celery_inflow_app.conf.update(
    worker_prefetch_multiplier=1,
    task_acks_late=True,
    worker_disable_rate_limits=True,
    task_reject_on_worker_lost=True,
    task_ignore_result=False,
    worker_max_tasks_per_child=1,
    task_default_queue=app_config.ENRICH_INPUT_REDIS_QUEUE,  # Add this line
    task_routes={
        'process_enrich_task': {'queue': app_config.ENRICH_INPUT_REDIS_QUEUE}
    }
)


def enrich_data(payload):

    final_data = []
    route_id = payload.get("route_id")
    for dat in payload.get('other_info'):
        reaction_smiles = dat.get("reaction_smiles")
        query = f"Give the full info about this reaction - {reaction_smiles}"
        reaction_class = dat.get("reaction_class")
        result = db_ops.query_smiles_metadata(reaction_smiles)
        if result and isinstance(result, dict):
            final_data.append({"reaction_smiles": reaction_smiles, "other_info": result})
            continue
            
        orchestrator = RootAgentOrchestrator()
        load_all_tools()
        load_all_agents()
        orchestrator.reaction_class = json.loads(reaction_class)
        max_retries = 3
        retry_delay = 10  # seconds
        for attempt in range(max_retries):
            try:
                logger.info(f"HANDLE_QUERY: About to call orchestrator.route_query for query: '{query}' (attempt {attempt + 1}/{max_retries})")
                # Create event loop and run async function
                loop = asyncio.get_event_loop()
                orchestrator_response = loop.run_until_complete(
                    orchestrator.route_query(query=query)
                )
                logger.info(f"HANDLE_QUERY: orchestrator.route_query completed. Analysis preview: '{str(orchestrator_response.get('analysis'))[:100]}...'")

                if 'reaction_smiles_interpreted' in orchestrator_response.get("analysis"):
                    db_ops.insert_smiles_data(reaction_smiles, '', orchestrator_response.get("analysis"))
                if 'error' in orchestrator_response.get("analysis"):
                    logger.error(f"Error in orchestrator_response: {orchestrator_response.get('analysis').get('error')}")
                    raise Exception(f"Error in orchestrator_response: {orchestrator_response.get('analysis').get('error')}")
                dt = {"reaction_smiles": reaction_smiles, "other_info": orchestrator_response.get("analysis")}
                final_data.append(dt)
                break  # Success - exit retry loop
            
            except Exception as e:
                if attempt < max_retries - 1:  # Don't sleep on last attempt
                    logger.warning(f"Attempt {attempt + 1} failed, retrying in {retry_delay} seconds: {str(e)}")
                    time.sleep(retry_delay)
                else:
                    logger.error(f"All {max_retries} attempts failed for query '{query}': {str(e)}")
                    logger.error(traceback.format_exc())
                    final_data.append({"reaction_smiles": reaction_smiles, "other_info": {}})
    return {"route_id": route_id, "other_info": final_data}
    

def send_enriched_task(payload):
    """Send a task to the patent worker"""    
    # Send task to specific queue
    result = celery_inflow_app.send_task(
        'process_enrich_inflow_task',
        args=[payload],
        queue=app_config.ENRICH_OUTPUT_REDIS_QUEUE
    )
    return result

@celery_inflow_app.task(bind=True, max_retries=3, name='process_enrich_task')
def process_enrich_task(self, payload):
    start_time = time.time()
    required_fields = {"route_id"}
    if not all(field in payload for field in required_fields):
        missing = required_fields - payload.keys()
        error_msg = f"Missing required fields: {missing}"
        logger.error(error_msg)
        return False
    logger.info(f"[ENRICH] Processing enrich task for route_id: {payload['route_id']}")
    final_data = enrich_data(payload)
    send_enriched_task(final_data)
    logger.info(f"[ENRICH] Completed enrich task for route_id: {payload['route_id']} in {time.time() - start_time:.2f}s")
    return True

