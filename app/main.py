# AGENT-HUB/app/main.py
import logging

from fastapi import Fast<PERSON><PERSON>
from fastapi.middleware.cors import CORSMiddleware

from app.api.chemcopilot_service import (
    initialize_chemcopilot_services,       # This function now takes 'app'
    mount_visualization_statics,
    api_logger as chemcopilot_api_logger
)


from .api.routes import workflows, patents # Assuming these exist
from app.api.routes import agents as chemcopilot_agents_router

# >>> ADD THIS IMPORT <<<
from app.api.routes.tools import router as chemcopilot_tools_router # Import the tools router

# ... (main_app_logger setup) ...
main_app_logger = logging.getLogger("agent_hub_main")
main_app_logger.setLevel(logging.INFO)
if not main_app_logger.hasHandlers(): # Basic setup if not configured elsewhere
    stream_handler = logging.StreamHandler()
    formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
    stream_handler.setFormatter(formatter)
    main_app_logger.addHandler(stream_handler)


app = FastAPI(
    title="Agent Hub API",
    description="AI Agent Orchestration Platform",
    version="1.0.0"
)

# ... (CORS middleware) ...
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"]
)


@app.on_event("startup")
async def startup_event():
    main_app_logger.info("Main application startup sequence initiated...")
    chemcopilot_api_logger.info("Invoking ChemCopilot services initialization...")
    # Pass the 'app' instance to the initialization function
    loaded_settings = await initialize_chemcopilot_services(app)
    
    mount_visualization_statics(app, loaded_settings)
    main_app_logger.info("Main application startup sequence complete.")

# ... (rest of main.py, including router inclusions and root/health endpoints) ...
# Register routers
app.include_router(
    chemcopilot_agents_router.router, # Ensure you are including the router object
    prefix="/api/v1/chemcopilot",     # This makes agent routes like /api/v1/chemcopilot/query etc.
    tags=["ChemCopilot Orchestration & Agents"]
)

# >>> ADD THIS SECTION TO INCLUDE THE TOOLS ROUTER <<<
app.include_router(
    chemcopilot_tools_router,                 # Use the imported tools router
    prefix="/api/v1/tools",                   # This is the prefix you want for tool endpoints
    tags=["ChemCopilot Tools", "Chemistry"]   # Add appropriate tags
)

# Register other application routers
app.include_router(
    workflows.router,
    prefix="/api/v1/workflows", # Example prefix
    tags=["Workflows"]
)
# app.include_router(patents.router, prefix="/api/v1")

@app.get("/health")
async def health_check():
    """Health check endpoint."""
    return {
        "status": "healthy",
        "version": "1.0.0"
    }

@app.get("/")
async def root():
    """Root endpoint."""
    return {
        "status": "online",
        "version": "1.0.0"
    }

main_app_logger.info("Agent Hub FastAPI application configured.")