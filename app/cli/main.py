"""Command-line interface for Patent2Reaction."""

import argparse
import json
import sys
from typing import Optional
from ..core.models import ProcessingRequest, ModelType
from ..services.chemical_converter import ChemicalConverter
from ..services.patent_search import PatentSearchService
from ..services.patent_preprocessor import PatentPreprocessor
from ..services.patent_processor import PatentProcessor
from ..services.entity_alignment import EntityAlignmentService
from ..utils.logging import get_logger


def create_parser() -> argparse.ArgumentParser:
    """Create command-line argument parser."""
    parser = argparse.ArgumentParser(
        description="Patent2Reaction: Extract chemical reactions from patents",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  # Process patents for a chemical compound
  patent2reaction --material "aspirin" --num-results 5

  # Process a specific patent
  patent2reaction --patent-id "US1234567"

  # Use GPT model instead of Gemini
  patent2reaction --material "caffeine" --model gpt

  # Use both models for comparison
  patent2reaction --material "caffeine" --model both

  # Enable debug mode
  patent2reaction --material "aspirin" --debug
        """
    )
    
    # Input options (mutually exclusive)
    input_group = parser.add_mutually_exclusive_group(required=True)
    input_group.add_argument(
        "--material", "-m",
        type=str,
        help="Chemical name or SMILES string to search for"
    )
    input_group.add_argument(
        "--patent-id", "-p",
        type=str,
        help="Specific patent ID to process (e.g., US1234567)"
    )
    
    # Processing options
    parser.add_argument(
        "--num-results", "-n",
        type=int,
        default=5,
        help="Maximum number of patents to process (default: 5)"
    )
    
    parser.add_argument(
        "--model",
        choices=["gemini", "gpt", "both"],
        default="gemini",
        help="AI model to use for processing (default: gemini)"
    )
    
    # Feature toggles
    parser.add_argument(
        "--no-preprocessing",
        action="store_true",
        help="Disable patent relevance preprocessing"
    )
    
    parser.add_argument(
        "--no-alignment",
        action="store_true",
        help="Disable entity alignment"
    )
    
    parser.add_argument(
        "--no-cache",
        action="store_true",
        help="Disable caching (useful for testing)"
    )
    
    # Output options
    parser.add_argument(
        "--output", "-o",
        type=str,
        help="Output file path (default: print to stdout)"
    )
    
    parser.add_argument(
        "--debug",
        action="store_true",
        help="Enable debug logging"
    )
    
    return parser


def main():
    """Main CLI entry point."""
    parser = create_parser()
    args = parser.parse_args()
    
    # Set up logging
    logger = get_logger(__name__, args.debug)
    
    try:
        # Create processing request
        request = ProcessingRequest(
            material=args.material,
            patent_id=args.patent_id,
            num_results=args.num_results,
            model_type=ModelType(args.model),
            enable_preprocessing=not args.no_preprocessing,
            enable_alignment=not args.no_alignment,
            debug_mode=args.debug,
            disable_cache=args.no_cache
        )
        
        logger.info(f"Starting Patent2Reaction processing with request: {request}")
        
        # Process the request
        result = process_request(request)
        
        # Output results
        if args.output:
            with open(args.output, 'w', encoding='utf-8') as f:
                json.dump(result, f, indent=2, ensure_ascii=False)
            logger.info(f"Results saved to: {args.output}")
        else:
            print(json.dumps(result, indent=2, ensure_ascii=False))
        
        logger.info("Processing completed successfully")
        
    except Exception as e:
        logger.error(f"Processing failed: {str(e)}")
        if args.debug:
            import traceback
            traceback.print_exc()
        sys.exit(1)


def process_request(request: ProcessingRequest) -> dict:
    """Process a Patent2Reaction request.
    
    Args:
        request: Processing request configuration
        
    Returns:
        Dictionary with processing results
    """
    logger = get_logger(__name__, request.debug_mode)
    
    # Step 1: Convert chemical name to SMILES if needed
    smiles = None
    if request.material:
        logger.info(f"Converting material to SMILES: {request.material}")
        converter = ChemicalConverter(debug_mode=request.debug_mode)
        success, result = converter.convert(request.material)
        
        if success:
            smiles = result
            logger.info(f"Successfully converted to SMILES: {smiles}")
        else:
            logger.warning(f"Could not convert to SMILES: {result}")
            smiles = request.material  # Use as-is
    
    # Step 2: Search for patents (if not processing specific patent)
    patent_ids = []
    if request.patent_id:
        patent_ids = [request.patent_id]
        logger.info(f"Processing specific patent: {request.patent_id}")
    elif smiles:
        logger.info(f"Searching for patents related to: {smiles}")
        search_service = PatentSearchService(debug_mode=request.debug_mode)
        patent_ids = search_service.search_patents_by_smiles(smiles, request.num_results)
        logger.info(f"Found {len(patent_ids)} patents")
    
    if not patent_ids:
        return {"error": "No patents found to process"}
    
    # Step 3: Process patents
    results = {
        "metadata": {
            "material": request.material,
            "patent_id": request.patent_id,
            "smiles": smiles,
            "model_type": str(request.model_type),
            "patents_found": len(patent_ids),
            "preprocessing_enabled": request.enable_preprocessing,
            "alignment_enabled": request.enable_alignment
        },
        "patents": {},
        "summary": {
            "total_patents": len(patent_ids),
            "processed_patents": 0,
            "failed_patents": 0
        }
    }
    
    # Initialize services
    processor = PatentProcessor(
        model_type=request.model_type,
        debug_mode=request.debug_mode,
        cache_enabled=not request.disable_cache
    )
    
    # Process each patent
    for patent_id in patent_ids:
        try:
            logger.info(f"Processing patent: {patent_id}")
            
            # For now, create dummy content - in real implementation,
            # this would fetch patent details via SerpAPI
            patent_content = f"Patent {patent_id} content would be fetched here"
            
            # Process the patent
            patent_result = processor.process_patent(patent_id, patent_content)
            results["patents"][patent_id] = patent_result
            results["summary"]["processed_patents"] += 1
            
        except Exception as e:
            logger.error(f"Failed to process patent {patent_id}: {str(e)}")
            results["patents"][patent_id] = {"error": str(e)}
            results["summary"]["failed_patents"] += 1
    
    return results


if __name__ == "__main__":
    main()
