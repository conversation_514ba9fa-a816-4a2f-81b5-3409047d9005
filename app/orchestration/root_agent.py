import autogen
import sys
import os
import re
import json 
from rdkit import Chem
import time
import logging # <<<< IMPORT ADDED HERE
import traceback
from typing import Dict, Optional, List, Any, Callable

# Configure module-level logger
# This basicConfig will apply if no handlers are configured on the root logger yet.
# In a larger application, logging is often configured once at the very entry point.
if not logging.getLogger().hasHandlers(): 
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[logging.StreamHandler(sys.stdout)]
    )
logger = logging.getLogger(__name__) # Define module-level logger

from app.config.settings import get_settings, Settings, PROJECT_ROOT_DIR as CFG_PROJECT_ROOT_DIR
from app.agents.registry import get_agent_instance
# from app.api.schemas.general_models import AnalysisResponse # For type hinting route_query if needed

try:
    import openai # type: ignore
except ImportError:
    openai = None


_orchestrator_moi_context: Dict[str, Optional[str]] = {"name": None, "smiles": None}


class RootAgentOrchestrator:
    def __init__(self):
        self.settings: Settings = get_settings()
        self.conversational_user_proxy: Optional[autogen.UserProxyAgent] = None
        self.conversational_assistant: Optional[autogen.AssistantAgent] = None
        self.analysis_output_dir = os.path.join(CFG_PROJECT_ROOT_DIR, "analysis_outputs_hub")
        os.makedirs(self.analysis_output_dir, exist_ok=True)
        logger.info("Initializing RootAgentOrchestrator components...") # Use module-level logger
        self._initialize_conversational_autogen_agents() 
        self.reaction_class = ''
        logger.info(f"[{self.__class__.__name__}] Orchestrator initialized. MOI: {_orchestrator_moi_context}") # Use module-level logger

    def _extract_reaction_smiles(self, query: str) -> Optional[str]:
        smi_core_chars = r"[\w@\[\]\(\)\.\+\-\=\#\:\$\%\~]" 
        explicit_pattern = rf"(?i:\b(?:reaction\s+smiles?|rxn)\s*[:=]?\s*)({smi_core_chars}+(?:>>{smi_core_chars}+)+)"
        match = re.search(explicit_pattern, query)
        if match:
            smiles = match.group(1).strip(); parts = smiles.split(">>")
            if len(parts) >= 2 and all(p.strip() for p in parts): return smiles
        
        standalone_pattern_double_arrow = rf"(?<![\w\/])({smi_core_chars}+(?:>>{smi_core_chars}+)+)(?![\w\/])"
        potential_matches_double = re.findall(standalone_pattern_double_arrow, query)
        for smi_candidate_match in potential_matches_double:
            smi_candidate = smi_candidate_match if isinstance(smi_candidate_match, str) else smi_candidate_match[0]
            parts = smi_candidate.strip().split(">>")
            if len(parts) >= 2 and all(p.strip() for p in parts):
                try:
                    if Chem.MolFromSmiles(parts[0].split('.')[0]) and Chem.MolFromSmiles(parts[-1].split('.')[0]):
                        return smi_candidate.strip()
                except: pass
        
        smi_core_chars_with_gt = r"[\w@\[\]\(\)\.\+\-\=\#\:\$\%\~\<\>]"
        gt_pattern = rf"(?<![\w\/])({smi_core_chars_with_gt}+(?:>{smi_core_chars_with_gt}+)+)(?![\w\/])"
        match_gt = re.search(gt_pattern, query)
        if match_gt:
            temp_smiles = match_gt.group(1).strip()
            if ">>" not in temp_smiles:
                gt_parts = [p.strip() for p in temp_smiles.split('>') if p.strip()]
                if len(gt_parts) >= 2:
                    lhs_components = ".".join(gt_parts[:-1]) 
                    products_gt = gt_parts[-1]
                    if lhs_components and products_gt:
                        try:
                            if Chem.MolFromSmiles(lhs_components.split('.')[0]) and Chem.MolFromSmiles(products_gt.split('.')[0]):
                                return f"{lhs_components}>>{products_gt}"
                        except: pass
        return None

    def _extract_single_compound_smiles(self, query: str) -> Optional[str]:
        words = query.split()
        regex_candidates = re.findall(r"[A-Za-z0-9@\[\]\(\)\+\-\=\#\:\.\$\%\/\\\{\}]{3,}", query)
        combined_candidates = list(set(words + regex_candidates))
        combined_candidates.sort(key=lambda x: (len(x), sum(1 for c in x if c in '()[]=#@')), reverse=True)
        for s_cand_orig in combined_candidates:
            s_cand = s_cand_orig.strip('.,;:)?!\'"')
            if not s_cand or '>>' in s_cand or '>' in s_cand or '<' in s_cand: continue
            if s_cand.isnumeric() and not ('[' in s_cand and ']' in s_cand) : continue
            try:
                mol = Chem.MolFromSmiles(s_cand, sanitize=True)
                if mol and mol.GetNumAtoms() >= 1:
                    if mol.GetNumAtoms() <= 2 and s_cand.isalpha() and s_cand.lower() in ['as','in','is','at','or','to','be','of','on','no','do','go','so','if','it','me','my','he','we','by','up','us','an','am','are']:
                        if not any(c in s_cand for c in '()[]=#.-+@:/\\%{}1234567890') and (len(s_cand) <=2 and s_cand.islower()):
                            continue 
                    if any(c in s_cand for c in '()[]=#.-+@:/\\%{}') or mol.GetNumAtoms() > 2 or len(s_cand) > 3 or (len(s_cand) > 0 and not s_cand.islower()):
                        return s_cand
            except Exception: pass
        return None

    # --- Tool Invocation Wrappers ---
    async def _tool_invoke_visualization_agent(self, smiles_or_reaction: str) -> str:
        logger.info(f"TOOL_INVOKE: VisualizationAgent for: {smiles_or_reaction}")
        try:
            agent = get_agent_instance("VisualizationAgent")
            result = await agent.process_message({"smiles_or_reaction": smiles_or_reaction})
            return str(result.get("reply", f"Visualization error for {smiles_or_reaction}."))
        except Exception as e:
            logger.error(f"Error invoking VisualizationAgent: {e}", exc_info=True)
            return json.dumps({"error": f"Error in VisualizationAgent: {str(e)}"})

    async def _tool_invoke_bond_analyzer_agent(self, reaction_smiles: str) -> str:
        logger.info(f"TOOL_INVOKE: BondAnalysisAgent for: {reaction_smiles}")
        try:
            agent = get_agent_instance("BondAnalysisAgent")
            result = await agent.process_message({"reaction_smiles": reaction_smiles})
            return str(result.get("reply", f"Bond analysis error for {reaction_smiles}."))
        except Exception as e:
            logger.error(f"Error invoking BondAnalysisAgent: {e}", exc_info=True)
            return json.dumps({"error": f"Error in BondAnalysisAgent: {str(e)}"})

    async def _tool_invoke_name_conversion_agent(self, identifier: str) -> str:
        logger.info(f"TOOL_INVOKE: NameConversionAgent for: {identifier}")
        try:
            agent = get_agent_instance("NameConversionAgent")
            result = await agent.process_message({"identifier": identifier})
            return str(result.get("reply", f"Name conversion error for {identifier}."))
        except Exception as e:
            logger.error(f"Error invoking NameConversionAgent: {e}", exc_info=True)
            return json.dumps({"error": f"Error in NameConversionAgent: {str(e)}"})

    async def _tool_invoke_func_group_agent(self, smiles_or_reaction: str) -> str:
        logger.info(f"TOOL_INVOKE: FuncGroupAgent for: {smiles_or_reaction}")
        try:
            agent = get_agent_instance("FuncGroupAgent")
            result = await agent.process_message({"smiles_or_reaction": smiles_or_reaction})
            return str(result.get("reply", f"Functional group analysis error for {smiles_or_reaction}."))
        except Exception as e:
            logger.error(f"Error invoking FuncGroupAgent: {e}", exc_info=True)
            return json.dumps({"error": f"Error in FuncGroupAgent: {str(e)}"})

    async def _tool_invoke_reaction_classifier_agent(self, reaction_smiles: str, property_query: Optional[str] = None) -> str:
        logger.info(f"TOOL_INVOKE: ReactionClassifierAgent for: {reaction_smiles}, Query: {property_query}")
        try:
            agent = get_agent_instance("ReactionClassifierAgent")
            payload = {"reaction_smiles": reaction_smiles}
            if property_query: payload["property_query"] = property_query
            result = await agent.process_message(payload)
            return str(result.get("reply", f"Reaction classification error for {reaction_smiles}."))
        except Exception as e:
            logger.error(f"Error invoking ReactionClassifierAgent: {e}", exc_info=True)
            return json.dumps({"error": f"Error in ReactionClassifierAgent: {str(e)}"})
        
    async def _tool_invoke_disconnection_suggester_agent(self, smiles: str, functional_groups: Optional[List[str]] = None) -> str:
        logger.info(f"TOOL_INVOKE: DisconnectionSuggesterAgent for: {smiles}")
        try:
            agent = get_agent_instance("DisconnectionSuggesterAgent")
            payload = {"smiles": smiles, "functional_groups": functional_groups or []}
            result = await agent.process_message(payload)
            return str(result.get("reply", f"Disconnection suggestion error for {smiles}."))
        except Exception as e:
            logger.error(f"Error invoking DisconnectionSuggesterAgent: {e}", exc_info=True)
            return json.dumps({"error": f"Error in DisconnectionSuggesterAgent: {str(e)}"})

    async def _tool_invoke_chemical_props_agent(self, chemical_identifier: str) -> str: 
        logger.info(f"TOOL_INVOKE: ChemicalPropsAgent for: {chemical_identifier}")
        try:
            agent = get_agent_instance("ChemicalPropsAgent")
            result = await agent.process_message({"chemical_identifier": chemical_identifier})
            return result.get("reply", f"Chemical properties error for {chemical_identifier}.")
        except Exception as e:
            logger.error(f"Error invoking ChemicalPropsAgent: {e}", exc_info=True)
            return json.dumps({"error": f"Error in ChemicalPropsAgent: {str(e)}"})

    async def _tool_invoke_reaction_recipe_agent(self, reaction_smiles: str) -> str:
        global _orchestrator_moi_context
        logger.info(f"TOOL_INVOKE: ReactionRecipeAgent called by LLM with raw SMILES: '{reaction_smiles}'")

        llm_provided_smiles = reaction_smiles
        
        # --- NEW, SIMPLIFIED VALIDATION LOGIC ---
        # A valid SMILES for our tool is one of these:
        # 1. It contains ">>" (standard format)
        # 2. It contains exactly two ">" and no ">>" (our custom R>A>P format)
        is_standard_format = ">>" in llm_provided_smiles
        is_custom_format = llm_provided_smiles.count('>') == 2 and not is_standard_format

        if not is_standard_format and not is_custom_format:
            # This handles truly malformed inputs, like single ">" or other junk.
            error_msg = f"Tool Error (ReactionRecipeAgent): Invalid reaction SMILES format: '{llm_provided_smiles}'. Must contain '>>' or be in 'Reactants>Agents>Products' format."
            logger.error(error_msg)
            return json.dumps({"error": error_msg})
        
        # If we reach here, the format is valid (either standard or custom).
        # We pass it directly to the agent without modification.
        smiles_for_tool = llm_provided_smiles
        logger.info(f"SMILES format is valid. Proceeding to call ReactionRecipeAgent with: '{smiles_for_tool}'")

        try:
            agent = get_agent_instance("ReactionRecipeAgent")
            args_for_agent: Dict[str, Any] = {"reaction_smiles": smiles_for_tool}
            
            moi_name_context = _orchestrator_moi_context.get("name")
            if moi_name_context and smiles_for_tool == _orchestrator_moi_context.get("smiles"):
                args_for_agent["original_name"] = moi_name_context
                logger.info(f"Added original_name '{moi_name_context}' from MOI to ReactionRecipeAgent call.")

            kwargs_ = {'reaction_class': self.reaction_class}
            tool_response_content = await agent.process_message(message_content=args_for_agent, **kwargs_)
            
            reply_content = tool_response_content.get("reply")
            if tool_response_content.get("status") == "success" and isinstance(reply_content, dict):
                try:
                    return json.dumps(reply_content) 
                except TypeError:
                    logger.error(f"Could not serialize recipe from ReactionRecipeAgent for {smiles_for_tool}", exc_info=True)
                    return json.dumps({"error": "Failed to serialize recipe data from tool."})
            else:
                error_message = tool_response_content.get("error_message") or str(reply_content)
                logger.error(f"Error from ReactionRecipeAgent: {error_message}")
                return json.dumps({"error": f"Tool Error (ReactionRecipeAgent): {error_message}"})
        except Exception as e:
            logger.error(f"Error invoking or processing result from ReactionRecipeAgent: {e}", exc_info=True)
            return json.dumps({"error": f"Error in ReactionRecipeAgent invocation: {str(e)}"})

    def _initialize_conversational_autogen_agents(self):
        logger.info("Initializing conversational Autogen agents...")
        llm_config_orchestrator = {
            "config_list": [
                {
                    "model": self.settings.OPENAI_CHAT_MODEL_ORCHESTRATOR,  # deployment name
                    "api_key": self.settings.AZURE_OPENAI_API_KEY,
                    "api_type": "azure",
                    "base_url": self.settings.OPENAI_API_BASE_URL,  # ✅ use 'base_url', not 'api_base'
                    "api_version": self.settings.OPENAI_API_VERSION,
                }
            ],
            "temperature": 0.2,
            "timeout": self.settings.CHATBOT_AGENT_TIMEOUT + 60,
        }
        
    
    # ... rest of the initialization code ...

    # Update the conversational agents initializatio


        
        self.callable_tools_for_autogen: List[Callable[..., Any]] = [
            self._tool_invoke_visualization_agent, 
            self._tool_invoke_bond_analyzer_agent,
            self._tool_invoke_name_conversion_agent, 
            self._tool_invoke_func_group_agent,
            self._tool_invoke_reaction_classifier_agent, 
            self._tool_invoke_disconnection_suggester_agent,
            self._tool_invoke_chemical_props_agent,
            self._tool_invoke_reaction_recipe_agent,
        ]
        
        tool_schemas_for_autogen = []
        for func in self.callable_tools_for_autogen:
            docstring = func.__doc__ or f"Invokes the {func.__name__} capability."
            param_props: Dict[str, Any] = {}
            required_params: List[str] = []

            if func.__name__ == "_tool_invoke_reaction_classifier_agent":
                param_props = {"reaction_smiles": {"type": "string", "description": "The reaction SMILES string (must contain '>>')."}, 
                               "property_query": {"type": "string", "description": "Optional. Specific property like 'yield' or 'temperature'."}}
                required_params = ["reaction_smiles"]
            elif func.__name__ == "_tool_invoke_disconnection_suggester_agent":
                param_props = {"smiles": {"type": "string", "description": "The molecule SMILES string."}, 
                               "functional_groups": {"type": "array", "items": {"type": "string"}, "description": "Optional. List of pre-identified functional groups."}}
                required_params = ["smiles"]
            elif func.__name__ == "_tool_invoke_chemical_props_agent":
                param_props = {"chemical_identifier": {"type": "string", "description": "Name, SMILES, or CAS number of the chemical."}}
                required_params = ["chemical_identifier"]
            elif func.__name__ == "_tool_invoke_visualization_agent" or func.__name__ == "_tool_invoke_func_group_agent":
                param_props = {"smiles_or_reaction": {"type": "string", "description": "The SMILES string of the molecule or reaction."}}
                required_params = ["smiles_or_reaction"]
            elif func.__name__ == "_tool_invoke_bond_analyzer_agent" or func.__name__ == "_tool_invoke_reaction_recipe_agent":
                param_props = {"reaction_smiles": {"type": "string", "description": "The reaction SMILES string (must contain '>>')."}}
                required_params = ["reaction_smiles"]
            elif func.__name__ == "_tool_invoke_name_conversion_agent":
                param_props = {"identifier": {"type": "string", "description": "The chemical name or SMILES string to convert."}}
                required_params = ["identifier"]
            else: 
                param_props = {"input_query": {"type": "string", "description": "Primary input for the tool."}}
                required_params = ["input_query"]
            
            tool_schemas_for_autogen.append({
                "type": "function", 
                "function": {
                    "name": func.__name__, 
                    "description": docstring.splitlines()[0].strip(), 
                    "parameters": {"type": "object", "properties": param_props, "required": required_params}
                }
            })
        
        current_llm_config = {**llm_config_orchestrator, "tools": tool_schemas_for_autogen}

        self.conversational_assistant = autogen.AssistantAgent(
            name="ChemGPT_Orchestrator", 
            llm_config=current_llm_config,
            system_message="Initializing system message...",
            is_termination_msg=lambda x: isinstance(x.get("content"), str) and x.get("content", "").rstrip().endswith("TERMINATE")
        )
        
        self.conversational_user_proxy = autogen.UserProxyAgent(
            name="UserProxy_ChemGPT", 
            human_input_mode="NEVER",
            max_consecutive_auto_reply=self.settings.MAX_CHATBOT_TURNS + 5, 
            code_execution_config=False,
            function_map={func.__name__: func for func in self.callable_tools_for_autogen},
            is_termination_msg=lambda x: isinstance(x.get("content"), str) and x.get("content", "").rstrip().endswith("TERMINATE")
        )
        self._update_conversational_system_message()
        logger.info(f"Conversational Autogen agents initialized. Assistant has {len(tool_schemas_for_autogen)} tools defined.")


    def _build_conversational_system_message(self) -> str:
        global _orchestrator_moi_context
        moi_name = _orchestrator_moi_context.get("name", "Not Set")
        moi_smiles = _orchestrator_moi_context.get("smiles", "Not Set")
        
        tool_descs = []
        if hasattr(self, 'conversational_assistant') and self.conversational_assistant and \
           self.conversational_assistant.llm_config and \
           self.conversational_assistant.llm_config.get("tools"):
            for tool_schema in self.conversational_assistant.llm_config["tools"]:
                if tool_schema["type"] == "function":
                    func_def = tool_schema["function"]
                    fn_name = func_def["name"]; desc = func_def["description"]
                    params_dict = func_def["parameters"]["properties"]
                    param_str = ", ".join([f"{p_name}: {p_info.get('type', 'string')}" for p_name, p_info in params_dict.items()])
                    tool_descs.append(f"- `{fn_name}({param_str})`: {desc}")
        else:
            tool_descs.append("Tool descriptions not available.")

        tools_list_str = "\n".join(tool_descs)

        return f"""You are ChemGPT, a sophisticated AI assistant for chemistry.
Your primary role is to understand a user's chemistry-related query, decide if a tool is needed, call the appropriate '_tool_invoke_...' function, and then provide a response.

Current Molecule of Interest (MOI):
Name: {moi_name}
SMILES: {moi_smiles}

Available functions to invoke specialized agents (tools):
{tools_list_str}

Core Task Resolution Strategy:
1.  **Understand Query:** Identify the chemical entity and task.
2.  **Tool Selection:**
    *   For "full info" or "recipe card" for a **REACTION SMILES**, select `_tool_invoke_reaction_recipe_agent`.
    *   For "full info" or "report" for a **COMPOUND**, select `_tool_invoke_chemical_props_agent`.
    *   For other specific tasks, select the most relevant tool.
3.  **Tool Invocation:** Provide the necessary arguments. For reactions, ensure `reaction_smiles` contains '>>'.
4.  **Response Formulation:**
    *   **If `_tool_invoke_reaction_recipe_agent` is called AND IT SUCCEEDS (returns a JSON string not containing an internal 'error' key): Your final response MUST BE ONLY the JSON string provided by this tool. Do NOT summarize it, reformat it, or add any surrounding text. Just output the JSON string, followed by TERMINATE.**
    *   If any tool returns an error (e.g., a JSON string like '{{"error": "..."}}'), or for any other tool call, provide a concise textual summary of the result or the error, followed by TERMINATE.
    *   If no tool is appropriate or the query is too general, respond with the standard refusal message, followed by TERMINATE.
5.  **MOI Handling:** Use MOI if relevant. Prioritize new entities in the query.
6.  **Termination:** ALWAYS end your textual responses with "TERMINATE". If directly outputting a JSON string as per the rule above, that JSON string itself is the primary content, and then append TERMINATE.
"""

    def _update_conversational_system_message(self):
        if self.conversational_assistant:
            logger.debug("Updating conversational system message with current MOI.")
            self.conversational_assistant.update_system_message(self._build_conversational_system_message())

    def clear_moi_context(self):
        global _orchestrator_moi_context
        logger.info("Clearing MOI context.")
        _orchestrator_moi_context = {"name": None, "smiles": None}

    async def route_query(self, query: str, original_name_for_saving: Optional[str] = None, clear_moi_before_query: bool = False) -> Dict[str, Any]:
        global _orchestrator_moi_context
        logger.info(f"ROUTE_QUERY START: Query='{query}', ClearMOI={clear_moi_before_query}, CurrentMOI={_orchestrator_moi_context}")
        query_start_time = time.time()
        if clear_moi_before_query:
            logger.info("ROUTE_QUERY: Clearing MOI context as requested.")
            self.clear_moi_context()

        priming_pattern = r"(?i)Let's discuss (?:the molecule of interest: |molecule |chemical |compound )?['\"]?(.*?)['\"]? with SMILES ['\"]?(.*?)['\"]?\. Please acknowledge\."
        priming_match = re.match(priming_pattern, query, re.IGNORECASE)
        if priming_match:
            moi_name, moi_smiles = priming_match.group(1).strip(), priming_match.group(2).strip()
            _orchestrator_moi_context["name"] = moi_name
            _orchestrator_moi_context["smiles"] = moi_smiles 
            logger.info(f"ROUTE_QUERY: MOI primed to Name='{moi_name}', SMILES='{moi_smiles}'.")
            ack_reply = f"Acknowledged. We are now discussing {moi_name} ({moi_smiles}). How can I assist you further?" 
            logger.info(f"ROUTE_QUERY END (Priming): Took {time.time() - query_start_time:.2f}s. Reply: '{ack_reply}'")
            return {"analysis": ack_reply, "visualization_path": None, "processed_smiles": moi_smiles, "analysis_context": "moi_primed_ack", "error": None, "current_moi_name": moi_name, "current_moi_smiles": moi_smiles }

        extracted_reaction_smiles = self._extract_reaction_smiles(query)
        if extracted_reaction_smiles:
            if _orchestrator_moi_context.get("smiles") != extracted_reaction_smiles: _orchestrator_moi_context["name"] = None
            _orchestrator_moi_context["smiles"] = extracted_reaction_smiles
            logger.info(f"ROUTE_QUERY: MOI SMILES (Reaction) set/updated from query to: {extracted_reaction_smiles}")
        else:
            extracted_compound_smiles = self._extract_single_compound_smiles(query)
            if extracted_compound_smiles:
                if _orchestrator_moi_context.get("smiles") != extracted_compound_smiles: _orchestrator_moi_context["name"] = None
                _orchestrator_moi_context["smiles"] = extracted_compound_smiles
                logger.info(f"ROUTE_QUERY: MOI SMILES (Compound) set/updated from query to: {extracted_compound_smiles}")
        
        if not self.conversational_user_proxy or not self.conversational_assistant:
            logger.error("ROUTE_QUERY: Orchestrator's conversational agent not initialized.")
            # Ensure consistent error structure for AnalysisResponse
            err_analysis = {"error": "Orchestrator agent not initialized."}
            return {"analysis": err_analysis, "error": "Agent not initialized.", "current_moi_name": _orchestrator_moi_context.get("name"), "current_moi_smiles": _orchestrator_moi_context.get("smiles")}


        self.conversational_user_proxy.reset(); self.conversational_assistant.reset()
        self._update_conversational_system_message()
        logger.info("ROUTE_QUERY: Conversational agents reset and system message updated.")

        final_response_for_api: Any = {"error": "Orchestrator did not formulate a response."} # Default to error dict
        visualization_path_from_tool_output = None
        analysis_context_for_filename = "orchestrator_general_response"
        
        try:
            logger.info(f"ROUTE_QUERY: Initiating chat with assistant for query: '{query}'")
            await self.conversational_user_proxy.a_initiate_chat(
                recipient=self.conversational_assistant, message=query, max_turns=self.settings.MAX_CHATBOT_TURNS + 3
            )
            
            llm_final_output_str = "Orchestrator did not provide a clear textual response."
            last_msg_assistant_perspective = self.conversational_assistant.last_message(self.conversational_user_proxy)
            if last_msg_assistant_perspective and last_msg_assistant_perspective.get("content"):
                llm_final_output_str = str(last_msg_assistant_perspective.get("content")).strip()
                if llm_final_output_str.upper().endswith("TERMINATE"):
                    llm_final_output_str = llm_final_output_str[:-len("TERMINATE")].strip()
            
            conv_history = self.conversational_user_proxy.chat_messages.get(self.conversational_assistant, [])
            last_tool_name_called = None
            raw_tool_output_content_for_recipe = None

            for msg in reversed(conv_history):
                if msg.get("role") == "tool" and msg.get("name") == "_tool_invoke_reaction_recipe_agent":
                    raw_tool_output_content_for_recipe = str(msg.get("content", ""))
                    last_tool_name_called = "_tool_invoke_reaction_recipe_agent"
                    logger.info(f"ROUTE_QUERY: Found raw output from _tool_invoke_reaction_recipe_agent: {raw_tool_output_content_for_recipe[:200]}...")
                    break
                elif msg.get("role") == "assistant" and msg.get("tool_calls"):
                    if msg["tool_calls"][0].get("function"):
                        last_tool_name_called = msg["tool_calls"][0].get("function", {}).get("name")
                    # If assistant's last action was to call a tool, but we haven't seen the tool's output yet,
                    # this means the conversation might have ended before tool output was processed by assistant.
                    # However, for recipe tool, we prioritize its direct output.
                    if last_tool_name_called != "_tool_invoke_reaction_recipe_agent": # Only break if it's not the recipe tool
                        break
            
            if last_tool_name_called == "_tool_invoke_reaction_recipe_agent" and raw_tool_output_content_for_recipe:
                logger.info("ROUTE_QUERY: Last tool was recipe agent. Attempting to use its direct JSON output.")
                try:
                    parsed_recipe_json = json.loads(raw_tool_output_content_for_recipe)
                    if isinstance(parsed_recipe_json, dict) and "error" not in parsed_recipe_json:
                        final_response_for_api = parsed_recipe_json
                        analysis_context_for_filename = "reaction_recipe_json_direct"
                        logger.info("ROUTE_QUERY: Successfully set direct JSON recipe as final API response.")
                    elif isinstance(parsed_recipe_json, dict) and "error" in parsed_recipe_json:
                        final_response_for_api = parsed_recipe_json
                        analysis_context_for_filename = "reaction_recipe_tool_error"
                        logger.warning(f"ROUTE_QUERY: Reaction recipe tool returned an error dict: {final_response_for_api}")
                    else: 
                        logger.warning("ROUTE_QUERY: Parsed recipe tool output, but not expected dict. Using LLM's textual response.")
                        final_response_for_api = llm_final_output_str
                except json.JSONDecodeError:
                    logger.error(f"ROUTE_QUERY: Failed to parse recipe tool's output string as JSON. Output: '{raw_tool_output_content_for_recipe[:200]}...'. Using LLM's textual response.")
                    final_response_for_api = llm_final_output_str
                    analysis_context_for_filename = "reaction_recipe_json_parse_fail_fallback_text"
            else:
                logger.info(f"ROUTE_QUERY: Recipe tool not identified as primary action or its output missing. Using LLM's final response: '{llm_final_output_str[:100]}'")
                final_response_for_api = llm_final_output_str
                if isinstance(final_response_for_api, str) and \
                   final_response_for_api.strip().startswith("{") and \
                   final_response_for_api.strip().endswith("}"):
                    try:
                        parsed_llm_json = json.loads(final_response_for_api)
                        # Check if it's a recipe-like structure to set context correctly
                        if isinstance(parsed_llm_json, dict) and "reaction_smiles_interpreted" in parsed_llm_json:
                             final_response_for_api = parsed_llm_json # Use the parsed dict
                             analysis_context_for_filename = "reaction_recipe_llm_direct_json"
                             logger.info("ROUTE_QUERY: LLM's final response was a parsable JSON recipe.")
                        # else, it might be some other JSON, keep it as dict
                        elif isinstance(parsed_llm_json, dict):
                            final_response_for_api = parsed_llm_json
                            logger.info("ROUTE_QUERY: LLM's final response was a parsable JSON (not recipe).")

                    except json.JSONDecodeError:
                        logger.info("ROUTE_QUERY: LLM's final response looked like JSON but failed to parse, keeping as string.")
                        pass 

            for msg in conv_history:
                if msg.get("role") == "tool" and msg.get("name") == "_tool_invoke_visualization_agent":
                    tool_reply_content = str(msg.get("content", ""))
                    viz_match_tool = re.search(r"Path:\s*(/static/autogen_visualizations/[\w\-\.\_]+\.png)", tool_reply_content, re.IGNORECASE)
                    if viz_match_tool:
                        visualization_path_from_tool_output = viz_match_tool.group(1)
                        logger.info(f"Found visualization path from tool output: {visualization_path_from_tool_output}")
                        break
            
            smiles_for_saving = _orchestrator_moi_context.get("smiles")
            name_for_saving = original_name_for_saving or _orchestrator_moi_context.get("name")
            
            if isinstance(final_response_for_api, dict) and "reaction_smiles_interpreted" in final_response_for_api: # If it is a recipe
                 if analysis_context_for_filename not in ["reaction_recipe_json_direct", "reaction_recipe_llm_direct_json", "reaction_recipe_tool_error"]:
                    analysis_context_for_filename = f"reaction_recipe_final_{self._sanitize_filename_part(smiles_for_saving or 'unknown')}"
            elif ("full info" in query.lower() or "report for" in query.lower()) and isinstance(final_response_for_api, str) and (name_for_saving or smiles_for_saving):
                 analysis_context_for_filename = f"compound_report_final_{self._sanitize_filename_part(name_for_saving or smiles_for_saving or 'unknown')}"

            should_save = True
            if isinstance(final_response_for_api, str):
                if "I am a specialized chemistry AI" in final_response_for_api or \
                   "Orchestrator did not formulate" in final_response_for_api or \
                   (len(final_response_for_api) < 70 and "error" in final_response_for_api.lower()):
                    should_save = False
            elif isinstance(final_response_for_api, dict) and "error" in final_response_for_api and len(final_response_for_api.keys()) == 1:
                should_save = False

            if should_save:
                 self._save_analysis(smiles_for_saving or "unknown_entity", final_response_for_api, analysis_context_for_filename, name_for_saving)

        except Exception as e:
            tb_str = traceback.format_exc()
            logger.error(f"ROUTE_QUERY: EXCEPTION during query processing: {e}\n{tb_str}", exc_info=True)
            final_response_for_api = {"error": f"An unhandled error occurred in the orchestrator: {str(e)}"}
            # Use AnalysisResponse model for consistent error structure
            from app.api.schemas.general_models import AnalysisResponse # Local import for type safety
            return AnalysisResponse(
                analysis=final_response_for_api, # This is now a dict {"error": ...}
                error=str(e), # Top-level error
                current_moi_name=_orchestrator_moi_context.get("name"),
                current_moi_smiles=_orchestrator_moi_context.get("smiles"),
                session_id=_orchestrator_moi_context.get("session_id_placeholder") 
            ).model_dump(exclude_none=True)

        logger.info(f"ROUTE_QUERY END: Total time {time.time() - query_start_time:.2f}s. Response type: {type(final_response_for_api).__name__}. Visualization: {visualization_path_from_tool_output}")
        
        current_error_for_response = None
        if isinstance(final_response_for_api, dict) and "error" in final_response_for_api:
            current_error_for_response = final_response_for_api["error"]
        
        from app.api.schemas.general_models import AnalysisResponse # Local import for type safety
        return AnalysisResponse(
            analysis=final_response_for_api,
            visualization_path=visualization_path_from_tool_output,
            processed_smiles=_orchestrator_moi_context.get("smiles"),
            analysis_context=analysis_context_for_filename,
            error=current_error_for_response,
            current_moi_name=_orchestrator_moi_context.get("name"),
            current_moi_smiles=_orchestrator_moi_context.get("smiles"),
            session_id=_orchestrator_moi_context.get("session_id_placeholder") # Ensure this key exists or handle None
        ).model_dump(exclude_none=True)


    def _sanitize_filename_part(self, name_part: Optional[str]) -> str:
        if not name_part: return "unknown"
        sanitized = re.sub(r'[^\w\.\-]+', '_', name_part)
        return sanitized[:75]

    def _save_analysis(self, entity_identifier: str, analysis_data: Any, query_context_type: str, original_name: Optional[str]):
        if not analysis_data:
            logger.warning(f"SAVE_ANALYSIS: Skipping save, analysis_data is empty.")
            return
        
        timestamp = time.strftime("%Y%m%d_%H%M%S")
        safe_entity_id = self._sanitize_filename_part(entity_identifier)
        safe_original_name = self._sanitize_filename_part(original_name)
        safe_context = self._sanitize_filename_part(query_context_type)
        
        filename_parts = []
        if safe_original_name and safe_original_name != "unknown" and safe_original_name.lower() != safe_entity_id.lower():
            filename_parts.append(safe_original_name)
        
        if safe_entity_id != "unknown":
            if not filename_parts or (safe_original_name and safe_original_name.lower() != safe_entity_id.lower()):
                 filename_parts.append(safe_entity_id)
            elif not filename_parts:
                 filename_parts.append(safe_entity_id)
        elif not filename_parts:
            filename_parts.append("unknown_entity_data")
            
        filename_parts.append(safe_context); filename_parts.append(timestamp)
        
        is_json_data = isinstance(analysis_data, dict)
        file_extension = ".json" if is_json_data else ".txt"
        filename = "_".join(filter(None, filename_parts)) + file_extension
        filepath = os.path.join(self.analysis_output_dir, filename)

        try:
            with open(filepath, "w", encoding="utf-8") as f:
                if is_json_data:
                    json.dump(analysis_data, f, indent=2)
                else: 
                    f.write(f"Entity Identifier (SMILES/Reaction): {entity_identifier}\n")
                    if original_name: f.write(f"Original Context Name: {original_name}\n")
                    f.write(f"Analysis Context Type: {query_context_type}\nTimestamp: {timestamp}\n")
                    f.write("="*50 + "\n\n" + str(analysis_data))
            logger.info(f"SAVE_ANALYSIS: Saved analysis to: {filepath}")
        except Exception as e:
            logger.error(f"SAVE_ANALYSIS: Error saving {filepath}: {e}", exc_info=True)

    def _update_conversational_system_message(self): # Copied from previous correct version
        if self.conversational_assistant:
            logger.debug("Updating conversational system message with current MOI.")
            self.conversational_assistant.update_system_message(self._build_conversational_system_message())

    def clear_moi_context(self): # Copied from previous correct version
        global _orchestrator_moi_context
        logger.info("Clearing MOI context.")
        _orchestrator_moi_context = {"name": None, "smiles": None}