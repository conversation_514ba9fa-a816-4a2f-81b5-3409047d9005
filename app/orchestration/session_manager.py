# This is a very basic in-memory session manager.
# For production, you'd use a database (Redis, SQL, NoSQL).
import uuid
from typing import Any, Dict, Optional

_sessions: Dict[str, Dict[str, Any]] = {} # session_id -> session_data

class SessionManager:
    def create_session(self) -> str:
        session_id = str(uuid.uuid4())
        _sessions[session_id] = {
            "moi_context": {"name": None, "smiles": None},
            "chat_history": [], # If you want to store chat history separately
            # Add other session-specific data here
        }
        # print(f"Session created: {session_id}")
        return session_id

    def get_session_data(self, session_id: str) -> Optional[Dict[str, Any]]:
        return _sessions.get(session_id)

    def update_moi_context(self, session_id: str, name: Optional[str], smiles: Optional[str]):
        if session_id in _sessions:
            _sessions[session_id]["moi_context"]["name"] = name
            _sessions[session_id]["moi_context"]["smiles"] = smiles
        else:
            print(f"Warning: Attempted to update MOI for non-existent session: {session_id}")
    
    def get_moi_context(self, session_id: str) -> Optional[Dict[str, Optional[str]]]:
        session = self.get_session_data(session_id)
        return session.get("moi_context") if session else None

    def clear_session_moi(self, session_id: str):
        if session_id in _sessions:
             _sessions[session_id]["moi_context"] = {"name": None, "smiles": None}
             # Optionally clear chat history too
             # _sessions[session_id]["chat_history"] = []
        
    def delete_session(self, session_id: str):
        if session_id in _sessions:
            del _sessions[session_id]
            # print(f"Session deleted: {session_id}")

# Global instance or injected via dependency injection
# session_manager_instance = SessionManager() 
# print("AGENT_HUB.app.orchestrations.session_manager module loaded")
