"""Workflow registry for agent orchestration."""

from typing import Dict, Type
from .workflows.base import BaseWorkflow
from .workflows.patent_workflow import PatentWorkflow

class WorkflowRegistry:
    """Central registry for all available workflows."""
    
    _instance = None
    
    def __new__(cls):
        if cls._instance is None:
            cls._instance = super(WorkflowRegistry, cls).__new__(cls)
            cls._instance._workflows = {}
            cls._instance._initialize_workflows()
        return cls._instance
    
    def _initialize_workflows(self):
        """Register all available workflows."""
        self.register_workflow("patent_processing", PatentWorkflow)
    
    def register_workflow(self, name: str, workflow_class: Type[BaseWorkflow]):
        """Register a new workflow."""
        self._workflows[name] = workflow_class
    
    def get_workflow(self, name: str) -> Type[BaseWorkflow]:
        """Get workflow by name."""
        return self._workflows.get(name)
    
    @property
    def available_workflows(self) -> Dict[str, Type[BaseWorkflow]]:
        """Get all registered workflows."""
        return self._workflows.copy()