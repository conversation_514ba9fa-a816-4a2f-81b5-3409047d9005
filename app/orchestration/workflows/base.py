"""Base workflow definition."""

from abc import ABC, abstractmethod
from typing import Dict, Any
from ...utils.logging import get_logger

class BaseWorkflow(ABC):
    """Base class for all workflows."""
    
    def __init__(self):
        self.logger = get_logger(self.__class__.__name__)
    
    @abstractmethod
    async def execute(self, **kwargs) -> Dict[str, Any]:
        """Execute the workflow."""
        pass
    
    @abstractmethod
    def validate_input(self, **kwargs) -> bool:
        """Validate workflow inputs."""
        pass