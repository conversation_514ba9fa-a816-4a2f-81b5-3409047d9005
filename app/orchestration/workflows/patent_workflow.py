"""Patent processing workflow implementation."""

import os,json
import datetime,time
from typing import Dict, Any, List
from .base import BaseWorkflow
from app.tools.patent.patent_search_service import PatentSearchService
from app.tools.patent.chemical_converter import ChemicalConverter
from app.tools.patent.patent_processor import PatentProcessor
from app.tools.patent.patent_preprocessor import PatentPreprocessor
from app.tools.patent.entity_alignment_service import EntityAlignmentService
from app.tools.patent.serp_patent_service import SerpPatentService
from app.config.settings import get_settings
from ...models.patent_models import ProcessingStatus
from ...utils.logging import get_logger
from app.utils.es_helper import *
from app.utils.context_helpers import request_id_var
import threading
from concurrent.futures import ThreadPoolExecutor, as_completed
import traceback


class PatentWorkflow(BaseWorkflow):
    """Workflow for patent processing and analysis."""
    
    def __init__(self):
        super().__init__()
        self.search_service = PatentSearchService()
        self.converter = ChemicalConverter()
        self.processor = PatentProcessor()
        self.preprocessor = PatentPreprocessor()
        self.aligner = EntityAlignmentService()
        self.serp_service = SerpPatentService()
        self.app_config = get_settings()
        self.max_parallel_workers = 5
        self.logger = get_logger(self.__class__.__name__)

    def execute(self, kwargs) -> Dict[str, Any]:
        """Execute the patent processing workflow."""
        self.logger.info("Starting Patent Workflow execution")
        try:
            if not self.validate_input(kwargs):
                return self._create_error_response("Missing required parameters")

            # Extract and validate parameters
            params = self._extract_parameters(kwargs)
            
            # # Check if data exists in ES when chemical name is provided
            # if params["chemical_name"]:
            #     es_data = self._search_existing_patents(es_client, params["chemical_name"])
            #     if es_data:
            #         self.logger.info(f"Found existing patents in ES for chemical name: {params['chemical_name']}")
            #         return self._process_existing_patents(es_client, es_data, params)

            # Get patent IDs either from direct input or search
            if params.get("patent_id"):
                self.logger.info(f"Processing single patent ID: {kwargs['patent_id']}")
                patent_ids = [kwargs["patent_id"]]
            else:
                patent_ids = self._get_patent_ids(params)
                if not patent_ids:
                    return self._create_error_response("No patents found to process")
            patent_ids = list(set(patent_ids))
            results = self._initialize_results(params, patent_ids)
            
            # Fetch and analyze patents
            self._fetch_and_stream_process(patent_ids, results, params)
            # patent_summary = self._fetch_patent_details_parallel(patent_ids)
            # self.logger.info(f"Fetched details for {len(patent_ids)} patents")
            
            # # Process patent relevance and store in ES
            # relevant_patents = self._process_patent_relevance(
            #      patent_summary, params, patent_ids
            # )
            
            # # Process patents and update results
            # self._process_patents(relevant_patents, results, params)
            logger.info("Patent Workflow execution completed")
            return results

        except Exception as e:
            self.logger.error(f"Patent Workflow execution failed: {str(e)} {traceback.format_exc()}")
            return self._create_error_response(str(e))

    def _process_existing_patents(self, _, es_data, params) -> Dict[str, Any]:
        """Process existing patents found in Elasticsearch."""
        self.logger.info(f"Found {len(es_data)} existing patents for chemical name: {params['chemical_name']}")
        
        # Extract patent IDs from ES data
        patent_ids = [doc['patent_id'] for doc in es_data]
        if not patent_ids:
            return self._create_error_response("No relevant patents found in Elasticsearch")

        # Initialize results structure
        results = self._initialize_results(params, patent_ids)
        
        # Process patents and update results
        self._process_patents(patent_ids, results, params)
        
        return results

    def _extract_parameters(self, kwargs) -> Dict[str, Any]:
        """Extract and return workflow parameters."""
        return {
            "material": kwargs.get("material"),
            "request_id": kwargs.get("request_id"),
            "chemical_name": kwargs.get("chemical_name"),
            "patent_id": kwargs.get("patent_id"),
            "max_results": kwargs.get("max_results", 10),
            "model_type": kwargs.get("model_type", "gemini"),
            "enable_preprocessing": kwargs.get("enable_preprocessing", True),
            "enable_alignment": kwargs.get("enable_alignment", True),
            "debug_mode": kwargs.get("debug_mode", False),
            "disable_cache": kwargs.get("disable_cache", False),
            "target_smiles": kwargs.get("target_smiles"),
        }

    def _search_existing_patents(self, chemical_name):
        """Search for existing patents in elasticsearch."""
        return self.es_client.search_documents(
            query={"query": {"bool": {"must": [{"match": {"chemical_name": chemical_name}}]}}}
        )

    def _get_patent_ids(self, params) -> List[str]:
        """Get patent IDs either from direct input or by searching."""
        if params["patent_id"]:
            return [params["patent_id"]]
    
        if params.get("target_smiles"):
            smiles = params["target_smiles"]
        else:
            smiles = self._convert_to_smiles(params["material"])
        # Store SMILES in params for later use
        params["smiles"] = smiles
        if smiles:
            return self.search_service.search_patents_by_smiles(
                smiles=smiles,
                max_results=params["max_results"]
            )
        return []

    def _convert_to_smiles(self, material) -> str:
        """Convert material to SMILES notation."""
        if not material:
            return None
            
        self.logger.info(f"Converting material to SMILES: {material}")
        success, result = self.converter.convert(material)
        if success:
            self.logger.info(f"Successfully converted to SMILES: {result}")
            return result
        self.logger.warning(f"Could not convert to SMILES: {result}")
        return material

    def _initialize_results(self, params, patent_ids) -> Dict[str, Any]:
        """Initialize the results structure."""
        return {
            "metadata": {
                "material": params["material"],
                "patent_id": params["patent_id"],
                "smiles": params.get("smiles"),
                "model_type": params["model_type"],
                "patents_found": len(patent_ids),
                "preprocessing_enabled": params["enable_preprocessing"],
                "alignment_enabled": params["enable_alignment"]
            },
            "patents": {},
            "summary": {
                "total_patents": len(patent_ids),
                "processed_patents": 0,
                "failed_patents": 0
            },
            "status": ProcessingStatus.COMPLETED
        }

    def _process_patent_relevance(self, patent_summary, params, patent_ids) -> List[str]:
        """Process patent relevance and store in ES."""
        batch_data = [
            (pid, params["chemical_name"], params.get("smiles"), details)
            for pid, details in patent_summary.items()
        ]
        
        relevance_data = self.preprocessor.analyze_patents_batch_optimized(patent_data=batch_data, params=params)
    
             
        return [pid for pid in patent_ids if relevance_data.get(pid, {}).get("relevancyScore", 0)> 5]

    def _process_patents(self, patent_ids, results, params):
        """Process patents and update results."""
        processor = PatentProcessor(
            model_type=params["model_type"],
            cache_enabled=not params["disable_cache"]
        )

        for patent_id in patent_ids:
            try:
                self.logger.info(f"Processing patent: {patent_id}")
                patent_content = f"Patent {patent_id} content would be fetched here"
                patent_result = processor.process_patent(patent_id, patent_content)
                results["patents"][patent_id] = patent_result
                results["summary"]["processed_patents"] += 1
            except Exception as e:
                self.logger.error(f"Failed to process patent {patent_id}: {str(e)}")
                results["patents"][patent_id] = {"error": str(e)}
                results["summary"]["failed_patents"] += 1

    def _create_error_response(self, error_message: str) -> Dict[str, Any]:
        """Create an error response dictionary."""
        return {
            "status": ProcessingStatus.FAILED,
            "error": error_message
        }

    def _fetch_patent_details_parallel(self, patent_ids: List[str]) -> Dict[str, Dict]:
        """Fetch patent details in parallel using ThreadPoolExecutor."""
        patent_details = {}
        details_lock = threading.Lock()
        completed = 0
        total_patents = len(patent_ids)

        def fetch_single_patent(patent_id: str):
            nonlocal completed
            try:
                details = self.serp_service.get_patent_details(patent_id)
                with details_lock:
                    completed += 1
                    if details:
                        patent_details[patent_id] = details
                        print(f"      📄 SERP {completed}/{total_patents}: {patent_id} ✅")
                    else:
                        print(f"      📄 SERP {completed}/{total_patents}: {patent_id} ❌")
            except Exception as e:
                with details_lock:
                    completed += 1
                    print(f"      📄 SERP {completed}/{total_patents}: {patent_id} ❌ Error: {str(e)}")
                self.logger.error(f"Error fetching SERP details for {patent_id}: {str(e)}")

        # Use parallel processing for SERP fetching
        with ThreadPoolExecutor(max_workers=self.max_parallel_workers) as executor:
            futures = [executor.submit(fetch_single_patent, patent_id) for patent_id in patent_ids]
            for future in as_completed(futures):
                try:
                    future.result()
                except Exception as e:
                    self.logger.error(f"Unexpected error in SERP fetch future: {str(e)}")

        return patent_details
    
    def _fetch_and_stream_process(self, patent_ids: List[str], results, params):
        batch = []
        batch_lock = threading.Lock()
        completed = 0
        total_patents = len(patent_ids)
        stop_event = threading.Event()
        BATCH_SIZE = 200
        BATCH_TIMEOUT = 5  # seconds

        def fetch_single_patent(patent_id):
            nonlocal completed
            try:
                details = self.serp_service.get_patent_details(patent_id)
                with batch_lock:
                    completed += 1
                    if details:
                        batch.append((patent_id, details))
                        print(f"📄 SERP {completed}/{total_patents}: {patent_id} ✅")
                    else:
                        print(f"📄 SERP {completed}/{total_patents}: {patent_id} ❌")
            except Exception as e:
                with batch_lock:
                    completed += 1
                self.logger.error(f"Error fetching patent {patent_id}: {str(e)}")

        def batch_processor():
            while not stop_event.is_set() or batch:
                time.sleep(BATCH_TIMEOUT)
                with batch_lock:
                    if batch:
                        mini_batch = batch[:BATCH_SIZE]
                        del batch[:BATCH_SIZE]
                    else:
                        mini_batch = []

                if mini_batch:
                    # Prepare inputs for batch processing
                    batch_dict = {pid: detail for pid, detail in mini_batch}
                    relevant = self._process_patent_relevance(
                        batch_dict, params, list(batch_dict.keys())
                    )
                    self._process_patents(relevant, results, params)

        # Start batch processor thread
        processor_thread = threading.Thread(target=batch_processor)
        processor_thread.start()

        # Start parallel fetch
        with ThreadPoolExecutor(max_workers=self.max_parallel_workers) as executor:
            futures = [executor.submit(fetch_single_patent, pid) for pid in patent_ids]
            for future in as_completed(futures):
                future.result()

        # All fetches done
        stop_event.set()
        processor_thread.join()

    
    def validate_input(self, kwargs) -> bool:
        """Validate workflow inputs."""
        # Check for either material or patent_id
        has_material = bool(kwargs.get("material"))
        has_patent_id = bool(kwargs.get("patent_id"))
        return has_material or has_patent_id