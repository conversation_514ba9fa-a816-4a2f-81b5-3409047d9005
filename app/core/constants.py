"""Constants for Patent2Reaction."""

from enum import Enum


class ModelType(str, Enum):
    """Supported AI models."""
    GEMINI = "gemini"
    GPT = "gpt"
    BOTH = "both"


class ProcessingStatus(str, Enum):
    """Processing status values."""
    PENDING = "pending"
    PROCESSING = "processing"
    COMPLETED = "completed"
    FAILED = "failed"


# API Configuration
DEFAULT_API_HOST = "0.0.0.0"
DEFAULT_API_PORT = 8000
API_TITLE = "Patent2Reaction API"
API_DESCRIPTION = "AI-powered chemical reaction extraction from patents"
API_VERSION = "1.0.0"

# Processing Configuration
DEFAULT_NUM_RESULTS = 5
MAX_NUM_RESULTS = 50
DEFAULT_BATCH_SIZE = 3
MAX_CONTENT_LENGTH = 300000

# Cache Configuration
DEFAULT_CACHE_FILE = "patent2reaction_cache.json"
DEFAULT_PREPROCESSING_CACHE = "preprocessing_cache.json"

# Model Configuration
GEMINI_MODEL = "gemini-2.5-pro-preview-05-06"
GEMINI_FLASH_MODEL = "gemini-2.5-flash-preview-05-20"
GPT_MODEL = "gpt-4"

# Azure Search Configuration
AZURE_SEARCH_URL = "https://mstack-reactions-search-service.search.windows.net/indexes/molecule_patents/docs"

# Retry Configuration
DEFAULT_MAX_RETRIES = 3
DEFAULT_RETRY_DELAY = 2

# Logging Configuration
LOG_FORMAT = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
LOG_DATE_FORMAT = "%Y-%m-%d %H:%M:%S"
