"""Custom exceptions for Patent2Reaction."""


class Patent2ReactionError(Exception):
    """Base exception for Patent2Reaction."""
    
    def __init__(self, message: str, error_code: str = None):
        self.message = message
        self.error_code = error_code
        super().__init__(self.message)


class APIError(Patent2ReactionError):
    """Exception for API-related errors."""
    
    def __init__(self, message: str, status_code: int = 500, error_code: str = None):
        self.status_code = status_code
        super().__init__(message, error_code)


class ProcessingError(Patent2ReactionError):
    """Exception for processing-related errors."""


class ConfigurationError(Patent2ReactionError):
    """Exception for configuration-related errors."""


class ValidationError(Patent2ReactionError):
    """Exception for validation-related errors."""


class CacheError(Patent2ReactionError):
    """Exception for cache-related errors."""


class ModelError(Patent2ReactionError):
    """Exception for AI model-related errors."""
    
    def __init__(self, message: str, model_type: str = None, error_code: str = None):
        self.model_type = model_type
        super().__init__(message, error_code)


class PatentSearchError(Patent2ReactionError):
    """Exception for patent search-related errors."""


class ChemicalConversionError(Patent2ReactionError):
    """Exception for chemical name/SMILES conversion errors."""


class PatentPreprocessingError(Patent2ReactionError):
    """Exception for patent preprocessing errors."""


class PatentProcessingError(Patent2ReactionError):
    """Exception for patent processing errors."""


class EntityAlignmentError(Patent2ReactionError):
    """Exception for entity alignment errors."""

class AgentRegistryError(Patent2ReactionError):
    """Exception for agent registry-related errors."""
    
    def __init__(self, message: str, agent_name: str = None, error_code: str = None):
        self.agent_name = agent_name
        super().__init__(message, error_code)