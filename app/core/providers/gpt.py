"""GPT client wrapper."""

import openai
from typing import Optional
from app.utils.logging import get_logger

class GPTClient:
    """Simple GPT client wrapper."""
    
    def __init__(self, api_key: str, temperature: float = 1.0):
        """Initialize GPT client."""
        self.client = openai.AzureOpenAI()
        self.temperature = temperature
        self.logger = get_logger(__name__)
    
    def answer_wo_vision(self, prompt: str, content: str) -> str:
        """Generate response without vision."""
        try:
            response = self.client.chat.completions.create(
                model="gpt-4",
                messages=[
                    {"role": "system", "content": prompt},
                    {"role": "user", "content": content}
                ],
                temperature=self.temperature
            )
            self.logger.info("GPT response generated successfully")
            return response.choices[0].message.content
        except Exception as e:
            raise Exception(f"GPT API error: {str(e)}")
