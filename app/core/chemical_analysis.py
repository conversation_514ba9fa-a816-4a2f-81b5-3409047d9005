import os
import re
import json
from typing import Optional, List, Dict, Any, Tuple
from dataclasses import dataclass, field
from enum import Enum
from datetime import datetime
import logging

import pubchempy as pcp
import requests
from rdkit import Chem, RDLogger

# --- OpenAI/RDKit import handling ---
try:
    import openai
except ImportError:
    openai = None # type: ignore

RDLogger.DisableLog('rdApp.*') # Disable RDKit warnings

from ..config.settings import Settings # Agent-hub's settings

logger = logging.getLogger(__name__)
# Basic configuration if no handlers are attached to the root logger yet.
# This is helpful if this module is run or imported before main app logging is set up.
if not logging.getLogger().hasHandlers():
    logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')

# --- Enums and Dataclasses ---
class HazardLevel(Enum):
    LOW = "Low"; MODERATE = "Moderate"; HIGH = "High"; EXTREME = "Extreme"; UNKNOWN = "Unknown"

class SolubilityType(Enum):
    WATER_SOLUBLE = "Water Soluble"; ORGANIC_SOLUBLE = "Organic Soluble"
    POORLY_SOLUBLE = "Poorly Soluble"; INSOLUBLE = "Insoluble"; UNKNOWN = "Unknown"

# These common auxiliaries are used by ReactionRecipeTool, but defined here for broader use if needed.
COMMON_AUXILIARIES_BY_NAME_LOWER = {
    "thionyl chloride", "hydrochloric acid", "sulfuric acid", "nitric acid", "socl2",
    "sodium hydroxide", "potassium hydroxide", "water", "dichloromethane", "hcl",
    "chloroform", "methanol", "ethanol", "diethyl ether", "tetrahydrofuran", 
    "toluene", "benzene", "dmso", "dmf", "acetonitrile", "acetic acid",
    "sodium bicarbonate", "sodium carbonate", "potassium carbonate", "magnesium sulfate",
    "sodium sulfate", "sodium chloride", "triethylamine", "pyridine", "diisopropylethylamine",
    "hydrogen peroxide", "sodium borohydride", "lithium aluminum hydride"}

COMMON_AUXILIARIES_BY_SMILES = {
    "O=S(Cl)Cl", "Cl", "OS(=O)(=O)O", "[N+](=O)([O-])O", "O", "[Na+].[OH-]", "[K+].[OH-]",
    "ClCCl", "ClC(Cl)Cl", "CO", "CCO", "CCOCC", "C1COCC1", "Cc1ccccc1", "c1ccccc1",
    "CS(=O)C", "CN(C)C=O", "CC#N"}

@dataclass
class ChemicalPropertiesData:
    name: str
    original_query: Optional[str] = None
    iupac_name: Optional[str] = None
    common_names: List[str] = field(default_factory=list)
    molecular_formula: Optional[str] = None
    molecular_weight: Optional[float] = None
    cas_number: Optional[str] = None
    smiles: Optional[str] = None
    pubchem_cid: Optional[int] = None
    solubility: Dict[str, Any] = field(default_factory=dict)
    hazard_level: HazardLevel = HazardLevel.UNKNOWN
    is_corrosive: Optional[bool] = None
    is_flammable: Optional[bool] = None
    is_toxic: Optional[bool] = None
    ghs_hazards: List[Dict[str, str]] = field(default_factory=list)
    green_chemistry_score: Optional[int] = None
    estimated_price_per_kg: Optional[float] = None
    price_currency: Optional[str] = None
    supplier_info: List[Dict[str, Any]] = field(default_factory=list)
    safety_notes: List[str] = field(default_factory=list)
    environmental_impact: Optional[str] = None
    hazard_rating: Optional[int] = None
    solubility_rating: Optional[int] = None
    pubchem_full_json: Optional[Dict[str, Any]] = None

class ChemicalAnalysisLogic:
    USD_TO_INR_RATE_DEFAULT = 83.0

    def __init__(self, settings: Settings, llm_provider_override: Optional[str] = None):
        self.settings = settings
        
        # Get DEFAULT_LLM_PROVIDER and sanitize it immediately
        raw_default_provider = self.settings.DEFAULT_LLM_PROVIDER
        clean_default_provider = raw_default_provider.split('#')[0].strip().replace('"', '').replace("'", "")
        if clean_default_provider not in ["openai", "perplexity"]:
            logger.warning(f"[ChemicalAnalysisLogic Init Warn] DEFAULT_LLM_PROVIDER ('{raw_default_provider}' -> '{clean_default_provider}') is not 'openai' or 'perplexity'. Defaulting to 'openai'.")
            clean_default_provider = "openai"
        
        self.llm_provider = (llm_provider_override or clean_default_provider).strip() # Ensure override is also clean
        if self.llm_provider not in ["openai", "perplexity"]:
            logger.warning(f"[ChemicalAnalysisLogic Init Warn] Final llm_provider ('{self.llm_provider}') is not 'openai' or 'perplexity'. Defaulting to 'openai'.")
            self.llm_provider = "openai"


        self.openai_api_key_val = self.settings.OPENAI_API_KEY
        self.perplexity_api_key_val = self.settings.PERPLEXITY_API_KEY
        
        logger.info(f"[ChemicalAnalysisLogic Init] Determined LLM Provider for initialization: '{self.llm_provider}'")
        logger.info(f"[ChemicalAnalysisLogic Init] OpenAI API Key from settings is set: {bool(self.openai_api_key_val and self.openai_api_key_val.lower() != 'none')}")
        logger.info(f"[ChemicalAnalysisLogic Init] Perplexity API Key from settings is set: {bool(self.perplexity_api_key_val and self.perplexity_api_key_val.lower() != 'none')}")

        try:
            self.usd_to_inr_rate = float(getattr(self.settings, 'USD_TO_INR_RATE', self.USD_TO_INR_RATE_DEFAULT))
        except (ValueError, TypeError):
            self.usd_to_inr_rate = self.USD_TO_INR_RATE_DEFAULT
            logger.warning(f"[ChemicalAnalysisLogic Init Warn] USD_TO_INR_RATE in settings is not a valid float. Using default: {self.usd_to_inr_rate}")

        self.openai_client: Optional[Any] = None
        
        if self.llm_provider == "openai": # Now this comparison should be reliable
            logger.info("[ChemicalAnalysisLogic Init] Attempting OpenAI client initialization because effective provider is 'openai'.")
            if not openai:
                logger.warning("[ChemicalAnalysisLogic Init Warn] OpenAI library not imported. Cannot initialize client.")
            elif not self.openai_api_key_val or self.openai_api_key_val.lower() == "none":
                logger.warning("[ChemicalAnalysisLogic Init Warn] OpenAI API key is not set or is 'none' in settings.")
            elif any(placeholder in self.openai_api_key_val.lower() for placeholder in ["your_openai_key", "missing_in_env"]):
                 logger.warning("[ChemicalAnalysisLogic Init Warn] OpenAI API key looks like a placeholder string.")
            else:
                 logger.info(f"[ChemicalAnalysisLogic Init] OpenAI API Key seems valid for initialization attempt: '{self.openai_api_key_val[:10]}...'")
                 try:
                    self.openai_client = openai.AzureOpenAI()
                    logger.info("[ChemicalAnalysisLogic Init] OpenAI client INITIALIZED successfully.")
                 except Exception as e:
                    logger.error(f"[ChemicalAnalysisLogic Init Error] Error initializing OpenAI client: {e}", exc_info=True)
                    self.openai_client = None
        else:
            logger.info(f"[ChemicalAnalysisLogic Init] OpenAI client initialization SKIPPED. Current provider: '{self.llm_provider}'.")

        self.pricing_sources: List[Dict[str, Any]] = []
        self._load_all_pricing_data()

    def _load_single_pricing_source(self, settings_path_attr: str, source_display_name: str) -> None:
        pricing_file_path = getattr(self.settings, settings_path_attr, None)
        if not pricing_file_path or not isinstance(pricing_file_path, str) or not os.path.exists(pricing_file_path):
            print(f"[ChemicalAnalysisLogic Pricing Warn] Path for '{settings_path_attr}' ('{pricing_file_path}') not found or invalid. Source '{source_display_name}' unavailable.")
            self.pricing_sources.append({"name": source_display_name, "filename": "N/A", "data": {}})
            return

        filename = os.path.basename(pricing_file_path)
        try:
            with open(pricing_file_path, 'r', encoding='utf-8') as f: data = json.load(f)
            self.pricing_sources.append({"name": source_display_name, "filename": filename, "data": data})
        except json.JSONDecodeError as e:
            print(f"[ChemicalAnalysisLogic Pricing Error] Parse error in {filename}: {e}. Source unavailable.")
            self.pricing_sources.append({"name": source_display_name, "filename": filename, "data": {}})
        except Exception as e:
            print(f"[ChemicalAnalysisLogic Pricing Error] Load error for {filename}: {e}. Source unavailable.")
            self.pricing_sources.append({"name": source_display_name, "filename": filename, "data": {}})

    def _load_all_pricing_data(self) -> None:
        self.pricing_sources = [] 
        self._load_single_pricing_source('PRICING_FILE_PRIMARY', "Primary Local Data")
        self._load_single_pricing_source('PRICING_FILE_SECONDARY', "Secondary Local Data")
        self._load_single_pricing_source('PRICING_FILE_TERTIARY', "Tertiary Local Data (Sigma)")

    def _is_cas_number(self, identifier: str) -> bool: return bool(re.match(r'^\d{2,7}-\d{2}-\d$', identifier))
    
    def _is_smiles_like(self, identifier: str) -> bool:
        if " " in identifier.strip() and len(identifier.strip().split()) > 1: return False 
        smiles_chars = set("()[]=#@+-.0123456789" + "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ")
        return len(set(identifier) - smiles_chars) < 3 and len(identifier) > 2 and any(c in identifier for c in "()[]=#@+-.")

    def _get_pubchem_data(self, chemical_identifier: str) -> Tuple[Optional[pcp.Compound], Optional[Dict[str, Any]]]:
        compound: Optional[pcp.Compound] = None
        full_json_data: Optional[Dict[str, Any]] = None
        search_methods = []

        if self._is_cas_number(chemical_identifier): search_methods.append({'id': chemical_identifier, 'namespace': 'cas', 'type': 'CAS'})
        if self._is_smiles_like(chemical_identifier): search_methods.append({'id': chemical_identifier, 'namespace': 'smiles', 'type': 'SMILES'})
        search_methods.append({'id': chemical_identifier, 'namespace': 'name', 'type': 'Name'}) 

        for method in search_methods:
            if compound: break 
            try:
                compounds = pcp.get_compounds(method['id'], method['namespace'], timeout=15)
                if compounds:
                    compound = compounds[0] 
                    try:
                        response = requests.get(f"https://pubchem.ncbi.nlm.nih.gov/rest/pug_view/data/compound/{compound.cid}/JSON", timeout=20)
                        response.raise_for_status()
                        full_json_data = response.json()
                    except (requests.RequestException, json.JSONDecodeError) as e_json:
                        print(f"[PubChem JSON Fetch Error] CID {compound.cid}: {e_json}")
                    break 
            except (pcp.PubChemHTTPError, Exception): pass
        return compound, full_json_data

    def _extract_ghs_from_pubchem_json(self, pubchem_json: Optional[Dict[str, Any]]) -> List[Dict[str, str]]:
        ghs_hazards_list = []
        if not pubchem_json: return ghs_hazards_list
        try:
            record = pubchem_json.get('Record', {})
            sections = record.get('Section', [])
            safety_section = next((s for s in sections if s.get('TOCHeading') == 'Safety and Hazards'), None)
            if not safety_section: return ghs_hazards_list
            haz_id_section = next((s for s in safety_section.get('Section', []) if s.get('TOCHeading') == 'Hazards Identification'), None)
            if not haz_id_section: return ghs_hazards_list
            ghs_class_section = next((s for s in haz_id_section.get('Section', []) if s.get('TOCHeading') == 'GHS Classification'), None)
            if not ghs_class_section: return ghs_hazards_list
            
            information = ghs_class_section.get('Information', [])
            pictograms_desc: Dict[str, str] = {} 
            for info_item in information:
                name = info_item.get('Name')
                value_node = info_item.get('Value')
                if not value_node or not isinstance(value_node, dict): continue
                if name == 'Pictogram(s)':
                    for swm in value_node.get('StringWithMarkup', []): 
                        for markup in swm.get('Markup', []):
                            if markup.get('Type') == 'Icon' and markup.get('URL') and markup.get('Extra'):
                                pictograms_desc[markup.get('URL')] = markup.get('Extra')
            
            for info_item in information:
                name = info_item.get('Name')
                value_node = info_item.get('Value')
                if not value_node or not isinstance(value_node, dict): continue
                if name == 'GHS Hazard Statements':
                    for swm_item in value_node.get('StringWithMarkup', []):
                        text = swm_item.get('String', '')
                        h_code, statement = "", text 
                        match = re.match(r"(H\d{3}[A-Za-z+]*)?(?:\s*\(?[\d.]*%\)?)?[:\s]*(.*?)(?:\s*\[(?:Warning|Danger).*\])?$", text)
                        if match:
                            h_code = match.group(1) or ""
                            statement_raw = match.group(2).strip()
                            statement = re.sub(r"\[(?:Warning|Danger)\s*(?:H\d{3}[A-Za-z+]*)?\]", "", statement_raw, flags=re.IGNORECASE).strip()
                        else: statement = re.sub(r"\s*\[(Warning|Danger)\s+.*?\]", "", text).strip()
                        current_pictogram_desc = "N/A"
                        for markup in swm_item.get('Markup', []):
                            if markup.get('Type') == 'Icon' and markup.get('URL') and markup.get('URL') in pictograms_desc:
                                current_pictogram_desc = pictograms_desc[markup.get('URL')]
                                break
                        if current_pictogram_desc == "N/A" and pictograms_desc: 
                            current_pictogram_desc = next(iter(pictograms_desc.values()), "N/A")
                        if statement: 
                            full_statement = f"{h_code}: {statement}".strip().lstrip(": ")
                            ghs_hazards_list.append({"pictogram_description": current_pictogram_desc, "statement": full_statement})
        except Exception as e: print(f"[GHS Extraction Error] {e}")
        return ghs_hazards_list

    def _search_single_local_source(self, source_data: Dict[str, Any], input_id_norm: str, iupac_norm: Optional[str], commons_norm: List[str], smiles: Optional[str]) -> Optional[Dict[str, Any]]:
        if not source_data: return None
        if smiles:
            for k, v_list in source_data.items():
                if isinstance(v_list, list) and len(v_list) >= 2 and isinstance(v_list[0], str) and v_list[0] == smiles:
                    price = v_list[1] if isinstance(v_list[1], (float, int)) else None
                    loc = v_list[2] if len(v_list) >=3 and isinstance(v_list[2], str) else "Unknown Location"
                    if price is not None: return {"price": float(price), "currency": "INR", "location": loc, "source_name_in_json": k, "match_type": "SMILES"}
        names_to_check = set(c.lower().strip() for c in commons_norm if c)
        if iupac_norm: names_to_check.add(iupac_norm.lower().strip())
        if input_id_norm: names_to_check.add(input_id_norm.lower().strip())
        matches = []
        for k, v_list in source_data.items():
            if not (isinstance(v_list, list) and len(v_list) >= 2): continue
            key_norm = k.lower().strip()
            base_key_norm_match = re.match(r"^(.*?)\s*\(", key_norm)
            base_key_norm = base_key_norm_match.group(1).strip() if base_key_norm_match else key_norm
            for name_check in names_to_check:
                if name_check == base_key_norm: 
                    price = v_list[1] if isinstance(v_list[1], (float, int)) else None
                    loc = v_list[2] if len(v_list) >=3 and isinstance(v_list[2], str) else "Unknown Location"
                    if price is not None: 
                        matches.append({"price": float(price), "currency": "INR", "location": loc, "source_name_in_json": k, "match_type": "Exact Name", "matched_on_name": name_check, "len": len(name_check)})
        if matches: 
            best_match = sorted(matches, key=lambda x: x["len"], reverse=True)[0]
            del best_match["len"]
            return best_match
        return None

    def _get_pricing_from_all_local_sources(self, in_id: str, iupac: Optional[str], commons: List[str], smiles: Optional[str]) -> Optional[Dict[str, Any]]:
        for src in self.pricing_sources:
            if not src.get("data"): continue 
            match = self._search_single_local_source(src["data"], in_id, iupac, commons, smiles)
            if match:
                match.update({"source_file_display_name": src['name'], "source_filename": src['filename']})
                return match
        return None

    def _get_llm_completion_with_fallback(self, system_prompt: str, user_prompt: str, model_openai="gpt-4o", model_perplexity="llama-3-sonar-large-32k-online", max_tokens=2000) -> Optional[str]:
        providers_to_try = []
        # Determine effective provider based on self.llm_provider and settings
        effective_provider = self.llm_provider or self.settings.DEFAULT_LLM_PROVIDER
        logger.debug(f"[LLM Fallback] Effective provider for this call: {effective_provider}")

        if effective_provider == "openai":
            providers_to_try = ["openai", "perplexity"]
        elif effective_provider == "perplexity":
            providers_to_try = ["perplexity", "openai"]
        else: # Unknown provider, default to OpenAI then Perplexity
            logger.warning(f"[LLM Fallback] Unknown llm_provider '{self.llm_provider}'. Defaulting to OpenAI then Perplexity.")
            providers_to_try = ["openai", "perplexity"]

        for provider_name in providers_to_try:
            logger.info(f"[LLM Fallback] Attempting LLM call with provider: {provider_name}")
            if provider_name == "openai":
                if self.openai_client:
                    logger.debug(f"[LLM Fallback] OpenAI client available. Key seems set. Model: {model_openai}")
                    try:
                        response = self.openai_client.chat.completions.create(
                            model=model_openai, 
                            messages=[{"role": "system", "content": system_prompt}, {"role": "user", "content": user_prompt}],
                            temperature=0.1, max_tokens=max_tokens
                        )
                        logger.info(f"[LLM Fallback] OpenAI call successful with {model_openai}.")
                        return response.choices[0].message.content
                    except openai.APIConnectionError as e:
                        logger.error(f"[LLM Fallback] OpenAI APIConnectionError: {e}. Trying next provider.")
                    except openai.RateLimitError as e:
                        logger.error(f"[LLM Fallback] OpenAI RateLimitError: {e}. Trying next provider.")
                    except openai.AuthenticationError as e:
                        logger.error(f"[LLM Fallback] OpenAI AuthenticationError (check API key): {e}. Trying next provider.")
                    except openai.APIStatusError as e: # More general OpenAI API error
                        logger.error(f"[LLM Fallback] OpenAI APIStatusError: Status {e.status_code}, Message: {e.message}. Trying next provider.")
                    except Exception as e:
                        logger.error(f"[LLM Fallback] Generic OpenAI call error: {e}. Trying next provider.")
                else:
                    logger.warning(f"[LLM Fallback] OpenAI client not initialized or API key missing. Skipping OpenAI for provider '{provider_name}'.")
            
            elif provider_name == "perplexity":
                if self.perplexity_api_key_val and "your_perplexity_api_key" not in self.perplexity_api_key_val.lower() and self.perplexity_api_key_val.lower() != "none":
                    logger.debug(f"[LLM Fallback] Perplexity API key available. Model: {model_perplexity}")
                    payload = {"model": model_perplexity, "messages": [{"role": "system", "content": system_prompt}, {"role": "user", "content": user_prompt}], "max_tokens": max_tokens}
                    headers = {"Authorization": f"Bearer {self.perplexity_api_key_val}", "Content-Type": "application/json", "Accept": "application/json"}
                    try:
                        response = requests.post("https://api.perplexity.ai/chat/completions", json=payload, headers=headers, timeout=self.settings.HTTP_REQUEST_TIMEOUT)
                        logger.debug(f"[LLM Fallback] Perplexity request sent. Status code: {response.status_code}")
                        if response.status_code == 401:
                            logger.error(f"[LLM Fallback] Perplexity AuthenticationError (401). Check API key. Response: {response.text[:200]}")
                        elif response.status_code == 429:
                            logger.error(f"[LLM Fallback] Perplexity RateLimitError (429). Response: {response.text[:200]}")
                        response.raise_for_status() # Will raise HTTPError for 4xx/5xx
                        logger.info(f"[LLM Fallback] Perplexity call successful with {model_perplexity}.")
                        return response.json()['choices'][0]['message']['content']
                    except requests.exceptions.HTTPError as e: # Catches 4xx/5xx errors specifically
                        logger.error(f"[LLM Fallback] Perplexity HTTPError: {e}. Response: {e.response.text[:500] if e.response else 'No response body'}. Trying next provider.")
                    except Exception as e:
                        logger.error(f"[LLM Fallback] Generic Perplexity call error: {e}. Trying next provider.")
                else:
                    logger.warning(f"[LLM Fallback] Perplexity API key not set or is placeholder. Skipping Perplexity for provider '{provider_name}'.")
        
        logger.error("[LLM Error] All LLM providers failed or are not configured after trying fallbacks.")
        return None
    
    def _parse_llm_json_response(self, llm_response_content: Optional[str], default_on_error: Optional[Dict] = None) -> Dict:
        if default_on_error is None: default_on_error = {}
        if not llm_response_content: return default_on_error
        try:
            json_str_to_parse = llm_response_content
            match_json = re.search(r"```json\s*([\s\S]*?)\s*```", llm_response_content, re.IGNORECASE)
            if match_json: json_str_to_parse = match_json.group(1).strip()
            else:
                first_brace = json_str_to_parse.find('{'); last_brace = json_str_to_parse.rfind('}')
                if first_brace != -1 and last_brace != -1 and last_brace > first_brace:
                    json_str_to_parse = json_str_to_parse[first_brace : last_brace+1]
                else:
                    print(f"[LLM JSON Parse WARN] Could not reliably find JSON object in: {llm_response_content[:100]}...");
                    return default_on_error
            return json.loads(json_str_to_parse)
        except json.JSONDecodeError as e:
            print(f"[LLM JSON Parse Error] Failed to decode JSON: {e}. Response: {llm_response_content[:200]}...");
            return default_on_error

    def _get_llm_derived_pricing(self, chemical_name: str, smiles: Optional[str], formula: Optional[str], cid: Optional[int]) -> Optional[Dict[str, Any]]:
        context_parts = [f"Chemical: {chemical_name}"]
        if smiles: context_parts.append(f"SMILES: {smiles}")
        if formula: context_parts.append(f"Formula: {formula}")
        if cid: context_parts.append(f"PubChem CID: {cid}")
        system_prompt = "You are a chemical market analyst. Provide price estimations in JSON format."
        user_prompt = f"""{", ".join(context_parts)}
Please provide an estimated bulk price for this chemical in INR per kg.
If INR is not possible, provide it in USD per kg. Consider typical research grade or small industrial scale pricing.
If an exact price is unknown, please provide your best possible *numerical estimate* or a price range (e.g., "10000-15000" for INR, or "100-150" for USD).
It is important to provide a numerical value if at all possible, even if confidence is very low.
Return JSON with EITHER "estimated_price_per_kg_inr" OR "estimated_price_per_kg_usd": {{
    "estimated_price_per_kg_inr": float_or_string_range_or_null, "estimated_price_per_kg_usd": float_or_string_range_or_null,
    "price_confidence": "very_low/low/medium/high", "price_basis_notes": "Brief notes..."}}
Provide only one of the price fields, setting the other to null if you use one. Prioritize INR if possible."""
        llm_response_content = self._get_llm_completion_with_fallback(system_prompt, user_prompt, max_tokens=500)
        parsed_data = self._parse_llm_json_response(llm_response_content)
        if not parsed_data: return None
        try:
            price_value_usd = parsed_data.get("estimated_price_per_kg_usd"); price_value_inr = parsed_data.get("estimated_price_per_kg_inr")
            final_price_inr = None; llm_provided_currency = None
            def parse_price_value(val_input: Any) -> Optional[float]:
                if isinstance(val_input, (int, float)): return float(val_input)
                if isinstance(val_input, str):
                    cleaned_val = val_input.replace(',', ''); match_r = re.match(r"(\d+\.?\d*)\s*-\s*(\d+\.?\d*)", cleaned_val) 
                    if match_r: return (float(match_r.group(1)) + float(match_r.group(2))) / 2
                    match_s = re.match(r"(\d+\.?\d*)", cleaned_val); 
                    if match_s: return float(match_s.group(1))
                return None
            if price_value_inr is not None:
                parsed_val = parse_price_value(price_value_inr)
                if parsed_val is not None: final_price_inr, llm_provided_currency = parsed_val, "INR"
            if final_price_inr is None and price_value_usd is not None: 
                parsed_val = parse_price_value(price_value_usd)
                if parsed_val is not None: final_price_inr, llm_provided_currency = parsed_val * self.usd_to_inr_rate, "USD"
            if final_price_inr is not None:
                return {"price_inr": final_price_inr, "currency_llm_provided": llm_provided_currency,
                        "raw_llm_price_value": price_value_inr if llm_provided_currency == "INR" else price_value_usd,
                        "confidence": parsed_data.get("price_confidence"), "basis_notes": parsed_data.get("price_basis_notes"),
                        "source_type": f"LLM ({self.llm_provider or 'fallback'})"}
            return {"price_inr": None, "currency_llm_provided": None, "raw_llm_price_value": None,
                    "confidence": parsed_data.get("price_confidence", "low_no_price_data"),
                    "basis_notes": parsed_data.get("price_basis_notes", "LLM no price or format issue."),
                    "source_type": f"LLM ({self.llm_provider or 'fallback'})"}
        except (ValueError, TypeError) as e:
            print(f"[LLM Pricing Parse Error] Error processing parsed LLM price data: {e}. Data: {parsed_data}")
            return None

    def _parse_int_score(self, val: Any, field_name_for_log: str) -> Optional[int]: 
        if val is None: return None
        try: return int(val)
        except (ValueError, TypeError): return None

    def _get_llm_derived_properties(self, name: str, formula: Optional[str], smiles: Optional[str],
                                   cid: Optional[int], cas: Optional[str], mw: Optional[float],
                                   iupac: Optional[str]) -> Dict[str, Any]:
        context_parts = [f"Chemical: {name}."]; known_info = []
        if cid: known_info.append(f"PubChem CID: {cid}")
        if iupac: known_info.append(f"IUPAC Name: {iupac}") 
        if formula: known_info.append(f"Formula: {formula}")
        if mw: known_info.append(f"MW: {mw:.2f}" if isinstance(mw, float) else f"MW: {mw}")
        if smiles: known_info.append(f"SMILES: {smiles}")
        if cas: known_info.append(f"CAS: {cas}")
        if known_info: context_parts.append(f"Known info: {'; '.join(known_info)}.")
        else: context_parts.append("No definitive structural or ID information known from databases yet.")
        full_context = "\n".join(context_parts); guess_instr = ""
        if not iupac: guess_instr += '"iupac_name_llm_guess": "Guess IUPAC name or null",\n'
        if not formula: guess_instr += '"molecular_formula_llm_guess": "Guess formula or null",\n'
        if not mw: guess_instr += '"molecular_weight_llm_guess": "Guess MW (float) or null",\n'
        if not cas: guess_instr += '"cas_number_llm_guess": "Guess CAS (XXX-XX-X) or null",\n'
        if not smiles: guess_instr += '"smiles_llm_guess": "Guess SMILES or null",\n'
        system_prompt = "You are a chemical safety, properties, and environmental expert. Provide accurate assessments in JSON format. If guessing core chemical identity, clearly indicate it (e.g., field ends with '_llm_guess')."
        user_prompt = f"""{full_context}
Provide analysis in JSON. Ratings 1-10 (10=high/extreme/excellent) or null. If core ID (IUPAC, formula, MW, CAS, SMILES) unknown, guess it.
Output JSON format:
{{{{ {guess_instr}
    "solubility": {{{{ "water_solubility": "Water Soluble/Organic Soluble/Poorly Soluble/Insoluble/Unknown", "organic_solvents_compatibility": ["alcohols", "ethers", "..."], "notes_on_solubility": "notes", "solubility_rating": "integer 1-10 or null" }}}},
    "hazards": {{{{ "corrosive": true/false/null, "flammable": true/false/null, "toxic": true/false/null, "carcinogenic_suspected": true/false/null, "environmental_hazard_notes": "notes", "overall_hazard_level": "Low/Moderate/High/Extreme/Unknown", "hazard_rating": "integer 1-10 or null", "ghs_info_llm": [ {{{{ "pictogram_description": "Pictogram Name", "h_code": "HXXX", "h_statement": "Full statement" }}}}] }}}},
    "safety_precautions": ["list of key safety measures"], "storage_recommendations": "storage conditions", "disposal_considerations": "disposal notes",
    "green_chemistry": {{{{ "renewable_feedstock_potential": "yes/no/unknown", "atom_economy_typical_reactions": "high/low/unknown", "biodegradability_assessment": "readily/poorly/unknown", "energy_efficiency_synthesis": "high/low/unknown", "waste_generation_typical_reactions": "high/low/unknown", "overall_score": "integer 1-10 or null" }}}},
    "environmental_impact_summary": "overall assessment" }}}}
Use empty list [] for 'ghs_info_llm' if none. For others, use null or "Unknown"."""
        llm_response_content = self._get_llm_completion_with_fallback(system_prompt, user_prompt, max_tokens=2500)
        default_empty_response = {"solubility": {"water_solubility": SolubilityType.UNKNOWN.value, "solubility_rating": None, "organic_solvents_compatibility": [], "notes_on_solubility": "N/A"},
                                  "hazards": {"overall_hazard_level": HazardLevel.UNKNOWN.value, "hazard_rating": None, "ghs_info_llm": [], "corrosive": None, "flammable": None, "toxic": None, "carcinogenic_suspected": None, "environmental_hazard_notes": "N/A"},
                                  "safety_precautions": [], "storage_recommendations": "N/A", "disposal_considerations": "N/A",
                                  "green_chemistry": {"overall_score": None, "renewable_feedstock_potential": "unknown", "atom_economy_typical_reactions": "unknown", "biodegradability_assessment": "unknown", "energy_efficiency_synthesis": "unknown", "waste_generation_typical_reactions": "unknown"},
                                  "environmental_impact_summary": "Assessment unavailable due to LLM error or no data."}
        parsed_data = self._parse_llm_json_response(llm_response_content, default_on_error=default_empty_response)
        for key, default_value in default_empty_response.items():
            if key not in parsed_data: parsed_data[key] = default_value
            elif isinstance(default_value, dict):
                for sub_key, sub_default_value in default_value.items():
                    if sub_key not in parsed_data[key]: parsed_data[key][sub_key] = sub_default_value
        return parsed_data
    
    def analyze_chemical(self, chemical_identifier: str) -> ChemicalPropertiesData:
        props = ChemicalPropertiesData(name=chemical_identifier, original_query=chemical_identifier)
        pubchem_compound, pubchem_full_json = self._get_pubchem_data(chemical_identifier)
        props.pubchem_full_json = pubchem_full_json 
        current_name_for_llm = chemical_identifier
        if pubchem_compound:
            props.name = pubchem_compound.iupac_name or (pubchem_compound.synonyms[0] if pubchem_compound.synonyms else chemical_identifier)
            current_name_for_llm = props.name 
            props.iupac_name = pubchem_compound.iupac_name
            props.common_names = list(set(pubchem_compound.synonyms[:10])) if pubchem_compound.synonyms else []
            if chemical_identifier.lower() not in [name.lower() for name in props.common_names] and \
               (not props.iupac_name or chemical_identifier.lower() != props.iupac_name.lower()) and \
               chemical_identifier.lower() != props.name.lower():
                props.common_names.insert(0, chemical_identifier)
            props.molecular_formula = pubchem_compound.molecular_formula
            props.molecular_weight = float(pubchem_compound.molecular_weight) if pubchem_compound.molecular_weight else None
            if pubchem_compound.synonyms:
                cas_syns = [s for s in pubchem_compound.synonyms if self._is_cas_number(s)]
                if cas_syns: props.cas_number = cas_syns[0]
            props.smiles = pubchem_compound.canonical_smiles
            props.pubchem_cid = pubchem_compound.cid
            props.ghs_hazards = self._extract_ghs_from_pubchem_json(props.pubchem_full_json)
        llm_derived_info = self._get_llm_derived_properties(current_name_for_llm, props.molecular_formula, props.smiles, props.pubchem_cid, props.cas_number, props.molecular_weight, props.iupac_name)
        if not pubchem_compound: 
            props.iupac_name = props.iupac_name or llm_derived_info.get("iupac_name_llm_guess")
            if props.name == chemical_identifier and props.iupac_name: props.name = props.iupac_name; current_name_for_llm = props.name
            props.molecular_formula = props.molecular_formula or llm_derived_info.get("molecular_formula_llm_guess")
            mw_g = llm_derived_info.get("molecular_weight_llm_guess")
            if mw_g is not None and props.molecular_weight is None:
                try: props.molecular_weight = float(mw_g)
                except (ValueError,TypeError): pass
            cas_g = llm_derived_info.get("cas_number_llm_guess")
            if cas_g and self._is_cas_number(cas_g) and props.cas_number is None: props.cas_number = cas_g
            if props.smiles is None: props.smiles = llm_derived_info.get("smiles_llm_guess")
        props.solubility = llm_derived_info.get("solubility", props.solubility)
        llm_hazards = llm_derived_info.get("hazards", {})
        haz_lvl_str = llm_hazards.get("overall_hazard_level", "unknown").lower()
        try: props.hazard_level = HazardLevel[haz_lvl_str.upper()]
        except KeyError: props.hazard_level = HazardLevel.UNKNOWN
        props.is_corrosive = llm_hazards.get("corrosive", props.is_corrosive)
        props.is_flammable = llm_hazards.get("flammable", props.is_flammable)
        props.is_toxic = llm_hazards.get("toxic", props.is_toxic)
        if not props.ghs_hazards and "ghs_info_llm" in llm_hazards: 
            for item in llm_hazards.get("ghs_info_llm", []):
                if isinstance(item, dict) and item.get("h_statement"): 
                    props.ghs_hazards.append({"pictogram_description": item.get("pictogram_description", "N/A"),
                                              "statement": f"{item.get('h_code', '')}: {item.get('h_statement', '')}".strip().lstrip(": ")})
        props.safety_notes = llm_derived_info.get("safety_precautions", props.safety_notes) 
        props.environmental_impact = llm_derived_info.get("environmental_impact_summary", props.environmental_impact)
        gc_score = llm_derived_info.get("green_chemistry", {}).get("overall_score")
        props.green_chemistry_score = self._parse_int_score(gc_score, "GC score") or props.green_chemistry_score
        props.hazard_rating = self._parse_int_score(llm_hazards.get("hazard_rating"), "Hazard rating") or props.hazard_rating
        sol_rating = llm_derived_info.get("solubility", {}).get("solubility_rating")
        props.solubility_rating = self._parse_int_score(sol_rating, "Solubility rating") or props.solubility_rating
        local_price = self._get_pricing_from_all_local_sources(chemical_identifier, props.iupac_name, props.common_names, props.smiles)
        if local_price:
            props.estimated_price_per_kg = local_price.get("price")
            props.price_currency = local_price.get("currency", "INR") 
            props.supplier_info = [{"name": f"Local DB: {local_price.get('source_file_display_name','N/A')} ({local_price.get('source_name_in_json', 'N/A')})",
                                    "availability": (f"Price: {props.estimated_price_per_kg:.2f} {props.price_currency}. Match: {local_price.get('match_type','N/A')} on '{local_price.get('source_name_in_json','N/A')}'."),
                                    "location": local_price.get('location', 'Unknown Location'), "source_type": "Local JSON"}]
        else:
            llm_price_info = self._get_llm_derived_pricing(current_name_for_llm, props.smiles, props.molecular_formula, props.pubchem_cid)
            if llm_price_info and llm_price_info.get("price_inr") is not None:
                props.estimated_price_per_kg = llm_price_info["price_inr"]; props.price_currency = "INR" 
                availability_details = (f"Est. Price: {props.estimated_price_per_kg:.2f} INR/kg. (LLM Raw: {llm_price_info.get('raw_llm_price_value')} {llm_price_info.get('currency_llm_provided')}, Conf: {llm_price_info.get('confidence')})")
                props.supplier_info = [{"name": f"LLM Estimation ({llm_price_info.get('source_type', self.llm_provider or 'fallback')})", "availability": availability_details,
                                        "location": "Global Market (Est.)", "source_type": llm_price_info.get('source_type', f"LLM ({self.llm_provider or 'fallback'})")}]
            else: 
                props.estimated_price_per_kg = None; props.price_currency = None
                availability_note = "Not in local DBs. LLM pricing inconclusive."
                if llm_price_info: availability_note += f" (LLM Conf: {llm_price_info.get('confidence','N/A')}, Basis: {llm_price_info.get('basis_notes','N/A')})"
                props.supplier_info = [{"name": "No Definitive Pricing Data", "availability": availability_note, "location": "N/A", "source_type": "None"}]
        return props

    def generate_report(self, props: ChemicalPropertiesData) -> str:
        report_parts = [f"Chemical Report: {props.name}\n" + "=" * (17 + len(props.name))]
        if props.original_query and props.original_query != props.name: report_parts.append(f"Original Query: {props.original_query}")
        core_info = [("IUPAC Name", props.iupac_name), ("Common Names", ", ".join(props.common_names) if props.common_names else "N/A"),
            ("Molecular Formula", props.molecular_formula), ("Molecular Weight", f"{props.molecular_weight:.2f} g/mol" if props.molecular_weight else None),
            ("CAS Number", props.cas_number), ("SMILES", props.smiles), ("PubChem CID", props.pubchem_cid)]
        report_parts.append("\n--- Core Identification ---"); [report_parts.append(f"{label}: {value}") for label, value in core_info if value is not None]
        report_parts.append("\n--- Physicochemical Properties ---")
        report_parts.append(f"Solubility in Water: {props.solubility.get('water_solubility', 'Unknown')}")
        if props.solubility.get('organic_solvents_compatibility'): report_parts.append(f"Organic Solvents Compatibility: {', '.join(props.solubility['organic_solvents_compatibility'])}")
        if props.solubility.get('notes_on_solubility'): report_parts.append(f"Solubility Notes: {props.solubility['notes_on_solubility']}")
        if props.solubility_rating is not None: report_parts.append(f"Solubility Rating (1-10): {props.solubility_rating}/10")
        report_parts.append("\n--- Hazard Information ---")
        report_parts.append(f"Overall Hazard Level: {props.hazard_level.value}")
        if props.hazard_rating is not None: report_parts.append(f"Hazard Rating (1-10): {props.hazard_rating}/10")
        if props.is_corrosive is not None: report_parts.append(f"Corrosive: {'Yes' if props.is_corrosive else 'No'}")
        if props.is_flammable is not None: report_parts.append(f"Flammable: {'Yes' if props.is_flammable else 'No'}")
        if props.is_toxic is not None: report_parts.append(f"Toxic: {'Yes' if props.is_toxic else 'No'}")
        if props.ghs_hazards:
            report_parts.append("GHS Hazard Statements:"); [report_parts.append(f"  - Pictogram: {ghs.get('pictogram_description', 'N/A')}, Statement: {ghs.get('statement', 'N/A')}") for ghs in props.ghs_hazards[:5]]
        else: report_parts.append("GHS Hazard Statements: Not available or not found.")
        if props.safety_notes: report_parts.append("Key Safety Precautions (LLM Suggested):"); [report_parts.append(f"  - {note}") for note in props.safety_notes]
        report_parts.append("\n--- Economic Information ---")
        if props.estimated_price_per_kg is not None and props.price_currency:
            report_parts.append(f"Estimated Price: {props.estimated_price_per_kg:.2f} {props.price_currency}/kg")
            if props.supplier_info: source_desc, availability = props.supplier_info[0].get('name', 'N/A'), props.supplier_info[0].get('availability', 'N/A'); report_parts.append(f"Price Source: {source_desc} ({availability})")
        else:
            report_parts.append("Estimated Price: Not available")
            if props.supplier_info: report_parts.append(f"Pricing/Supplier Notes: {props.supplier_info[0].get('availability', 'No specific notes.')}")
        report_parts.append("\n--- Environmental & Green Chemistry ---")
        if props.environmental_impact: report_parts.append(f"Environmental Impact Summary: {props.environmental_impact}")
        if props.green_chemistry_score is not None: report_parts.append(f"Green Chemistry Score (1-10): {props.green_chemistry_score}/10")
        report_parts.append(f"\nReport generated by ChemicalAnalysisLogic on {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        return "\n".join(report_parts)