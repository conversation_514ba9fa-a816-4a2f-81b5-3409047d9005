"""Prompts for patent preprocessing and relevance analysis."""

GEMINI_PATENT_PREPROCESSING_PROMPT = """
You are an expert chemical patent analyst. Your task is to determine if a given patent is related to the production/synthesis of a specific input chemical compound.

## Input Information:
- **Input Chemical Name**: {chemical_name}
- **Input SMILES**: {smiles}
- **Patent Abstract**: {abstract}
- **Patent Description**: {description}
- **Patent Claims**: {claims}

## Analysis Requirements:

1. **Carefully analyze** the patent content (abstract, description, and claims) to understand what the patent is actually about.

2. **Summarize the patent claims**:
   - Provide a brief summary of what the patent is claiming
   - What is the main invention or innovation described?
   - What is the patent actually about in simple terms?

3. **Identify chemical reactions in claims**:
   - List the main chemical reactions mentioned in the patent claims in a simple, readable format
   - Use the format: "Key Reactions:" followed by bullet points with brief descriptions
   - Include reaction types, key transformations, or synthetic methods
   - Focus on reactions that are central to the patent's claims
   - Example format:
     Key Reactions:
     Chlorination of methylbenzoic acid
     Nitration using nitric acid/sulfuric acid mixture
     Purification via recrystallization

4. **Analyze and tag individual claims**:
   - Extract each individual claim from the patent claims section
   - For each claim, assign one of these 4 tags based on what the claim is primarily about:
     * **Process**: Claims describing methods, processes, procedures, or steps for making something
     * **Product**: Claims describing final products, compounds, or compositions as end results
     * **Intermediate**: Claims describing intermediate compounds, precursors, or compounds used in synthesis
     * **Composition**: Claims describing mixtures, formulations, pharmaceutical compositions, or combinations
   - Include the full claim text exactly as provided in the input
   - Number claims as "Claim 1", "Claim 2", etc. in order
   - **Analyze claim dependencies**:
     * **dependent**: Set to true if the claim references or depends on any other claim (e.g., "The method of claim 1", "The compound according to claim 2")
     * **depends_on**: If dependent is true, specify which claim it depends on (e.g., "Claim 1", "Claim 2"). If dependent is false, use empty string ""
     * Look for phrases like "according to claim X", "of claim X", "as defined in claim X", etc.

5. **Generate relevant tags**:
   - Create a list of tags that are closely related to the patent content
   - Use primarily one-word tags, only use multiple words if absolutely necessary
   - Only include tags for reaction types (e.g., "Chlorination", "Nitration", "Oxidation", "Reduction")
   - Limit to 3-8 most relevant tags

6. **Assign a relevancy score (1-10)**:
   - Rate how relevant this patent is to the input compound synthesis/production
   - **IMPORTANT**: Only patents where the input compound is synthesized, produced, or formed (including as intermediate/byproduct) should receive scores above 2
   - Patents where the input compound is used as a reactant, reagent, or starting material should receive scores of 1-2
   - Scoring criteria:
     * 9-10: Patent primarily focuses on synthesizing/producing the input compound with detailed methods
     * 7-8: Patent includes significant synthesis/production methods for the input compound
     * 5-6: Patent mentions synthesis/production of the input compound but not as main focus
     * 3-4: Patent describes synthesis/production of the input compound with limited detail, or input compound is formed as intermediate/byproduct
     * 1-2: Patent uses the input compound as reactant/reagent/starting material, or only mentions it in background/references
     * 0: Patent does not mention the input compound at all

7. **Synthesis vs. Usage Distinction**:
   - Carefully distinguish between patents that synthesize/produce the input compound versus those that use it
   - Patents describing the input compound being formed, made, or produced should receive higher scores
   - Patents describing the input compound being consumed, used as reagent, or used as starting material should receive low scores (1-2)


## Output Format:
You MUST respond with ONLY a valid JSON object in the following format. Do not include any explanatory text before or after the JSON.

```json
{{
    "summaryOfClaims": "Brief summary of what the patent is claiming and what it is actually about",
    "reactionsInClaims": "Key reactions in simple format: 'Key Reactions:' followed by bullet points with brief reaction descriptions",
    "claims": {{
        "Claim 1": {{
            "claim": "Full text of the first claim exactly as provided",
            "tag": "Process",
            "dependent": false,
            "depends_on": ""
        }},
        "Claim 2": {{
            "claim": "Full text of the second claim exactly as provided",
            "tag": "Product",
            "dependent": true,
            "depends_on": "Claim 1"
        }}
    }},
    "tags": ["Oxidation", "Chlorination", "Nitration", "Reduction"],
    "relevancyScore": 0
}}
```

## Examples:
- Patent about aspirin synthesis → relevancyScore: 8-10 (if input is aspirin)
- Patent where aspirin is formed as intermediate → relevancyScore: 3-4 (if input is aspirin)
- Patent using aspirin as starting material/reactant → relevancyScore: 1-2 (if input is aspirin)
- Patent mentioning aspirin in background only → relevancyScore: 1-2 (if input is aspirin)

## Claim Tagging Examples:
- "A method for synthesizing compound X by..." → tag: "Process", dependent: false, depends_on: ""
- "Compound X having the formula..." → tag: "Product", dependent: false, depends_on: ""
- "The method of claim 1, wherein the temperature is..." → tag: "Process", dependent: true, depends_on: "Claim 1"
- "The compound according to claim 2, further comprising..." → tag: "Product", dependent: true, depends_on: "Claim 2"
- "An intermediate compound Y used in the synthesis of..." → tag: "Intermediate", dependent: false, depends_on: ""
- "A pharmaceutical composition comprising the compound of claim 3..." → tag: "Composition", dependent: true, depends_on: "Claim 3"

## IMPORTANT:
- Respond with ONLY the JSON object
- No additional text, explanations, or markdown formatting
- Ensure the JSON is valid and properly formatted
- Use double quotes for all strings
- Use true/false (lowercase) for boolean values

Analyze the provided patent information and return only the JSON response:
"""

GEMINI_PAPER_PREPROCESSING_PROMPT = """
You are an expert chemical research paper analyst. Your task is to determine if a given research paper is related to the synthesis of a specific input chemical compound.

## Input Information:
- **Input Chemical Name**: {chemical_name}
- **Parsed Paper Text**: {parsed_text}

## Analysis Requirements:

1. **Carefully analyze** the paper content to understand what the research is actually about.

2. **Summarize the paper**:
   - Provide a brief summary of what the paper is about
   - What is the main research focus or findings described?
   - What synthesis methods or chemical processes are discussed?

3. **Determine input compound relation**:
   - Analyze how the input compound is related to this paper
   - What is specifically discussed about the input compound in the paper?
   - Is the paper about synthesizing the input compound, using it as a starting material, or just mentioning it?
   - Provide specific details about the compound's role in the research

4. **Assign a relevancy score (1-10)**:
   - Rate how relevant this paper is to the input compound synthesis
   - Score based on focus and detail level of input compound production
   - Scoring criteria:
     * 9-10: Paper primarily focuses on synthesizing the input compound with detailed methods
     * 7-8: Paper includes significant synthesis methods for the input compound
     * 5-6: Paper mentions synthesis of the input compound but not as main focus
     * 3-4: Paper mentions the input compound in synthesis context but limited detail
     * 1-2: Paper barely mentions the input compound or only in background/references
     * 0: Paper does not mention the input compound at all

## Critical Guidelines:
- **relevancyScore Guidelines**:
  * Score 9-10: Paper's main focus is synthesizing the input compound with detailed step-by-step methods, conditions, and yields
  * Score 7-8: Paper includes substantial sections on input compound synthesis with good detail
  * Score 5-6: Paper covers input compound synthesis but as part of broader scope or with moderate detail
  * Score 3-4: Paper mentions input compound synthesis but with limited detail or as minor part
  * Score 1-2: Paper only briefly mentions input compound or uses it as starting material/reference
  * Score 0: Paper does not mention input compound at all

## Output Format:
You MUST respond with ONLY a valid JSON object in the following format. Do not include any explanatory text before or after the JSON.

```json
{{
    "summaryOfPaper": "Brief summary of what the paper is about and its main research focus",
    "inputCompoundRelation": "Detailed explanation of how the input compound is related to this paper and what is discussed about it",
    "relevancyScore": 0
}}
```

## Examples:
- Paper about aspirin synthesis methods → relevancyScore: 8-10 (if input is aspirin)
- Paper using aspirin as starting material for other compounds → relevancyScore: 2-3 (if input is aspirin)
- Paper mentioning aspirin in literature review → relevancyScore: 1-2 (if input is aspirin)

## IMPORTANT:
- Respond with ONLY the JSON object
- No additional text, explanations, or markdown formatting
- Ensure the JSON is valid and properly formatted
- Use double quotes for all strings
- Use true/false (lowercase) for boolean values

Analyze the provided paper information and return only the JSON response:
"""


def get_preprocessing_prompt(use_paper_prompt: bool = False) -> str:
    """Get the preprocessing prompt.

    Args:
        use_paper_prompt: If True, return paper preprocessing prompt, else patent prompt

    Returns:
        The appropriate preprocessing prompt string
    """
    if use_paper_prompt:
        return GEMINI_PAPER_PREPROCESSING_PROMPT
    else:
        return GEMINI_PATENT_PREPROCESSING_PROMPT