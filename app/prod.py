# import os
# import json, uuid
# from celery import Celery

# # Initialize Celery app (same config as your worker)
# celery_app = Celery('process_patent_message', broker=os.getenv("REDIS_URL", "redis://localhost:6379/0"))

# def send_patent_task(payload):
#     """Send a task to the patent worker"""
#     INPUT_REDIS_QUEUE = os.getenv("PATENT_INPUT_REDIS_QUEUE", "patent_input_redis_queue")
    
#     # Send task to specific queue
#     result = celery_app.send_task(
#         'process_patent_message',
#         args=[payload],
#         queue=INPUT_REDIS_QUEUE
#     )
#     return result


# # Usage example:
# if __name__ == "__main__":
#     payload = {
#         'request_id': 'b56038a8-0a0e-4d07-bc10-8b45c1380254',
#         'target_smiles': 'C1C=C(S(=O)(C)=O)C(C2CCON=2)=C(C)C=1C#N',
#         'molecule_name' : '3-(4,5-dihydroisoxazol-3-yl)-2-methyl-4-(methylsulfonyl)benzonitrile',
#         'patent_id' : 'JP2020015738',
#         'model_type': 'gemini',
#     }
    
#     task_result = send_patent_task(payload)
#     print(f"Task sent with ID: {task_result.id}")


# #     curl -X 'GET' \
# #   'http://agent-hub.ai.svc.cluster.local/api/v1/tools/smiles_to_iupac_name?smiles_string=O%3DC%28O%29C1%3DCC%28Cl%29%3DCC%28%3DC1N%29C' \
# #   -H 'accept: application/json'
    
# #     curl -X 'POST' \
# #   'http://agent-hub.ai.svc.cluster.local/api/v1/chemcopilot/query' \
# #   -H 'accept: application/json' \
# #   -H 'Content-Type: application/json' \
# #   -d '{
# #   "query": "Give the full info about this reaction - O=C(O)c1ccncc1C(F)(F)F.ClS(=O)Cl>ClS(=O)Cl>O=C(Cl)c1ccncc1C(F)(F)F.O=S.Cl",
# #   "session_id": "string",
# #   "original_name_for_saving": "string",
# #   "clear_moi_before_query": false
# # }'




# import os
# import json, uuid
# from celery import Celery

# # Initialize Celery app (same config as your worker)
# celery_app = Celery('main', broker=os.getenv("REDIS_URL", "redis://localhost:6379/0"))

# def send_patent_task(payload):
#     """Send a task to the patent worker"""
#     INPUT_REDIS_QUEUE = os.getenv("RETRO_INPUT_REDIS_QUEUE", "retro_input_job_queue")
    
#     # Send task to specific queue
#     result = celery_app.send_task(
#         'process_retro_task',
#         args=[payload],
#         queue=INPUT_REDIS_QUEUE
#     )
#     return result

# # Usage example:
# if __name__ == "__main__":
#     payload = {
#         'request_id': "fs6df8a82-9fae-479e-ba10-e92e0f8f9492",  # Generate a unique request ID
#         'molecule_name': '2,3-Dichloropyridine',
#         'target_smiles': 'ClC1=C(Cl)C=CC=N1',
#         'model_type': 'gemini',
#         'max_results': 10
#     }
    
#     task_result = send_patent_task(payload)
#     print(f"Task sent with ID: {task_result.id}")


#     curl -X 'GET' \
#   'http://agent-hub.ai.svc.cluster.local/api/v1/tools/smiles_to_iupac_name?smiles_string=O%3DC%28O%29C1%3DCC%28Cl%29%3DCC%28%3DC1N%29C' \
#   -H 'accept: application/json'
    
#     curl -X 'POST' \
#   'http://agent-hub.ai.svc.cluster.local/api/v1/chemcopilot/query' \
#   -H 'accept: application/json' \
#   -H 'Content-Type: application/json' \
#   -d '{
#   "query": "Give the full info about this reaction - O=C(O)c1ccncc1C(F)(F)F.ClS(=O)Cl>ClS(=O)Cl>O=C(Cl)c1ccncc1C(F)(F)F.O=S.Cl",
#   "session_id": "string",
#   "original_name_for_saving": "string",
#   "clear_moi_before_query": false
# }'




from openai import AzureOpenAI
from dotenv import load_dotenv
import os

load_dotenv()

client = AzureOpenAI(
    api_key="BnkXhIU7bkgOz43zKkAcP6qoOLEBnVLkKTezX8isp6urGvjMptabJQQJ99BCACYeBjFXJ3w3AAABACOGfqjl",
    api_version="2025-03-01-preview",
    azure_endpoint="https://mstack-openai-prod-use.openai.azure.com"
)

input_text = """
You are an expert at finding the most relevant patents given a chemical compound with their patent id.......
"""

response = client.responses.create(
    model="gpt-4.1",  # replace this with your deployment name
    tools=[
        {
            "type": "web_search_preview",
            "search_context_size": "high"
        }
    ],
    input=input_text
)

print(response.output_text)