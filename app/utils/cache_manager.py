"""Cache management utilities."""

import json
import os
from typing import Dict, Any, Optional
from ..core.exceptions import CacheError
from ..utils.logging import get_logger

logger = get_logger(__name__)


class CacheManager:
    """Manages in-memory caching."""
    
    def __init__(self, enabled: bool = True):
        """
        Initialize cache manager.
        
        Args:
            enabled: Whether caching is enabled
        """
        self.enabled = enabled
        self.cache_data: Dict[str, Any] = {}
    
    def get(self, key: str) -> Optional[Any]:
        """
        Get value from cache.
        
        Args:
            key: Cache key
            
        Returns:
            Cached value or None if not found
        """
        if not self.enabled:
            return None
            
        result = self.cache_data.get(key)
        if result is not None:
            logger.debug(f"Cache hit for key: {key}")
        return result
    
    def set(self, key: str, value: Any) -> None:
        """
        Set value in cache.
        
        Args:
            key: Cache key
            value: Value to cache
        """
        if not self.enabled:
            return
            
        self.cache_data[key] = value
        logger.debug(f"Cached value for key: {key}")
    
    def has(self, key: str) -> bool:
        """
        Check if key exists in cache.
        
        Args:
            key: Cache key
            
        Returns:
            True if key exists
        """
        if not self.enabled:
            return False
        return key in self.cache_data
    
    def delete(self, key: str) -> bool:
        """
        Delete key from cache.
        
        Args:
            key: Cache key
            
        Returns:
            True if key was deleted
        """
        if not self.enabled:
            return False
            
        if key in self.cache_data:
            del self.cache_data[key]
            logger.debug(f"Deleted cache key: {key}")
            return True
        return False
    
    def clear(self) -> None:
        """Clear all cache data."""
        if not self.enabled:
            return
            
        self.cache_data.clear()
        logger.info("Cleared all cache data")
    
    def size(self) -> int:
        """Get cache size."""
        return len(self.cache_data)
    
    def keys(self) -> list:
        """Get all cache keys."""
        return list(self.cache_data.keys())
