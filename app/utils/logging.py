"""Logging utilities for Patent2Reaction."""

import logging
import sys
from typing import Optional
from app.config.settings import get_settings


def setup_logging(
    level: Optional[str] = None,
    format_string: Optional[str] = None,
    filename: Optional[str] = None
) -> None:
    """
    Setup logging configuration.
    
    Args:
        level: Log level (DEBUG, INFO, WARNING, ERROR, CRITICAL)
        format_string: Log format string
        filename: Log file name (if None, logs to console)
    """
    settings = get_settings()
    
    log_level = level or settings.log_level
    log_format = format_string or settings.log_format
    
    # Convert string level to logging constant
    numeric_level = getattr(logging, log_level.upper(), logging.INFO)
    
    # Configure logging
    config = {
        'level': numeric_level,
        'format': log_format,
        'datefmt': "%Y-%m-%d %H:%M:%S"
    }
    if filename:
        config.update({'filename': filename, 'filemode': 'a'})
    else:
        config.update({'stream': sys.stdout})
    
    logging.basicConfig(**config)
    
    # Set specific loggers to appropriate levels
    logging.getLogger("urllib3").setLevel(logging.WARNING)
    logging.getLogger("requests").setLevel(logging.WARNING)
    logging.getLogger("httpx").setLevel(logging.WARNING)
    
    logger = logging.getLogger(__name__)
    logger.info(f"Logging configured with level: {log_level}")


def get_logger(name: str, debug_mode: bool = False) -> logging.Logger:
    """
    Get a logger instance.

    Args:
        name: Logger name (usually __name__)
        debug_mode: Enable debug logging for this logger

    Returns:
        Logger instance
    """
    # Ensure logging is configured
    if not logging.getLogger().handlers:
        setup_logging()
        
    logger = logging.getLogger(name)

    # Set debug level if requested
    if debug_mode and logger.getEffectiveLevel() > logging.DEBUG:
        logger.setLevel(logging.DEBUG)

    return logger


class LoggerMixin:
    """Mixin class to add logging capabilities."""
    
    @property
    def logger(self) -> logging.Logger:
        """Get logger for this class."""
        return get_logger(self.__class__.__module__ + "." + self.__class__.__name__)
