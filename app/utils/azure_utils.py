import json

from azure.storage.blob import BlobServiceClient


class AzureUtils:
    """
    Azure utility class for handling chemical reactions and rendering images. and store it in Azure Blob Storage.
    This class provides methods to load chemical reactions, render them as images, and upload these images to Azure Blob Storage.
    Attributes:
    ----------
    indigo : Indigo
        An instance of the Indigo library for chemical informatics.
    renderer : IndigoRenderer
        An instance of the IndigoRenderer for rendering chemical structures.
   
    """
    def __init__(self, app_config):
        """
        Initializes the AzureUtils class by creating instances of Indigo and IndigoRenderer.
        """
        self.app_config = app_config
        self.connection_string = app_config.AZURE_BLOB_CONNECTION_STRING
        self.container_name = app_config.AZURE_BLOB_CONTAINER_NAME


    def upload_to_azure_blob(self, blob_name: str, data, content_type: str = 'application/json') -> str:
        """
        Uploads content to Azure Blob Storage.

        Parameters:
        ----------
        blob_name : str
            Blob path including any folder structure (e.g. 'folder/file.json')
        data : str | bytes | dict | pd.DataFrame
            The content to upload. Can be a dict (for JSON), string (HTML), or DataFrame (for Parquet).
        content_type : str
            MIME type of the content.

        Returns:
        -------
        str
            The URL of the uploaded blob.
        """
        if isinstance(data, dict):
            upload_data = json.dumps(data).encode('utf-8')
        elif isinstance(data, str):
            upload_data = data.encode('utf-8')
        elif isinstance(data, bytes):
            upload_data = data
        else:
            raise TypeError("Unsupported data type for upload.")

        blob_service_client = BlobServiceClient.from_connection_string(self.connection_string)
        container_client = blob_service_client.get_container_client(self.container_name)
        blob_client = container_client.get_blob_client(blob_name)

        blob_client.upload_blob(upload_data, overwrite=True, content_type=content_type)

        blob_url = f"https://{blob_service_client.account_name}.blob.core.windows.net/{self.container_name}/{blob_name}"
        return blob_url


    def get_data_from_azure_blob(self, blob_name: str):
        """
        Retrieves a JSON blob stored under a folder named after the patent_id from Azure Blob Storage.

        Parameters:
        ----------
        patent_id : str
            The folder (prefix) used when the blob was uploaded.

        Returns:
        -------
        dict
            The dictionary stored in the blob.
        """
        
        blob_service_client = BlobServiceClient.from_connection_string(self.connection_string)
        blob_client = blob_service_client.get_container_client(self.container_name).get_blob_client(blob_name)

        stream = blob_client.download_blob()
        data = stream.readall()
        return data
    
    def create_blob_container(self):
        """
        Creates a blob container in Azure Blob Storage.

        Returns:
        -------
        ContainerClient
            The client for the created container.
        """
        blob_service_client = BlobServiceClient.from_connection_string(self.connection_string)
        try:
            container_client = blob_service_client.create_container(self.container_name)
        except Exception as e:
            print(f"Container already exists: {e}")
        return container_client


