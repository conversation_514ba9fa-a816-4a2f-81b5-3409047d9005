import os
import uuid
from elasticsearch import Elasticsearch, NotFoundError
from azure.search.documents import SearchClient as AzureClient
from azure.core.credentials import AzureKeyCredential
from celery.utils.log import get_task_logger

logger = get_task_logger(__name__)


class BaseSearchClient:
    def index_exists(self, index_name: str) -> bool:
        raise NotImplementedError

    def create_index(self, index_name: str, settings: dict, mappings: dict) -> bool:
        raise NotImplementedError

    def insert_document(self, index_name: str, document: dict) -> str:
        raise NotImplementedError

    def search_documents(self, index_name: str, query: dict) -> list:
        raise NotImplementedError

    def get_document(self, index_name: str, doc_id: str) -> dict:
        raise NotImplementedError

    def delete_document(self, index_name: str, doc_id: str) -> bool:
        raise NotImplementedError

    def update_document(self, index_name: str, doc_id: str, update_fields: dict) -> bool:
        raise NotImplementedError

class ElasticsearchSearchClient(BaseSearchClient):
    def __init__(self, host: str, port: int, username: str, password: str):
        self.client = Elasticsearch(
            f"http://{host}:{port}",
            basic_auth=(username, password),
            verify_certs=False
        )
        if not self.client.ping():
            raise ConnectionError("Elasticsearch cluster is not available")
        logger.info("Connected to Elasticsearch")

    def index_exists(self, index_name: str) -> bool:
        try:
            return self.client.indices.exists(index=index_name)
        except NotFoundError:
            return False

    def create_index(self, index_name: str, settings: dict, mappings: dict) -> bool:
        if self.index_exists(index_name):
            return False
        self.client.indices.create(index=index_name, body={'settings': settings, 'mappings': mappings})
        return True

    def insert_document(self, index_name: str, document: dict) -> str:
        response = self.client.index(index=index_name, body=document)
        return response['_id']

    def search_documents(self, index_name: str, query: dict) -> list:
        response = self.client.search(index=index_name, body=query)
        return response['hits']['hits']

    def get_document(self, index_name: str, doc_id: str) -> dict:
        try:
            return self.client.get(index=index_name, id=doc_id)['_source']
        except NotFoundError:
            return None

    def delete_document(self, index_name: str, doc_id: str) -> bool:
        try:
            self.client.delete(index=index_name, id=doc_id)
            return True
        except NotFoundError:
            return False

    def update_document(self, index_name: str, doc_id: str, update_fields: dict) -> bool:
        try:
            self.client.update(index=index_name, id=doc_id, body={'doc': update_fields})
            return True
        except NotFoundError:
            return False








class AzureAISearchClient(BaseSearchClient):
    def __init__(self, endpoint: str, index_name: str, api_key: str):
        self.index_name = index_name
        self.client = AzureClient(endpoint=endpoint, index_name=index_name, credential=AzureKeyCredential(api_key))

    def index_exists(self, index_name: str) -> bool:
        # Azure doesn’t directly support checking index existence via this client.
        # Would typically require using Azure Search IndexClient (not documents client).
        raise NotImplementedError("Use IndexClient to check index existence")

    def create_index(self, index_name: str, settings: dict, mappings: dict) -> bool:
        raise NotImplementedError("Requires IndexClient setup")

    def insert_document(self, index_name: str =  None, document: dict = {}) -> str:
        if 'id' not in document:
            document['id'] = str(uuid.uuid4())
        result = self.client.upload_documents(documents=[document])
        return result[0].key  # Assuming document has a key field

    def search_documents(self, index_name: str = None, query: dict = {}) -> list:
        if 'patent_id' in query:
            # Search by ID using filter
            results = self.client.search(
                search_text="*",
                filter=f"patent_id eq '{query['patent_id']}'"
            )
        elif 'key' in query:
            # Search by any key-value pair
            key = query['key']
            value = query.get('value', '')
            results = self.client.search(
                search_text="*",
                filter=f"{key} eq '{value}'"
            )
        
        return [doc for doc in results]

    def get_document(self, index_name: str, doc_id: str) -> dict:
        return self.client.get_document(key=doc_id)

    def delete_document(self, index_name: str, doc_id: str) -> bool:
        self.client.delete_documents(documents=[{"id": doc_id}])
        return True

    def update_document(self, index_name: str, doc_id: str, update_fields: dict) -> bool:
        update_fields['id'] = doc_id
        self.client.merge_documents(documents=[update_fields])
        return True


def get_search_client(config: dict) -> BaseSearchClient:
    provider = config.get("provider", "azure").lower()
    
    if provider == "elasticsearch":
        return ElasticsearchSearchClient(
            host=config["host"],
            port=config["port"],
            username=config["username"],
            password=config["password"]
        )
    elif provider == "azure":
        return AzureAISearchClient(
            endpoint=config["endpoint"],
            index_name=config["index_name"],
            api_key=config["api_key"]
        )
    else:
        raise ValueError("Unsupported search provider")
