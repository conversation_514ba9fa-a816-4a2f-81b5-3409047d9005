"""Token management utilities for optimizing Gemini API usage."""

import time
import threading
from typing import Dict, List, Tuple, Optional
from google import genai
from ..utils.logging import get_logger
from ..prompts.patent_preprocessing import get_preprocessing_prompt
from app.config.settings import get_settings


class TokenManager:
    """Manages token counting and batching for optimal API usage."""
    
    def __init__(self):
        """Initialize token manager.
        
        """
        self.logger = get_logger(__name__)
        self.settings = get_settings()
        
        # Token limits
        self.max_tokens_per_minute = 1_000_000
        self.safety_margin = 0.95  # Use 95% of limit to be safe
        self.effective_limit = int(self.max_tokens_per_minute * self.safety_margin)
        
        # Rate limiting
        self.current_minute_start = time.time()
        self.current_minute_tokens = 0
        self.lock = threading.Lock()
        
        # Initialize Gemini client for token counting
        self._init_gemini_client()
    
    def _init_gemini_client(self):
        """Initialize Gemini client for token counting."""
        try:
            if not self.settings.GEMINI_API_KEY:
                raise ValueError("Gemini API key is required")
            
            self.gemini_client = genai.Client(api_key=self.settings.GEMINI_API_KEY)
            self.gemini_model = "gemini-2.5-flash-preview-05-20"
            
            self.logger.info(f"Initialized Gemini client for token counting with model: {self.gemini_model}")
            
        except Exception as e:
            error_msg = f"Failed to initialize Gemini client: {str(e)}"
            self.logger.error(error_msg)
            raise ValueError(error_msg)
    
    def count_tokens(self, text: str) -> int:
        """Count tokens in a text string.
        
        Args:
            text: Text to count tokens for
            
        Returns:
            Number of tokens
        """
        try:
            total_tokens = self.gemini_client.models.count_tokens(
                model=self.gemini_model, 
                contents=text
            )
            return total_tokens.total_tokens
        except Exception as e:
            self.logger.warning(f"Failed to count tokens, using estimation: {str(e)}")
            # Fallback estimation: roughly 4 characters per token
            return len(text) // 4
    
    def estimate_preprocessing_tokens(
        self, 
        chemical_name: str, 
        smiles: str, 
        patent_details: Dict
    ) -> int:
        """Estimate tokens needed for preprocessing a patent.
        
        Args:
            chemical_name: Chemical name
            smiles: SMILES string
            patent_details: Patent details dict
            
        Returns:
            Estimated token count
        """
        try:
            # Build the full prompt as it would be sent
            prompt = get_preprocessing_prompt().format(
                chemical_name=chemical_name,
                smiles=smiles,
                abstract=patent_details.get('abstract', 'Not available'),
                description=patent_details.get('description', 'Not available'),
                claims=patent_details.get('claims', 'Not available')
            )
            
            return self.count_tokens(prompt)
            
        except Exception as e:
            self.logger.warning(f"Failed to estimate tokens: {str(e)}")
            # Conservative fallback estimation
            base_prompt_tokens = 1000  # Estimated base prompt size
            content_tokens = sum([
                len(patent_details.get('abstract', '')) // 4,
                len(patent_details.get('description', '')) // 4,
                len(patent_details.get('claims', '')) // 4
            ])
            return base_prompt_tokens + content_tokens
    
    def create_token_optimized_batches(
        self, 
        patent_data: List[Tuple[str, str, str, Dict]]
    ) -> List[List[Tuple[str, str, str, Dict]]]:
        """Create batches of patents optimized for token usage.
        
        Args:
            patent_data: List of (patent_id, chemical_name, smiles, patent_details) tuples
            
        Returns:
            List of batches, each batch is a list of patent data tuples
        """
        batches = []
        current_batch = []
        current_batch_tokens = 0
        
        self.logger.info(f"Creating token-optimized batches for {len(patent_data)} patents")
        self.logger.info(f"Target tokens per batch: {self.effective_limit:,}")
        
        for i, (patent_id, chemical_name, smiles, patent_details) in enumerate(patent_data):
            # Estimate tokens for this patent
            estimated_tokens = self.estimate_preprocessing_tokens(
                chemical_name, smiles, patent_details
            )
            
            self.logger.debug(f"Patent {patent_id}: estimated {estimated_tokens:,} tokens")
            
            # Check if adding this patent would exceed the limit
            if current_batch and (current_batch_tokens + estimated_tokens) > self.effective_limit:
                # Start a new batch
                batches.append(current_batch)
                self.logger.info(
                    f"Batch {len(batches)} created with {len(current_batch)} patents "
                    f"({current_batch_tokens:,} tokens)"
                )
                current_batch = []
                current_batch_tokens = 0
            
            # Add patent to current batch
            current_batch.append((patent_id, chemical_name, smiles, patent_details))
            current_batch_tokens += estimated_tokens
            
            # Progress logging
            if (i + 1) % 10 == 0:
                self.logger.info(f"Processed {i + 1}/{len(patent_data)} patents for batching")
        
        # Add the last batch if it has patents
        if current_batch:
            batches.append(current_batch)
            self.logger.info(
                f"Final batch {len(batches)} created with {len(current_batch)} patents "
                f"({current_batch_tokens:,} tokens)"
            )
        
        self.logger.info(f"Created {len(batches)} token-optimized batches")
        return batches
    
    def wait_for_rate_limit_reset(self):
        """Wait if necessary to respect rate limits."""
        with self.lock:
            current_time = time.time()
            time_since_minute_start = current_time - self.current_minute_start
            
            # If we're in a new minute, reset counters
            if time_since_minute_start >= 60:
                self.current_minute_start = current_time
                self.current_minute_tokens = 0
                self.logger.debug("Rate limit window reset")
                return
            
            # If we're approaching the limit, wait for the next minute
            if self.current_minute_tokens >= self.effective_limit:
                wait_time = 60 - time_since_minute_start + 1  # +1 for safety
                self.logger.info(
                    f"Rate limit reached ({self.current_minute_tokens:,} tokens). "
                    f"Waiting {wait_time:.1f} seconds for reset..."
                )
                time.sleep(wait_time)
                self.current_minute_start = time.time()
                self.current_minute_tokens = 0
    
    def record_token_usage(self, tokens_used: int):
        """Record token usage for rate limiting.
        
        Args:
            tokens_used: Number of tokens used in the request
        """
        with self.lock:
            current_time = time.time()
            time_since_minute_start = current_time - self.current_minute_start
            
            # If we're in a new minute, reset counters
            if time_since_minute_start >= 60:
                self.current_minute_start = current_time
                self.current_minute_tokens = 0
            
            self.current_minute_tokens += tokens_used
            self.logger.debug(
                f"Recorded {tokens_used:,} tokens. "
                f"Total this minute: {self.current_minute_tokens:,}/{self.effective_limit:,}"
            )
    
    def get_current_usage(self) -> Dict[str, int]:
        """Get current token usage statistics.
        
        Returns:
            Dictionary with usage statistics
        """
        with self.lock:
            current_time = time.time()
            time_since_minute_start = current_time - self.current_minute_start
            
            # If we're in a new minute, reset counters
            if time_since_minute_start >= 60:
                self.current_minute_start = current_time
                self.current_minute_tokens = 0
                time_since_minute_start = 0
            
            return {
                "current_minute_tokens": self.current_minute_tokens,
                "effective_limit": self.effective_limit,
                "time_since_minute_start": time_since_minute_start,
                "remaining_tokens": max(0, self.effective_limit - self.current_minute_tokens),
                "remaining_time_in_minute": max(0, 60 - time_since_minute_start)
            }
